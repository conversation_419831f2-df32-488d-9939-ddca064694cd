package com.gilead.testscripts.GileadCommercial_Smoke;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "GileadHIV_Smoke_Tests", testlab = "${testlab}", testset = "${testset}")

public class gileadhiv extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyAboutUsPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 3)
	public void verifyEventsPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 4)
	public void verifyHIVTestingPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 5)
	public void verifyHomePage() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 6)
	public void verifyHivCareContinuumPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}
}
