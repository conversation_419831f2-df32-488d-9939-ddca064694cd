package com.gilead.utils;

import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;

import org.apache.commons.codec.binary.Base64;

public class PasswordEncrypter {

	static Cipher ecipher;

	static Cipher dcipher;

	static byte[] salt = new byte[] { -87, -101, -56, 50, 86, 53, -29, 3 };

	static int iterationCount = 19;
	
	/**
	 * function to encrypt given string
	 * @param paramString2
	 * @return encrypted string
	 * @throws NoSuchAlgorithmException
	 * @throws InvalidKeySpecException
	 * @throws NoSuchPaddingException
	 * @throws InvalidKeyException
	 * @throws InvalidAlgorithmParameterException
	 * @throws UnsupportedEncodingException
	 * @throws IllegalBlockSizeException
	 * @throws BadPaddingException
	 */
	
	public static String encrypt(String paramString2) throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, UnsupportedEncodingException, IllegalBlockSizeException, BadPaddingException {
		String str1 = "dqi014atnqpeh1940auzm9";
		PBEKeySpec pBEKeySpec = new PBEKeySpec(str1.toCharArray(), salt, iterationCount);
	    SecretKey secretKey = SecretKeyFactory.getInstance("PBEWithMD5AndDES").generateSecret(pBEKeySpec);
	    PBEParameterSpec pBEParameterSpec = new PBEParameterSpec(salt, iterationCount);
	    ecipher = Cipher.getInstance(secretKey.getAlgorithm());
	    ecipher.init(1, secretKey, pBEParameterSpec);
	    String str = "UTF-8";
	    byte[] arrayOfByte1 = paramString2.getBytes(str);
	    byte[] arrayOfByte2 = ecipher.doFinal(arrayOfByte1);
	    return (new Base64()).encodeToString(arrayOfByte2);
	  }

}
