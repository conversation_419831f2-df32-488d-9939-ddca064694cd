<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.LetsChatCart.Navigation_And_Funtionality_Verification_For_LetsChatCart_Website" tests="2" failures="1" timestamp="2025-05-12T18:47:33 IST" time="41.524" errors="0">
  <testcase name="invokeURL" time="9.155" classname="com.gilead.testscripts.LetsChatCart.Navigation_And_Funtionality_Verification_For_LetsChatCart_Website"/>
  <system-out/>
  <testcase name="letsChatartNavigationAndFunctionalityVerification" time="32.369" classname="com.gilead.testscripts.LetsChatCart.Navigation_And_Funtionality_Verification_For_LetsChatCart_Website">
    <failure type="com.gilead.config.FrameworkAssertion" message="Error - Who is CAR T for? Link is not found in the page: &quot;https://www.letschatcart.com/what-is-car-t-therapy&quot; even after waiting for 10 Seconds">
      <![CDATA[com.gilead.config.FrameworkAssertion: Error - Who is CAR T for? Link is not found in the page: "https://www.letschatcart.com/what-is-car-t-therapy" even after waiting for 10 Seconds
at com.gilead.reports.Report.updateTestLog(Report.java:423)
at com.gilead.maintenance.ALMFunctions.ThrowException(ALMFunctions.java:252)
at com.gilead.maintenance.WebDriverUtil.waitUntilElementLocated(WebDriverUtil.java:304)
at com.gilead.maintenance.CommonActionsAndFunctions.objectExists(CommonActionsAndFunctions.java:959)
at businesscomponents.CommonFunctions.clickAndVerifyLink(CommonFunctions.java:5438)
at businesscomponents.CommonFunctions.letsChatCartNavigationAndFunctionality(CommonFunctions.java:5686)
at com.gilead.testscripts.LetsChatCart.Navigation_And_Funtionality_Verification_For_LetsChatCart_Website.letsChatartNavigationAndFunctionalityVerification(Navigation_And_Funtionality_Verification_For_LetsChatCart_Website.java:29)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.ArrayList.forEach(ArrayList.java:1259)
at org.testng.TestRunner.privateRun(TestRunner.java:794)
at org.testng.TestRunner.run(TestRunner.java:596)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:377)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:371)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:332)
at org.testng.SuiteRunner.run(SuiteRunner.java:276)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:53)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:96)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1212)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1134)
at org.testng.TestNG.runSuites(TestNG.java:1063)
at org.testng.TestNG.run(TestNG.java:1031)
at org.testng.remote.AbstractRemoteTestNG.run(AbstractRemoteTestNG.java:115)
at org.testng.remote.RemoteTestNG.initAndRun(RemoteTestNG.java:251)
at org.testng.remote.RemoteTestNG.main(RemoteTestNG.java:77)
]]>
    </failure>
  </testcase> <!-- letsChatartNavigationAndFunctionalityVerification -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.LetsChatCart.Navigation_And_Funtionality_Verification_For_LetsChatCart_Website -->
