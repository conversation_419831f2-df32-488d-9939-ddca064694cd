package com.gilead.testscripts.TalkPrep;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class Validate_TalkPrep_Resources_page extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 2)
	public void logOnResourcesPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenu();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 3)
	public void verifyStayInformedPage() throws Exception {
		try {
			objCommonFunctions.validateComponentExists();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 4)
	public void verifyFAQsPage() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPage();
		} finally {
			checkErrors();
		}
	}
}
