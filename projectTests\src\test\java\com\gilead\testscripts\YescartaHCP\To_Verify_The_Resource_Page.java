package com.gilead.testscripts.YescartaHCP;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class To_Verify_The_Resource_Page extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 2)
	public void PDFverification() throws Exception {
		try {
			objCommonFunctions.verifyMenuLinks();
			objCommonFunctions.checkPdfFileDownload();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 3)
	public void toVerifyResourcePage() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPage();
		} finally {
			checkErrors();
		}
	}	
}
