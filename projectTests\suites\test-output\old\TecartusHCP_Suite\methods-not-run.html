<h2>Methods that were not run</h2><table>
<tr><td>com.gilead.base.BaseTest.afterSuite</td></tr>
<tr><td>com.gilead.base.BaseTest.afterSuite</td></tr>
<tr><td>com.gilead.base.BaseTest.afterSuite</td></tr>
<tr><td>com.gilead.base.BaseTest.afterSuite</td></tr>
<tr><td>com.gilead.base.BaseTest.afterSuite</td></tr>
<tr><td>com.gilead.base.BaseTest.afterSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.tearDownTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.tearDownTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.tearDownTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.tearDownTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.tearDownTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.tearDownTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.setUpTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.setUpTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.setUpTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.setUpTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.setUpTestSuite</td></tr>
<tr><td>com.gilead.maintenance.CRAFTLiteTestCase.setUpTestSuite</td></tr>
</table>