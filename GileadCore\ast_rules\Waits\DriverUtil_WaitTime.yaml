id: NoHardcodeWait_waitUntilWindowCountAvailable
message: "Don't use hardcoded wait time for waitUntilWindowCountAvailable"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilWindowCountAvailable($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 3
      kind: decimal_integer_literal  
---
id: NoHardcodeWait_waitUntilPageLoaded
message: "Don't use hardcoded wait time for waitUntilPageLoaded"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilPageLoaded($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal  
---
id: NoHardcodeWait_waitUntilPageReadyStateComplete
message: "Don't use hardcoded wait time for waitUntilPageReadyStateComplete"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilPageReadyStateComplete($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal
---
id: NoHardcodeWait_isAlertPresent
message: "Don't use hardcoded wait time for isAlertPresent"
language: java
severity: warning
rule:
  pattern: driverUtil.isAlertPresent($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal  
---
id: NoHardcodeWait_waituntilTextPresentInPageTitle
message: "Don't use hardcoded wait time for waituntilTextPresentInPageTitle"
language: java
severity: warning
rule:
  pattern: driverUtil.waituntilTextPresentInPageTitle($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal  
---
id: NoHardcodeWait_waituntilContainsPresentInPageTitle
message: "Don't use hardcoded wait time for waituntilContainsPresentInPageTitle"
language: java
severity: warning
rule:
  pattern: driverUtil.waituntilContainsPresentInPageTitle($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal  
---
id: NoHardcodeWait_waitUntilAjaxLoadingComplete
message: "Don't use hardcoded wait time for waitUntilAjaxLoadingComplete"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilAjaxLoadingComplete($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal 
---
id: NoHardcodeWait_waitUntilElementLocated
message: "Don't use hardcoded wait time for waitUntilElementLocated"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilElementLocated($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal  
---
id: NoHardcodeWait_waitUntilElementVisible
message: "Don't use hardcoded wait time for waitUntilElementVisible"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilElementVisible($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal 
---
id: NoHardcodeWait_waitUntilElementEnabled
message: "Don't use hardcoded wait time for waitUntilElementEnabled"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilElementEnabled($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_waitUntilElementDisabled
message: "Don't use hardcoded wait time for waitUntilElementDisabled"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilElementDisabled($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal  
---
id: NoHardcodeWait_waituntilTextPresentInElement
message: "Don't use hardcoded wait time for waituntilTextPresentInElement"
language: java
severity: warning
rule:
  pattern: driverUtil.waituntilTextPresentInElement($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal 
---
id: NoHardcodeWait_waitUntilFrameAvailableAndSwitch
message: "Don't use hardcoded wait time for waitUntilFrameAvailableAndSwitch"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilFrameAvailableAndSwitch($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_waitUntilStalenessOfElement
message: "Don't use hardcoded wait time for waitUntilStalenessOfElement"
language: java
severity: warning
rule:
  pattern: driverUtil.waitUntilStalenessOfElement($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal 
---
id: NoHardcodeWait_WaitUntilElementAtributeContainsText
message: "Don't use hardcoded wait time for WaitUntilElementAtributeContainsText"
language: java
severity: warning
rule:
  pattern: driverUtil.WaitUntilElementAtributeContainsText($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal 
---
id: NoHardcodeWait_WaitUntilElementAtributeNotContainsText
message: "Don't use hardcoded wait time for WaitUntilElementAtributeNotContainsText"
language: java
severity: warning
rule:
  pattern: driverUtil.WaitUntilElementAtributeNotContainsText($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal  
---
id: NoHardcodeWait_WaitUntilElementAtributeText
message: "Don't use hardcoded wait time for WaitUntilElementAtributeText"
language: java
severity: warning
rule:
  pattern: driverUtil.WaitUntilElementAtributeText($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal