<html><head><title>testng.xml for REMS_Suite</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;guice-stage="DEVELOPMENT"&nbsp;name="REMS_Suite"&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="3"&nbsp;name="Test&nbsp;under&nbsp;EntireSuite_Suite1"&nbsp;parallel="classes"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;parameter&nbsp;name="RunID"&nbsp;value="0"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;Test&nbsp;under&nbsp;EntireSuite_Suite1&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;REMS_Suite&nbsp;--&gt;
<br/></tt></body></html>