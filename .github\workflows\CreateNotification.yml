name: Create Notification

on:
  repository_dispatch:

permissions:
  issues: write
  
jobs:
  notify:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout main repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 1
        token: ${{ secrets.GIT_TOKEN }}
        submodules: recursive
        
    - name: Retrive User Details
      run: |
          sudo apt-get update
          sudo apt-get install -y jq
          user_name=$(curl -s -H "Authorization: token ${{ secrets.GIT_TOKEN }}" https://api.github.com/user)
          echo "user_login=$(echo $user_name | jq -r '.login')" >> $GITHUB_ENV
          user_email=$(curl -s -H "Authorization: token ${{ secrets.GIT_TOKEN }}" https://api.github.com/user/emails)
          echo "user_login_email=$(echo $user_email | jq -r '.[0].email')" >> $GITHUB_ENV
          
    - name: Configure git
      run: |
        git config --global user.name "${{ env.user_login }}"
        git config --global user.email "${{ env.user_email }}"
        
    - name: Get Latest Commit Details of Submodule
      run: |
          cd GileadCore
          git checkout main
          git pull origin main
          echo "COMMIT_ID=$(git rev-parse HEAD)" >> $GITHUB_ENV
          echo "COMMIT_MESSAGE=$(git log -1 --pretty=format:%s)" >> $GITHUB_ENV
          
    - name: Create Notification Issue
      uses: actions/github-script@v6
      with:
        script: |
          const commit_id = process.env.COMMIT_ID;
          const commit_message = process.env.COMMIT_MESSAGE;
          await github.rest.issues.create({
          owner: context.repo.owner,
          repo: context.repo.repo,
          title: "Submodule Update Notification",
          body: "Submodule update has been detected with latest **Commit_ID:** " + commit_id +" **Commit_Message:** "+ commit_message +"",
          labels:["Notification"]
          });
