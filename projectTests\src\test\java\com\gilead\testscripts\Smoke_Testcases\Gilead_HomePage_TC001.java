package com.gilead.testscripts.Smoke_Testcases;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;


public class Gilead_HomePage_TC001 extends BaseTest {

	CommonFunctions objCommonFunctions;


	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
//			objCommonFunctions.zoomOutBrowser();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyHeader() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("Component");					
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 3)
	public void verifyFooter() throws Exception {
		try {		
			objCommonFunctions.verifyComponentExists("Component");			
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 4)
	public void verifyMenuComponents() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("Component");			
		} finally {
			checkErrors();
		}
	}
}
