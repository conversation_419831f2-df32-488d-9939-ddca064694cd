package com.gilead.testscripts.Regression_TC;

import org.testng.annotations.AfterClass;
import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class TC004_Trodelvy_SignUp_FormFill extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);//
			objCommonFunctions.invokeUrl();
			objCommonFunctions.closeCookies();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void formFill() throws Exception {
		try {
			//objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.reqFormFill("reqFormFill","SignUpForm");
			
		} finally {
			checkErrors();
		}
	}
	@Ignore
	@Test(priority = 3)
	
	public void verifySuccessMessage() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.verifyMessage("Thank you for contacting us!",driver.getTitle());
			
		} finally {
			checkErrors();
		}
	}
	@AfterClass
	public void resetHashMap() {
		CommonFunctions.resetHashMap();
	}

}
