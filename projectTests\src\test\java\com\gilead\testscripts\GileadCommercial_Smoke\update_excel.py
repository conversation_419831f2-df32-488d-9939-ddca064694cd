#!/usr/bin/env python3
"""
Excel Update Script for GileadCommercial_Smoke Test Case Consolidation
=====================================================================

This script updates the Excel files to support the consolidated test case structure.
It replaces XPath values with label names and updates file references.

Requirements:
- pip install openpyxl pandas

Usage:
python update_excel.py

Files to be updated:
1. Automated Scripts_SmokeTestcases.xlsx
2. Run Manager.xlsx (if needed)
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import os
import sys
from datetime import datetime

# Configuration
EXCEL_FILES = {
    'smoke_testcases': 'projectTests/src/test/resources/Datatables/Automated Scripts_SmokeTestcases.xlsx',
    'run_manager': 'projectTests/src/test/resources/Datatables/Run Manager.xlsx'
}

# Mapping of XPaths to Label Names based on analysis
XPATH_TO_LABEL_MAPPING = {
    # GileadHIV mappings
    "//a[contains(text(),'About Us')]": "AboutUs",
    "//a[contains(text(),'Events')]": "Events", 
    "//a[contains(text(),'HIV Testing')]": "HIVTesting",
    "//a[contains(text(),'HIV Care Continuum')]": "HIVCareContinuum",
    "//div[contains(@class,'home-page')]": "HomePage",
    
    # P2PTalkPrep mappings
    "//a[contains(text(),'Peer Insights')]": "PeerInsights",
    "//div[contains(@class,'peer-insights')]": "VerifyComponentPresent",
    
    # TalkPrep mappings
    "//a[contains(text(),'About Prep')]": "AboutPrep",
    "//a[contains(text(),'Prescribing PreP')]": "PrescribingPrep",
    "//a[contains(text(),'Resources')]": "Resources",
    "//a[contains(text(),'Stay Informed')]": "StayInformed",
    "//a[contains(text(),'Who is prep for')]": "WhoIsPrepFor",
    "//div[contains(@class,'register-link')]": "VerifyComponentPresent",
    "//a[contains(@href,'.pdf')]": "PDFDownload",
    
    # UKHCV mappings
    "//a[contains(text(),'Gilead And Elimination')]": "GileadAndElimination",
    "//a[contains(text(),'Be Free of Hep C')]": "BeFreeOfHepC",
    "//a[contains(text(),'Elimination Partners')]": "EliminationPartners",
    "//a[contains(text(),'What Is Hep C')]": "WhatIsHepC",
    "//div[contains(@class,'hep-c-ki')]": "HepCKi",
    "//div[contains(@class,'elimination-resources')]": "EliminationResources",
    "//div[contains(@class,'component-exists')]": "VerifyComponentPresent"
}

# File name mappings for consolidated classes
FILE_NAME_MAPPING = {
    'Validate_GileadHIV_Home_page': 'gileadhiv',
    'Validate_GileadHIV_About_US': 'gileadhiv',
    'Validate_GileadHIV_Events': 'gileadhiv',
    'Validate_GileadHIV_HIV_Testing': 'gileadhiv',
    'Validate_GileadHIV_Hiv_Care_Continuum': 'gileadhiv',
    
    'Validate_P2P_Talk_Prep_Peer_insights': 'p2ptalkprep',
    
    'Validate_TalkPrep_Home_Page': 'talkprep',
    'Validate_TalkPrep_About_Prep_page': 'talkprep',
    'Validate_TalkPrep_Prescribing_PreP': 'talkprep',
    'Validate_TalkPrep_Resources_page': 'talkprep',
    'Validate_TalkPrep_Stay_Informed_Page': 'talkprep',
    'Validate_TalkPrep_Who_is_prep_for_page': 'talkprep',
    
    'Validate_UKHCV_HomePage': 'ukhcv',
    'Validate_UKHCV_Gilead_And_Elimination': 'ukhcv',
    'Validate_UKHCV_Be_Free_of_Hep_C': 'ukhcv',
    'Validate_UKHCV_Elimination_Partner': 'ukhcv',
    'Validate_UKHCV_What_Is_Hep_C': 'ukhcv',
    'Validate_UKHCV_HepC_Ki': 'ukhcv',
    'Validate_UKHCV_Elimination_Resources': 'ukhcv'
}

def backup_file(file_path):
    """Create a backup of the original file"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(file_path, backup_path)
        print(f"Backup created: {backup_path}")
        return backup_path
    return None

def update_smoke_testcases_excel():
    """Update the Automated Scripts_SmokeTestcases.xlsx file"""
    file_path = EXCEL_FILES['smoke_testcases']
    
    if not os.path.exists(file_path):
        print(f"ERROR: File not found: {file_path}")
        return False
    
    # Create backup
    backup_path = backup_file(file_path)
    
    try:
        # Load the workbook
        workbook = load_workbook(file_path)
        
        # Process each sheet
        for sheet_name in workbook.sheetnames:
            if 'FunctionalRegression' in sheet_name or 'SmokeCICD' in sheet_name:
                sheet = workbook[sheet_name]
                print(f"Processing sheet: {sheet_name}")
                
                # Find header row
                header_row = 1
                headers = {}
                for col in range(1, sheet.max_column + 1):
                    cell_value = sheet.cell(row=header_row, column=col).value
                    if cell_value:
                        headers[cell_value] = col
                
                # Update XPath columns to label names
                xpath_columns = ['SubMenuLocators', 'Component', 'LinkStaticLocator']
                
                for row in range(2, sheet.max_row + 1):
                    tc_id_col = headers.get('TC_ID')
                    if tc_id_col:
                        tc_id = sheet.cell(row=row, column=tc_id_col).value
                        if tc_id and any(project in str(tc_id).lower() for project in ['gileadhiv', 'p2ptalkprep', 'talkprep', 'ukhcv']):
                            
                            # Update XPath columns
                            for col_name in xpath_columns:
                                if col_name in headers:
                                    col_idx = headers[col_name]
                                    cell_value = sheet.cell(row=row, column=col_idx).value
                                    
                                    if cell_value:
                                        # Replace XPaths with label names
                                        updated_value = str(cell_value)
                                        for xpath, label in XPATH_TO_LABEL_MAPPING.items():
                                            if xpath in updated_value:
                                                updated_value = updated_value.replace(xpath, label)
                                        
                                        if updated_value != str(cell_value):
                                            sheet.cell(row=row, column=col_idx).value = updated_value
                                            print(f"Updated {col_name} in row {row}: {cell_value} -> {updated_value}")
                            
                            # Update TC_ID to consolidated class name
                            if tc_id_col and tc_id in FILE_NAME_MAPPING:
                                new_tc_id = FILE_NAME_MAPPING[tc_id]
                                sheet.cell(row=row, column=tc_id_col).value = new_tc_id
                                print(f"Updated TC_ID in row {row}: {tc_id} -> {new_tc_id}")
        
        # Save the updated workbook
        workbook.save(file_path)
        print(f"Successfully updated: {file_path}")
        return True
        
    except Exception as e:
        print(f"ERROR updating {file_path}: {str(e)}")
        if backup_path and os.path.exists(backup_path):
            import shutil
            shutil.copy2(backup_path, file_path)
            print(f"Restored from backup: {backup_path}")
        return False

def update_run_manager_excel():
    """Update the Run Manager.xlsx file"""
    file_path = EXCEL_FILES['run_manager']
    
    if not os.path.exists(file_path):
        print(f"WARNING: File not found: {file_path}")
        return True  # Not critical if this file doesn't exist
    
    # Create backup
    backup_path = backup_file(file_path)
    
    try:
        # Load the workbook
        workbook = load_workbook(file_path)
        
        # Process each sheet
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            print(f"Processing Run Manager sheet: {sheet_name}")
            
            # Find header row
            header_row = 1
            headers = {}
            for col in range(1, sheet.max_column + 1):
                cell_value = sheet.cell(row=header_row, column=col).value
                if cell_value:
                    headers[cell_value] = col
            
            # Update TestCase column
            testcase_col = headers.get('TestCase') or headers.get('Test Case') or headers.get('ClassName')
            
            if testcase_col:
                for row in range(2, sheet.max_row + 1):
                    cell_value = sheet.cell(row=row, column=testcase_col).value
                    
                    if cell_value and str(cell_value) in FILE_NAME_MAPPING:
                        new_value = FILE_NAME_MAPPING[str(cell_value)]
                        sheet.cell(row=row, column=testcase_col).value = new_value
                        print(f"Updated TestCase in row {row}: {cell_value} -> {new_value}")
        
        # Save the updated workbook
        workbook.save(file_path)
        print(f"Successfully updated: {file_path}")
        return True
        
    except Exception as e:
        print(f"ERROR updating {file_path}: {str(e)}")
        if backup_path and os.path.exists(backup_path):
            import shutil
            shutil.copy2(backup_path, file_path)
            print(f"Restored from backup: {backup_path}")
        return False

def main():
    """Main execution function"""
    print("=" * 60)
    print("Excel Update Script for GileadCommercial_Smoke Consolidation")
    print("=" * 60)
    print(f"Start time: {datetime.now()}")
    print()
    
    success = True
    
    # Update Smoke Testcases Excel
    print("1. Updating Automated Scripts_SmokeTestcases.xlsx...")
    if not update_smoke_testcases_excel():
        success = False
    print()
    
    # Update Run Manager Excel
    print("2. Updating Run Manager.xlsx...")
    if not update_run_manager_excel():
        success = False
    print()
    
    if success:
        print("✓ All Excel files updated successfully!")
        print("\nNEXT STEPS:")
        print("1. Review the updated Excel files")
        print("2. Test the consolidated Java classes")
        print("3. Verify that label names are correctly resolved by Common.java methods")
        print("4. Run smoke tests to validate functionality")
    else:
        print("✗ Some errors occurred during update. Check the logs above.")
        print("Backup files have been created for safety.")
    
    print(f"\nEnd time: {datetime.now()}")

if __name__ == "__main__":
    main()
