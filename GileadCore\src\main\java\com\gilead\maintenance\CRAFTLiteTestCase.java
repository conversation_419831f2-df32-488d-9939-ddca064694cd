package com.gilead.maintenance;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.testng.IExecutionListener;
import org.testng.ITestContext;
import org.testng.SkipException;
import org.testng.annotations.AfterSuite;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeSuite;

import com.applitools.eyes.BatchInfo;
import com.applitools.eyes.config.Configuration;
import com.applitools.eyes.selenium.StitchMode;
import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.reporter.ExtentHtmlReporter;
import com.aventstack.extentreports.reporter.configuration.ChartLocation;
import com.aventstack.extentreports.reporter.configuration.Theme;
import com.gilead.base.DriverScript;
import com.gilead.base.RunContext;
import com.gilead.base.TestConfigurationInfo;
import com.gilead.reports.APIReusuableLibrary;
import com.gilead.reports.CraftDriver;
import com.gilead.reports.FrameworkParameters;
import com.gilead.reports.ScriptHelper;
import com.gilead.reports.SeleniumReport;
import com.gilead.reports.SeleniumTestParameters;
import com.gilead.reports.TestCaseBean;
import com.gilead.utils.ServiceRegister;
import com.gilead.utils.Util;

/**
 * Abstract base class for all the test cases to be automated
 * 
 * <AUTHOR>
 */
public abstract class CRAFTLiteTestCase implements IExecutionListener {

	protected static ExtentHtmlReporter htmlReporter;
	protected static ExtentReports extentReport;

	/**
	 * The current scenario
	 */
	protected String currentScenario;
	/**
	 * The current test case
	 */
	protected String currentTestcase;

	protected ResultSummaryManager resultSummaryManager = ResultSummaryManager.getInstance();

	/**
	 * The {@link CraftliteDataTable} object (passed from the Driver script)
	 */
	protected CraftDataTable dataTable;
	/**
	 * The {@link SeleniumReport} object (passed from the Driver script)
	 */
	protected SeleniumReport report;
	/**
	 * The {@link CraftDriver} object (passed from the Driver script)
	 */
	protected CraftDriver driver;
	/**
	 * The {@link WebDriverUtil} object (passed from the Driver script)
	 */
	protected WebDriverUtil driverUtil;

	/**
	 * The {@link ScriptHelper} object (required for calling one reusable library
	 * from another)
	 */
	protected ScriptHelper scriptHelper;

	/**
	 * The {@link Properties} object with settings loaded from the framework
	 * properties file
	 */
	protected Properties properties;
	/**
	 * The {@link FrameworkParameters} object
	 */
	protected FrameworkParameters frameworkParameters = FrameworkParameters.getInstance();

	/**
	 * The {@link APIReusuableLibrary} object
	 */
	protected APIReusuableLibrary apiDriver;

	/**
	 * The {@link ExtentTest} object
	 */
	protected ExtentTest extentTest;

	protected ServiceRegister register = ServiceRegister.getInstance();

	/**
	 * The {@link ExtentTest} object Reusable method to set Key & value and can be
	 * used within test case
	 */
	protected Map<String, String> reusableHandle;

	protected TestConfigurationInfo testConfigurationInfo;

	protected int endIteration;

	protected List<Map<String, String>> configurationErrors = new ArrayList<>();
	
	public static Configuration config;
	public static BatchInfo batch;
	
	/**
	 * Function to initialize the various objects that may beed to be used with a
	 * test script
	 * 
	 * @param scriptHelper The {@link ScriptHelper} object
	 */
	public void initialize(ScriptHelper scriptHelper) {
		this.scriptHelper = scriptHelper;
		this.dataTable = scriptHelper.getDataTable();
		this.report = scriptHelper.getReport();
		this.driver = scriptHelper.getcraftDriver();
		this.driverUtil = scriptHelper.getDriverUtil();
		this.apiDriver = scriptHelper.getApiDriver();
		this.extentTest = scriptHelper.getExtentTest();
		this.reusableHandle = scriptHelper.getReusablehandle();

		properties = Settings.getInstance();
		frameworkParameters = FrameworkParameters.getInstance();
	}

	public synchronized void initialize(RunContext runContext) {
		this.scriptHelper = runContext.getScriptHelper();
		this.dataTable = runContext.getScriptHelper().getDataTable();
		this.report = runContext.getScriptHelper().getReport();
		this.driver = runContext.getScriptHelper().getcraftDriver();
		this.driverUtil = runContext.getScriptHelper().getDriverUtil();
		this.apiDriver = runContext.getScriptHelper().getApiDriver();
		this.extentTest = runContext.getScriptHelper().getExtentTest();
		this.reusableHandle = runContext.getScriptHelper().getReusablehandle();

		properties = Settings.getInstance();
		frameworkParameters = FrameworkParameters.getInstance();
	}

	/**
	 * Function to do the required framework setup activities before executing the
	 * overall test suite
	 * 
	 * @param testContext The TestNG {@link ITestContext} of the current test suite
	 */
	@BeforeSuite
	public void setUpTestSuite(ITestContext testContext) {
		System.out.println("CurrentThread is " + Thread.currentThread().getName());
		resultSummaryManager.setRelativePath();
		resultSummaryManager.initializeTestBatch(testContext.getSuite().getName());
		String reportPath = TimeStamp.getInstance();
		// System.setProperty("ReportPath", reportPath);
		register.putService(testContext.getSuite().getName() + "ReportPath", reportPath);
		register.putService(Thread.currentThread().getName() + "CurrentSuite", testContext.getSuite().getName());
		testContext.setAttribute("RSM", resultSummaryManager);
		testContext.setAttribute("ConfigurationErrors", configurationErrors);

		int nThreads;
		if ("false".equalsIgnoreCase(testContext.getSuite().getParallel())) {
			nThreads = 1;
		} else {
			nThreads = testContext.getCurrentXmlTest().getThreadCount();
		}

		// Note: Separate threads may be spawned through usage of DataProvider
		// testContext.getSuite().getXmlSuite().getDataProviderThreadCount();
		// This will be at test case level (multiple instances on same test case
		// in parallel)
		// This level of threading will not be reflected in the summary report

		resultSummaryManager.initializeSummaryReport(nThreads);
		resultSummaryManager.setupErrorLog();
		generateExtentReports();
		
		//Configuring Applitools Settings
				configureApplitoolsDetails();
		
	}

	
	/**
	 * Function to create the configuration details for Applitools 
	 */
	public void configureApplitoolsDetails() {
		
		properties =Settings.getInstance();		
		config = new Configuration();
		config.setApiKey(properties.getProperty("ApiKey"));
		config.setServerUrl(properties.getProperty("ServerUrl"));
		String strBatchName = properties.getProperty("ProjectName")+"_"+properties.getProperty("RunConfiguration");
		batch = new BatchInfo(strBatchName);	
		config.setStitchMode(StitchMode.CSS);
		config.setBatch(batch); 
		config.setHideCaret(true);
	//	config.setIgnoreDisplacements(true);
	}
	
	
	/**
	 * Function to do the required framework setup activities before executing each
	 * test case
	 */
	// @BeforeMethod
	@BeforeClass
	public void setUpTestRunner(ITestContext context) {

		if (frameworkParameters.getStopExecution()) {
			tearDownTestSuite(context);

			// Throwing TestNG SkipException within a configuration method
			// causes all subsequent test methods to be skipped/aborted
			throw new SkipException("Aborting all subsequent tests!");
		}
	}

	/**
	 * Function to handle any pre-requisite steps required before beginning the test
	 * case execution <br>
	 * <u>Note</u>: This function can be left blank if not applicable
	 */
	public abstract void setUp();

	/**
	 * Function to handle the core test steps required as part of the test case
	 */
	public abstract void executeTest();

	/**
	 * Function to handle any clean-up steps required after completing the test case
	 * execution <br>
	 * <u>Note</u>: This function can be left blank if not applicable
	 */
	public abstract void tearDown();

	/**
	 * Function to do the required framework teardown activities after executing
	 * each test case
	 * 
	 * @param testParameters The {@link SeleniumTestParameters} object passed from
	 *                       the test case
	 * @param driverScript   The {@link DriverScript} object passed from the test
	 *                       case
	 */
	protected synchronized void tearDownTestRunner(SeleniumTestParameters testParameters, DriverScript driverScript,
			ITestContext testContext) {
		TestCaseBean testCaseBean = new TestCaseBean();
		String testReportName = driverScript.getReportName();
		String executionTime = driverScript.getExecutionTime();
		String testStatus = driverScript.getTestStatus();
		ResultSummaryManager rsm = (ResultSummaryManager) testContext.getAttribute("RSM");
		rsm.updateResultSummary(testParameters, testReportName, executionTime, testStatus);
		/* DB-Updating reports to database */
		DataBaseOperation dbOperation = new DataBaseOperation();
		dbOperation.initializeTestParameters(testParameters);
		dbOperation.updateMongoDB("Run Manager", testCaseBean, executionTime, testStatus);

//		if ("Failed".equalsIgnoreCase(testStatus)) {
//			Assert.fail(driverScript.getFailureDescription());
//		}
	}

	protected synchronized void tearDownTestRunner(RunContext runContext) {

		TestCaseBean testCaseBean = new TestCaseBean();
		String testReportName = runContext.getDriverScript().getReportName();
		String executionTime = runContext.getDriverScript().getExecutionTime();
		String testStatus = runContext.getDriverScript().getTestStatus();

		resultSummaryManager.updateResultSummary(runContext.getSeleniumTestParameters(), testReportName, executionTime,
				testStatus);
		/* DB-Updating reports to database */
		DataBaseOperation dbOperation = new DataBaseOperation();
		dbOperation.initializeTestParameters(runContext.getSeleniumTestParameters());
		dbOperation.updateMongoDB("Run Manager", testCaseBean, executionTime, testStatus);

//		if ("Failed".equalsIgnoreCase(testStatus)) {
//			Assert.fail(runContext.getDriverScript().getFailureDescription());
//		}
	}

	/**
	 * Function to do the required framework teardown activities after executing the
	 * overall test suite
	 */
	@AfterSuite
	public void tearDownTestSuite(ITestContext context) {
		ResultSummaryManager rsm = (ResultSummaryManager) context.getAttribute("RSM");
		rsm.wrapUp(true);
		extentReport.flush();
		context.removeAttribute("RSM");
		TimeStamp.clearReportPathWithTimeStamp();
		configurationErrors.clear();
		register.remove();
	}

	/**
	 * Function to set Extent Report Path within Framework and initialze extent
	 * objects
	 */
	private void generateExtentReports() {
		Properties properties = Settings.getInstance();
		htmlReporter = new ExtentHtmlReporter(resultSummaryManager.getReportPath() + Util.getFileSeparator()
				+ "Extent Result" + Util.getFileSeparator() + "ExtentReport.html");
		extentReport = new ExtentReports();
		extentReport.attachReporter(htmlReporter);
		extentReport.setSystemInfo("Project Name", properties.getProperty("ProjectName"));
		extentReport.setSystemInfo("Framework", "CRAFT Maven");
		extentReport.setSystemInfo("Framework Version", "3.2");
		extentReport.setSystemInfo("Author", "Cognizant");

		htmlReporter.config().setDocumentTitle("CRAFT Extent Report");
		htmlReporter.config().setReportName("Extent Report for CRAFT");
		htmlReporter.config().setTestViewChartLocation(ChartLocation.TOP);
		htmlReporter.config().setTheme(Theme.STANDARD);
	}
}