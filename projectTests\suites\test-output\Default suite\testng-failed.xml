<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite guice-stage="DEVELOPMENT" name="Failed suite [Default suite]">
  <test thread-count="5" name="Default test(failed)">
    <classes>
      <class name="com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Resources_Site">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="toVerifyParlonsCartTherapyResourcesSite"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
        </methods>
      </class> <!-- com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Resources_Site -->
    </classes>
  </test> <!-- Default test(failed) -->
</suite> <!-- Failed suite [Default suite] -->
