package com.gilead.maintenance;

import java.lang.invoke.MethodHandles;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.gilead.base.RunContext;
import com.gilead.reports.ExecutionMode;
import com.gilead.reports.SeleniumTestParameters;

public class LoadTime {

	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());
	 
	
		
	public static long getLoadTime(String key) {
		
		Properties properties = Settings.getInstance();

		SeleniumTestParameters testParameters = RunContext.getRunContext().getSeleniumTestParameters();
		long defaultTimeOut = Long.parseLong(properties.get("ObjectSyncTimeout").toString());

		if (testParameters.getExecutionMode() == ExecutionMode.GRID) {
			key = "Remote" + key;
			if (properties.containsKey(key)) {
				logger.info(String.format("Fetched timeout key %s for GRID", key));
				return Long.parseLong(properties.get(key).toString());
			} else {
				logger.info(String.format("Timeout key %s not found returning %s for REMOTE", key,"RemoteObjectSyncTimeout"));
				return defaultTimeOut;
			}
		} else if(testParameters.getExecutionMode() == ExecutionMode.LOCAL) {
			if (properties.containsKey(key)) {
				logger.info(String.format("Fetched timeout key %s for LOCAL", key));
				return Long.parseLong(properties.get(key).toString());
			} else {
				logger.info(String.format("Timeout key %s not found returning %s for LOCAL", key,"ObjectSyncTimeout"));
				return defaultTimeOut;
				
			}
		}
		
		return defaultTimeOut;
	}

}
