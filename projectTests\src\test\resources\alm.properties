# property for alm connection user should replace respective value for each below property
host=sjetmappprdg02
port=8080
domain=TEST
project=Gverify_demo
 
#BASIC/OAUTH2
authtype=BASIC
 
username=alm_user02
password=YflA7OwxBOO0Cb86isjrfsfwhlovqdE3
 
clientid=NA
secret=NA
 
# Session idle timeout validation true/false
# based on this flag polling happens for every timeout
# see rest_session_idle_timeout 
rest_session_idle_timeout_validation=true
 
#Session idle timeout in minutes (polling timout <=10 minutes)
rest_session_idle_timeout=10
 
#REST APPLICABLE PROPERTIES
accept=application/xml
 
project_testplan=Subject\\Project\\AutomationScripts
project_testlab=Root\\Project
project_testset=DryRunSuite