package com.gilead.testscripts.DDME;

import org.testng.annotations.AfterClass;
import org.testng.annotations.Test;

import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class HomePage_TC002 extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.invokeUrl();
			objCommonFunctions.closeCookies();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyCrticalCompontes_HomePage() {
		try {

			objCommonFunctions.verifySearchResultCount();
			objCommonFunctions.enterTextInKeywordField();
			objCommonFunctions.verifyAllLinksUnderHeaderSection();
			objCommonFunctions.verifyElementPresent();
			objCommonFunctions.verifyAccordianFunctionality();
		} finally {
			checkErrors();
		}
	}

	@AfterClass
	public void resetHashMap() {
		CommonFunctions.resetHashMap();
	}

}
