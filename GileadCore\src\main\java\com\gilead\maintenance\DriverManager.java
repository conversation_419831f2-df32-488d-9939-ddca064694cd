package com.gilead.maintenance;

import java.util.Properties;

import org.openqa.selenium.WebDriver;

import com.gilead.base.DriverScript;
import com.gilead.base.RunContext;
import com.gilead.config.FrameworkException;
import com.gilead.reports.Browser;
import com.gilead.reports.CraftDriver;
import com.gilead.reports.ExecutionMode;
import com.gilead.reports.SauceLabsDriverFactory;
import com.gilead.reports.SeleniumTestParameters;
import com.gilead.reports.ToolName;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.windows.WindowsDriver;

public class DriverManager {

	private final SeleniumTestParameters testParameters;
	private CraftDriver driver;
	private static Properties properties = Settings.getInstance();
	private Properties mobileProperties;	
	private WebDriver pureWebDriver;
	private RunContext runContext;
	
	/**
	 * Constructor to initialize the component library
	 * @param scriptHelper The {@link ScriptHelper} object passed from the {@link DriverScript}
	 */
	
	public DriverManager(SeleniumTestParameters testParameters) {		
		this.testParameters =testParameters;
	}
	
	public DriverManager(RunContext runContext) {
		this.runContext = runContext;
		this.testParameters = this.runContext.getSeleniumTestParameters();		
	}
		
    public void setCraftDriver(CraftDriver driver) {
		this.driver = driver;
	}
    
    public CraftDriver getDriver() {
    	return driver;
    }
    
    public void initializeWebDriver() {
    	initializeDriver(testParameters.getExecutionMode());  
    };
    
    public void initializeArbitaryDriver(ExecutionMode executionMode) {
    	initializeDriver(executionMode);  ;
    }
    
    public void reinitializePrimaryDriver() {
    	driver.quit();
    	driver.setDriver(pureWebDriver);	
    }
    
    private void initializeDriver(ExecutionMode executionMode) {

		switch (executionMode) {
		case API:
			break;

		case LOCAL:
			WebDriver webDriver = WebDriverFactory.getWebDriver(testParameters.getBrowser());
			if(driver==null) {				
				driver = new CraftDriver(webDriver);
				runContext.setCraftDriver(driver);
				pureWebDriver = webDriver;
			}
			else {
				driver.setDriver(webDriver);
				runContext.setCraftDriver(driver);
			}
			driver.setTestParameters(testParameters);
			if(testParameters.getBrowser().equals(Browser.CHROME)) {
				testParameters.setBrowserVersion(WebDriverFactory.getChromeBrowserVersion(webDriver));
			}
			if(testParameters.getBrowser().equals(Browser.EDGE)) {
				testParameters.setBrowserVersion(WebDriverFactory.getEdgeBrowserVersion(webDriver));
			}
			maximizeWindow();
			break;
			
		case DESKTOP_WINDOWS:
			WindowsDriver winDriver = WebDriverFactory.getWindowsDriver(testParameters.getDesktopAppIdentifier());
			if(driver==null) {
				driver = new CraftDriver(winDriver);
				runContext.setCraftDriver(driver);
			}
			else {
				driver.setDriver(winDriver);
				runContext.setCraftDriver(driver);
			}			
			driver.setTestParameters(testParameters);
			break;
		
		case DESKTOP_MAC:
			AppiumDriver mac2Driver = WebDriverFactory.getMac2Driver(testParameters.getDesktopAppIdentifier());
			if(driver==null) {
				driver = new CraftDriver(mac2Driver);
				runContext.setCraftDriver(driver);
			}
			else {
				driver.setDriver(mac2Driver);
				runContext.setCraftDriver(driver);
			}
			driver.setTestParameters(testParameters);
			break;
			
		case GRID:
			WebDriver remoteGridDriver = WebDriverFactory.getRemoteWebDriver(testParameters.getBrowser(),
					testParameters.getBrowserVersion(), testParameters.getPlatform(),
					properties.getProperty("RemoteUrl"));
			if(driver==null) {
				driver = new CraftDriver(remoteGridDriver);
				runContext.setCraftDriver(driver);
				pureWebDriver = remoteGridDriver;
			}
			else {
				driver.setDriver(remoteGridDriver);
				runContext.setCraftDriver(driver);
			}			
			driver.setTestParameters(testParameters);						
			maximizeWindow();
			break;

		case MOBILE:
//			WebDriver appiumDriver = AppiumDriverFactory.getAppiumDriver(testParameters.getMobileExecutionPlatform(),
//					testParameters.getDeviceName(), testParameters.getMobileOSVersion(),
//					testParameters.shouldInstallApplication(), mobileProperties.getProperty("AppiumURL"));
//			if(driver==null) {
//				driver = new CraftDriver(appiumDriver);
//				pureWebDriver = appiumDriver;
//			}
//			else {
//				driver.setDriver(appiumDriver);
//			}
//			driver.setTestParameters(testParameters);

			break;

		case PERFECTO:
//			if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) {
//				WebDriver appiumPerfectoDriver = PerfectoDriverFactory.getPerfectoAppiumDriver(
//						testParameters.getMobileExecutionPlatform(), testParameters.getDeviceName(),
//						mobileProperties.getProperty("PerfectoHost"));
//				if(driver==null) {
//					driver = new CraftDriver(appiumPerfectoDriver);
//					pureWebDriver = appiumPerfectoDriver;
//				}
//				else {
//					driver.setDriver(appiumPerfectoDriver);
//				}
//				driver.setTestParameters(testParameters);
//
//			} else if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
//				WebDriver remotePerfectoDriver = PerfectoDriverFactory
//						.getPerfectoRemoteWebDriverForDesktop(testParameters);
//				if(driver==null){
//					driver = new CraftDriver(remotePerfectoDriver);
//					pureWebDriver = remotePerfectoDriver;
//				}
//				else {
//					driver.setDriver(remotePerfectoDriver);
//				}
//				driver.setTestParameters(testParameters);
//			}

			break;

		case SAUCELABS:
			if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) {
				AppiumDriver appiumSauceDriver = SauceLabsDriverFactory.getSauceAppiumDriver(
						testParameters.getMobileExecutionPlatform(), testParameters.getDeviceName(),
						mobileProperties.getProperty("SauceHost"), testParameters);
				if(driver==null) {
					driver = new CraftDriver(appiumSauceDriver);
					runContext.setCraftDriver(driver);
					pureWebDriver = appiumSauceDriver;
				}
				else {
					driver.setDriver(appiumSauceDriver);
					runContext.setCraftDriver(driver);
				}
				driver.setTestParameters(testParameters);

			} else if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
				WebDriver remoteSauceDriver = SauceLabsDriverFactory.getSauceRemoteWebDriver(
						mobileProperties.getProperty("SauceHost"), testParameters.getBrowser(),
						testParameters.getBrowserVersion(), testParameters.getPlatform(), testParameters);
				if(driver==null) {
					driver = new CraftDriver(remoteSauceDriver);
					runContext.setCraftDriver(driver);
					pureWebDriver = remoteSauceDriver;
				}
				else {
					driver.setDriver(remoteSauceDriver);
					runContext.setCraftDriver(driver);
				}
				driver.setTestParameters(testParameters);
			}

			break;

		case TESTOBJECT:

			WebDriver testObjectAppiumDriver = SauceLabsDriverFactory.getTestObjectAppiumDriver(
					testParameters.getMobileExecutionPlatform(), testParameters.getDeviceName(),
					mobileProperties.getProperty("TestObjectHost"), testParameters);
			if(driver==null) {
				driver = new CraftDriver(testObjectAppiumDriver);
				runContext.setCraftDriver(driver);
				pureWebDriver = testObjectAppiumDriver;
			}
			else {
				driver.setDriver(testObjectAppiumDriver);
				runContext.setCraftDriver(driver);
			}
			driver.setTestParameters(testParameters);

			break;

		case FASTEST:
//			if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
//				WebDriver fastestRemoteDriver = FastestDriverFactory.getRemoteWebDriver(testParameters.getBrowser(),
//						testParameters.getBrowserVersion(), testParameters.getPlatform(),
//						mobileProperties.getProperty("FastestHost"), testParameters.getCurrentTestcase());
//				if(driver==null) {
//					driver = new CraftDriver(fastestRemoteDriver);
//					pureWebDriver = fastestRemoteDriver;
//				}
//				else {
//					driver.setDriver(fastestRemoteDriver);
//				}
//				driver.setTestParameters(testParameters);
//			} else if (testParameters.getMobileToolName().equals(ToolName.APPIUM)) {
//				WebDriver mintAppiumtDriver = FastestDriverFactory.getMintAppiumDriver(
//						testParameters.getMobileExecutionPlatform(), testParameters.getDeviceName(),
//						mobileProperties.getProperty("MintHost"), testParameters.getMobileOSVersion());
//				if(driver==null) {
//					driver = new CraftDriver(mintAppiumtDriver);
//					pureWebDriver = mintAppiumtDriver;
//				}
//				else {
//					driver.setDriver(mintAppiumtDriver);
//				}
//				driver.setTestParameters(testParameters);
//			}

			break;

		case BROWSERSTACK:
//			if (testParameters.getMobileToolName().equals(ToolName.REMOTE_WEBDRIVER)) {
//				WebDriver browserstackRemoteDrivermobile = BrowserStackDriverFactory
//						.getBrowserStackRemoteWebDriverMobile(testParameters.getMobileExecutionPlatform(),
//								testParameters.getDeviceName(), mobileProperties.getProperty("BrowserStackHost"),
//								testParameters);
//				if(driver==null) {
//					driver = new CraftDriver(browserstackRemoteDrivermobile);
//					pureWebDriver = browserstackRemoteDrivermobile;
//				}
//				else {
//					driver.setDriver(browserstackRemoteDrivermobile);
//				}
//				driver.setTestParameters(testParameters);
//
//			} else if (testParameters.getMobileToolName().equals(ToolName.DEFAULT)) {
//				WebDriver browserstackRemoteDriver = BrowserStackDriverFactory.getBrowserStackRemoteWebDriver(
//						mobileProperties.getProperty("BrowserStackHost"), testParameters.getBrowser(),
//						testParameters.getBrowserVersion(), testParameters.getPlatform(), testParameters);
//
//				if(driver==null) {
//					driver = new CraftDriver(browserstackRemoteDriver);
//					pureWebDriver = browserstackRemoteDriver;
//				}
//				else {
//					driver.setDriver(browserstackRemoteDriver);
//				}
//				driver.setTestParameters(testParameters);
//			}

			break;

		default:
			throw new FrameworkException("Unhandled Execution Mode!");
		}
	}
    
    private void maximizeWindow() {
		driver.manage().window().maximize();
	}

    
    
}

