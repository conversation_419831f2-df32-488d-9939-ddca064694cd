<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="6" name="Test under EntireSuite_Suite1" tests="6" failures="3" timestamp="2025-05-13T20:35:52 IST" time="177.077" errors="0">
  <testcase name="@AfterMethod afterMethod" time="25.238" classname="com.gilead.base.BaseTest">
    <failure type="org.openqa.selenium.WebDriverException" message="Timed out waiting for driver server to stop.
Build info: version: &amp;apos;4.1.2&amp;apos;, revision: &amp;apos;9a5a329c5a&amp;apos;
System info: host: &amp;apos;Z1VD7STHPRDN255&amp;apos;, ip: &amp;apos;************&amp;apos;, os.name: &amp;apos;Windows 10&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;1.8.0_291&amp;apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [ef0e09041baa69c443d80b1ea80ee2fe, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:63219}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:63219/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: ef0e09041baa69c443d80b1ea80ee2fe">
      <![CDATA[org.openqa.selenium.WebDriverException: Timed out waiting for driver server to stop.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [ef0e09041baa69c443d80b1ea80ee2fe, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:63219}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:63219/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: ef0e09041baa69c443d80b1ea80ee2fe
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:132)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.ExecutionException: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: driver.version: unknown
at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:128)
... 29 more
Caused by: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: driver.version: unknown
at org.openqa.selenium.os.OsProcess.waitFor(OsProcess.java:174)
at org.openqa.selenium.os.CommandLine.waitFor(CommandLine.java:127)
at org.openqa.selenium.remote.service.DriverCommandExecutor.lambda$execute$2(DriverCommandExecutor.java:122)
at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)
... 3 more
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="invokeURL" time="18.32" classname="com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS"/>
  <testcase name="invokeURL" time="17.482" classname="com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site"/>
  <testcase name="invokeURL" time="13.678" classname="com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site"/>
  <testcase name="toVerifyYescartaTecartusREMSSite" time="30.102" classname="com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS">
    <failure type="java.lang.RuntimeException" message="Error while verifying file download &amp;apos;yescarta-pi&amp;apos; on page &amp;apos;REMS&amp;apos;: Downloaded file &amp;apos;yescarta-pi&amp;apos; not found in path &amp;apos;C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles&amp;apos;">
      <![CDATA[java.lang.RuntimeException: Error while verifying file download 'yescarta-pi' on page 'REMS': Downloaded file 'yescarta-pi' not found in path 'C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles'
at businesscomponents.CommonFunctions.handleFileDownload(CommonFunctions.java:4156)
at businesscomponents.CommonFunctions.verifyYescartaTecartusREMSSite(CommonFunctions.java:5732)
at com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS.toVerifyYescartaTecartusREMSSite(To_Download_Resources_From_YescartaTecartusREMS.java:29)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.RuntimeException: Downloaded file 'yescarta-pi' not found in path 'C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles'
at businesscomponents.CommonFunctions.handleFileDownload(CommonFunctions.java:4152)
... 17 more
... Removed 12 stack frames]]>
    </failure>
  </testcase> <!-- toVerifyYescartaTecartusREMSSite -->
  <testcase name="toVerifyCriticalComponentsYescartaTecartusREMSSite" time="39.266" classname="com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site">
    <failure type="com.gilead.config.FrameworkAssertion" message="&amp;apos;1747148646482.pdf&amp;apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles">
      <![CDATA[com.gilead.config.FrameworkAssertion: '1747148646482.pdf' is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
at com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.toVerifyCriticalComponentsYescartaTecartusREMSSite(To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.java:31)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames]]>
    </failure>
  </testcase> <!-- toVerifyCriticalComponentsYescartaTecartusREMSSite -->
  <testcase name="toVerifyYescartaTecartusREMSSite" time="128.607" classname="com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site">
    <failure type="com.gilead.config.FrameworkAssertion" message="&amp;apos;1747148740249.pdf&amp;apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles">
      <![CDATA[com.gilead.config.FrameworkAssertion: '1747148740249.pdf' is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
at com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site.toVerifyYescartaTecartusREMSSite(To_Verify_YescartaTecartusREMS_Site.java:31)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames]]>
    </failure>
  </testcase> <!-- toVerifyYescartaTecartusREMSSite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
