id: switch_default
message: "Ensure default should be handled as part of switch statement"
language: java
severity: warning
rule:
  kind: switch_block
  not:
    has:
      kind: switch_block_statement_group
      has:
        kind: switch_label
        regex: default
---
id: switch_break
message: "Ensure all case statements should have break in switch statement"
language: java
severity: warning
rule:
  kind: switch_block_statement_group
  not:
    has:
      kind: break_statement