package com.gilead.testscripts.Smoke_Testcases;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;


public class GileadMarketAccess_HomePage_TC001 extends BaseTest {

	CommonFunctions objCommonFunctions;


	@Test(priority = 1)
	public void invokeURL() {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyTabsInVirologySection() {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
			objCommonFunctions.validateComponentExists();
		} finally {
			checkErrors();
		}
	}
	@Test(priority = 3)
	public void verifyTabsInOncologySection() {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
			objCommonFunctions.validateComponentExists();
		} finally {
			checkErrors();
		}
	}
}
