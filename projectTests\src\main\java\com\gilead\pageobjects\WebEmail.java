package com.gilead.pageobjects;

import org.openqa.selenium.By;

public class WebEmail {

	public By labelEmailSubject;
	public By verifyProfile,mailResponse;
	public static By inputUsernameSSO = By.xpath("//input[@name='username' or @autocomplete='username']");
	public static By inputPasswordSSO = By.xpath("//input[@name='password' or @type='password']");
	public static By btnSubmitSSO = By.xpath("//input[@type='submit']");
	public static By btnOffice365SSO = By.xpath("//*[@content='0365 Office Portal']");
	public static By inputEmailSSO = By.xpath("//input[@type='email']");
	public static By btnNoSSO = By.xpath("//input[@value='No']");
	public static By btnOutlookSSO = By.xpath("//button[@id='Mail']");
	public static By btnSendMail = By.xpath("//button[@aria-label='Send']");
	public static By btnCloseCallOut = By.xpath("//button[@type='button' and @title='Close the callout']");
	public static By emailContent = By.xpath("//pre[contains(@style,'word-wrap')]");
	public static By sentItems = By.xpath("//div[@data-folder-name='sent items']");
	public static By approvedMessage = By.xpath("//div[@aria-label='Message body']//*[contains(text(),'Approved')]");
	public static By rejectedMessage = By.xpath("//div[@aria-label='Message body']//*[contains(text(),'Rejected')]");
	public static By btnApps = By.xpath("//button[@aria-label='Apps']");
	public static By btnGotIT = By.xpath(".//button[text()='Got it']");
	public static By lnkOutlook = By.xpath("//div[@role='link' and @data-testid='core-apps-Mail']");
	
	
	
	
	public static By labelOtherAccount = By.xpath("//div[contains(@id,'otherTileText')]");
	public static By listOfUnreadEmails = By.xpath(
			"//div[@id='MailList']//div[@class='EeHm8']/div[@role='option' and(contains(@aria-label,'Unread'))]");
	public static By markAsRead = By.xpath(
			"//div[@id='MailList']//div[@class='EeHm8']/div[@role='option' and(contains(@aria-label,'Unread'))]//div//button");
	public static By emailBody = By.xpath(
			"//div[@id='MailList']//div[@class='EeHm8']/div[@role='option' and (contains(@aria-label,'Unread'))]//following::div[@aria-label='Reading Pane']//following-sibling::div[@role='document' and @aria-label='Message body']");
	public static By docuSignLink = By.xpath(
			"(//div[@id='MailList']//div[@class='EeHm8']/div[@role='option' and (contains(@aria-label,'Unread'))]//following::div[@aria-label='Reading Pane']//following-sibling::div[@role='document' and @aria-label='Message body']//table//a)[position()=1]");
	public static By emailInbox = By.xpath("//div[@id='MailList']");
	public static By labelMailReceivedTime = By
			.xpath("//*[contains(@id,'ReadingPaneContainerId')]//*[contains(@data-testid,'SentReceivedSavedTime')]");
	public static By emailSignOut = By.xpath("//a[text()='Sign out']");
	public static By emailViewProfile = By.xpath("//button[contains(@aria-label,'Account manager') and contains(@title,'Account manager')]");
	public static By defaultUserProfile = By.xpath("//button[contains(@aria-label,'Close account')]");
	//
	//public static By emailViewProfile = By.xpath("//div[@id='meInitialsButton']");
	public static By signWithOtherUser = By.xpath("//div[text()='Sign in with a different account']");
	public static By viewRecord = By.xpath(".//a[text()='View Record']");
	public static By btnOther = By.xpath("//button[@name='Other']");
	
	
	
	public WebEmail(String strLabel) {
		//labelEmailSubject = By.xpath("(//*[contains(@id,'ReadingPaneContainerId')]//*[contains(@data-testid,'SentReceivedSavedTime')]//ancestor::div[@id='ReadingPaneContainerId']//div[@role='heading']/div/span[contains(normalize-space(text()),'"+ strLabel + "')])|(//div//*[contains(normalize-space(text()),'"+ strLabel + "')])");
		labelEmailSubject = By.xpath(".//div//*[contains(normalize-space(text()),'"+ strLabel + "')]");
		verifyProfile = By.xpath("//div[@id='mectrl_currentAccount_secondary' and contains(text(),'"+ strLabel +"@Gileadconnect.onmicrosoft.com') or contains(text(),'"+ strLabel +"@gilead.com')]");
		mailResponse = By.xpath(".//a[text()='"+ strLabel +"']");
	}
}
