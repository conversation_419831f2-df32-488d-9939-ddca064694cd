Sub Test_Main(Debug, CurrentTestSet, CurrentTSTest, CurrentRun)

    TDOutput.Clear

    'Local Variable Declarations
    Dim objShell,strHomeFolder,StrTempFolder,strTestCaseFolder,objFso,objTestCaseFolder,objTestCaseFolderFiles
    Dim objFile,objTestCaseFolderSubfolders,objFolder,strRelativePath,StrWorkspaceFolder,StrWorkspaceParentFolder,StrParentFolderpath
    Dim objCurrentTestSet,objMyFile,objDriverScript

    If Debug Then
        MsgBox "This test cannot be run in debug mode! Please choose the test mode...", , "Error"
        Exit Sub
    End If

    Set objShell = CreateObject("WScript.Shell")
    strHomeFolder = objShell.ExpandEnvironmentStrings("%USERPROFILE%")
    StrTempFolder = objShell.ExpandEnvironmentStrings("%TEMP%")
    strTestCaseFolder = StrTempFolder & "\" & CurrentTSTest.TestName & "_" & CurrentTSTest.Instance
    Set objFso = CreateObject("Scripting.FileSystemObject")
    if Not (objFso.FolderExists(strTestCaseFolder)) then
       objFso.CreateFolder(strTestCaseFolder)
    Else
       set objTestCaseFolder = objFso.GetFolder(strTestCaseFolder)
       Set objTestCaseFolderFiles = objTestCaseFolder.Files
       for each objFile in objTestCaseFolderFiles
           objFile.Delete
       Next
       Set objTestCaseFolderSubfolders = objTestCaseFolder.SubFolders
       for each objFolder in objTestCaseFolderSubfolders
           objFolder.Delete
       Next

       'Release Objects
       Set objTestCaseFolder = nothing
       Set objTestCaseFolderFiles = nothing
       Set objTestCaseFolderSubfolders = nothing
       Set objFile = Nothing
       Set objFolder = Nothing
    End IF

    DownloadFileFromALM "Subject\SPARC Automation\FunctionalLibraries_Test\","Framework Folder Settings.properties",strTestCaseFolder&"\"
    StrWorkspaceFolder = ReadVariablefromProperties(strTestCaseFolder+"\Framework Folder Settings.properties","WorkspaceFolderName")
    StrWorkspaceParentFolder = ReadVariablefromProperties(strTestCaseFolder+"\Framework Folder Settings.properties","WorkspaceParentFolderName")
    StrParentFolderpath = strHomeFolder+"\"+StrWorkspaceParentFolder
    strRelativePath = StrParentFolderpath&"\"&StrWorkspaceFolder        'Customize this line as required
    TDOutput.Print strRelativePath
    If Not objFso.FolderExists(strRelativePath) Then
        TDOutput.Print "The specified framework path ' " & strRelativePath & "' does not exist!"
        CurrentRun.Status = "Failed"
        CurrentTSTest.Status = "Failed"
        Exit Sub
    End If

    'Handle run-time errors
    If Err.Number <> 0 Then
       TDOutput.Print "Run-time error [" & Err.Number & "] : " & Err.Description
       CurrentRun.Status = "Failed"
       CurrentTSTest.Status = "Failed"
    End If

    strTcName = CurrentTSTest.TestName
    strBatFilePath = strRelativePath &  "\AlmExecution.bat"
    strCommandLineArguments = """'" & strTcName   &  "'"""
    dim PackageName
    dim SuiteName
    SuiteName =  "EntireSuite_Suite1"
    PackageName = "com.gilead.testscripts.incident"
    dim job
    job = strBatFilePath & " """ & SuiteName   &  """" & " """ & strTcName &  """" & " """ & PackageName &  """" & " """ & strRelativePath &  "\"""
    'MsgBox job
    XTools.run strBatFilePath, job

        'Release Objects
    'Set objDriverScript = nothing
    'Set objFso = nothing
    'Set objShell = nothing

End Sub

'Functions
'Function to download file from ALM Test Plan
'##############################################################################
Sub DownloadFileFromALM(strNodePath,strFilename,strClientpath)

    Dim objTstMgr,objFolderPath,objAttFactory,objAttachment,objAttList,strDownloadPath,blnFileExists,objAttachmentStorage

    blnFileExists = false
    Set objFso = CreateObject("Scripting.FileSystemObject")
    Set objTstMgr = TDConnection.TreeManager
    Set objFolderPath = objTstMgr.NodeByPath(StrNodePath)'ALM Path of the Zip file
    if objFolderPath is Nothing then
         msgbox "Error - specified Path does not Exists in ALM " & StrNodePath,16,"Error - File Not Downloaded"
         Err.Raise 6001,"Error - specified Path does not Exists in ALM " & StrNodePath
    End If
    Set objAttFactory = objFolderPath.Attachments
    Set objAttList = objAttFactory.NewList("")
    for each objAttachment in objAttList
        if (strComp(objAttachment.Name(1),strFilename,vbtextcompare)=0) then
           blnFileExists = true
           if Not (objFso.FileExists(strClientpath & "\" & objAttachment.Name(1))) then
              Set objAttachmentStorage = objAttachment.AttachmentStorage
              objAttachmentStorage.ClientPath = strClientpath & "\"
              objAttachmentStorage.Load objAttachment.Name,True
              Set objFile = objFso.GetFile(strClientpath & "\" & objAttachment.Name)
              if Not objFile.name = strFileName then
                 objFile.name = strFileName
              End If
              Exit For
           End If
        End IF
    Next

    'Release Objects
    Set objTstMgr = nothing
    Set objFolderPath = nothing
    Set objAttFactory = nothing
    Set objAttList = nothing
    Set objAttachmentStorage = nothing

    if blnFileExists = false then
       msgbox "Error - specified file " & strFilename & " does not exists in the attachments of path " & strNodePath ,16,"Error - File Not Found"
       Err.Raise 6002,"Error - File Not Found","Error - specified file " & strFilename & " does not exists in the attachments of path " & strNodePath
    Else
       if Not objFso.FileExists(strclientpath & "\" & strFilename) then
          msgbox "Error - specified file " & strFilename & " does not gets downloaded in the path " & strclientpath,16,"Error - File Not Downloaded"
          Set objFso = nothing
          Err.Raise 6003,"Error - File Not Downloaded","Error - specified file " & strFilename & " does not gets downloaded in the path " & strclientpath
       End IF
    End IF

    Set objFso = nothing

End Sub
'#############################################################################
'Function to Extract Zip File to the specified Folder
'#############################################################################
Sub UnzipFile(strZipFilePath,strExtractToPath,strZipFileName)

    Dim objShell,objFilesInZip,objExtractFolder,objSubFolder,objFso,blnZipFileExists

    blnZipFileExists = false
    Set objFso = CreateObject("Scripting.FileSystemObject")
    set objShell = CreateObject("Shell.Application")
    if (objFso.FileExists(strZipFilePath)) then
        blnZipFileExists = true
        set objFilesInZip = objShell.NameSpace(strZipFilePath).items
        objShell.NameSpace(strExtractToPath).CopyHere(objFilesInZip)
        objFso.DeleteFile strZipFilePath
        Set objExtractFolder = objFso.GetFolder(strExtractToPath)
        Set objSubFolder = objExtractFolder.SubFolders
        for each objExtractFolder in objSubFolder
            if (InStr(objExtractFolder.name,strZipFileName)>0) then'ALM name of the zip file
                if Not objExtractFolder.name = strZipFileName then
                   objExtractFolder.name = strZipFileName
                End if
            End if
        Next
    End IF

    'Release Objects
    Set objFso = Nothing
    Set objShell = Nothing
    Set objFilesInZip = Nothing
    Set objExtractFolder = Nothing
    Set objSubFolder = Nothing

    If blnZipFileExists = false then
        msgbox "Error - specified zip file does not exists in the path " & strZipFilePath ,16,"Error - Zip File Not Found"
        Err.Raise 6004,"Error - Zip File Not Found","Error - specified zip file does not exists in the path " & strZipFilePath
    End IF

End Sub
'##############################################################################
'Function to Read property from properties file
'#############################################################################
Function ReadVariablefromProperties(strPropertyFilePath,strVariable)

    Dim blnFlag,objFile,intLen,strVariableValue,strConfigLine,intEqualSignPosition,strVariableInProperties,objFso

    blnFlag = False
    Set objFso = CreateObject("Scripting.FileSystemObject")
    Set objFile = objFso.OpenTextFile(strPropertyFilePath)
    do while(NOT objFile.AtEndOfStream)
       strConfigLine = TRIM(objFile.ReadLine)
       IF ((INSTR(1,strConfigLine,"#",1) <> 1) AND (INSTR(1,strConfigLine,strVariable,1) <> 0))THEN
            intEqualSignPosition = INSTR(1,strConfigLine,"=",1)
            intLen = LEN(strConfigLine)
            strVariableInProperties = TRIM(Mid(strConfigLine, 1, intEqualSignPosition-1))
            if strComp(strVariableInProperties,strVariable,vbtextcompare) = 0 then
               blnFlag = True
               strVariableValue = TRIM(Mid(strConfigLine, intEqualSignPosition + 1, intLen - intEqualSignPosition))
               ReadVariablefromProperties= strVariableValue
               Exit Do
            End IF
       End If
    Loop
    objFile.Close
    Set objFile = nothing
    Set objFso = nothing

    if blnFlag = False then
       MsgBox "Error - " & strvariable & " is not found in the property file " & strPropertyFilePath,16,"Error - Variable Not Found"
       Err.Raise 6005,"Error - Property Not Found","Error - " & strvariable & " is not found in the property file " & strPropertyFilePath
    End If

End Function