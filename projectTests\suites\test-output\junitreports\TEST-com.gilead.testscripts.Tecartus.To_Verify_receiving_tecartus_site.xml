<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site" tests="2" failures="0" timestamp="2025-04-22T11:57:44 IST" time="126.692" errors="1">
  <testcase name="invokeURL" time="47.337" classname="com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site"/>
  <system-out/>
  <testcase name="verifyReceivingYescarta" time="79.355" classname="com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site">
    <error type="java.lang.RuntimeException" message="Error handling video playback for &#039;What&#039;s the CAR T treatment process like? from Gilead Sciences on Vimeo&#039; on page &#039;Tecartus&#039;: javascript error: Cannot read properties of null (reading &#039;pause&#039;)
  (Session info: chrome=131.0.6778.265)
Build info: version: &#039;4.1.2&#039;, revision: &#039;9a5a329c5a&#039;
System info: host: &#039;Z1VD7STHPRDN255&#039;, ip: &#039;************&#039;, os.name: &#039;Windows 10&#039;, os.arch: &#039;amd64&#039;, os.version: &#039;10.0&#039;, java.version: &#039;1.8.0_291&#039;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector(&#039;video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video&#039;).pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad">
      <![CDATA[java.lang.RuntimeException: Error handling video playback for 'What's the CAR T treatment process like? from Gilead Sciences on Vimeo' on page 'Tecartus': javascript error: Cannot read properties of null (reading 'pause')
  (Session info: chrome=131.0.6778.265)
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector('video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video').pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad
at businesscomponents.CommonFunctions.handleVideoPlayAndClose(CommonFunctions.java:4038)
at businesscomponents.CommonFunctions.verifyReceivingSite(CommonFunctions.java:4416)
at com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site.verifyReceivingYescarta(To_Verify_receiving_tecartus_site.java:28)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: org.openqa.selenium.JavascriptException: javascript error: Cannot read properties of null (reading 'pause')
  (Session info: chrome=131.0.6778.265)
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector('video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video').pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.RemoteWebDriver.executeScript(RemoteWebDriver.java:492)
at businesscomponents.CommonFunctions.videoPlay(CommonFunctions.java:1077)
at businesscomponents.CommonFunctions.handleVideoPlayAndClose(CommonFunctions.java:4034)
... 17 more
]]>
    </error>
  </testcase> <!-- verifyReceivingYescarta -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site -->
