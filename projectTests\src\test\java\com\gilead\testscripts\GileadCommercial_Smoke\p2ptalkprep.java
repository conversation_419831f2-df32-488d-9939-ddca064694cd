package com.gilead.testscripts.GileadCommercial_Smoke;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "P2PTalkPrep_Smoke_Tests", testlab = "${testlab}", testset = "${testset}")

public class p2ptalkprep extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void logOnPeerInsights() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 3)
	public void verifyPeerInsightsPage() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 4)
	public void verifyRegisterLink() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}
}
