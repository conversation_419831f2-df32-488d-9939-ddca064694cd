package com.gilead.testscripts.GileadCommercial_Smoke;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TalkPrep_Smoke_Tests", testlab = "${testlab}", testset = "${testset}")

public class talkprep extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyAboutPrepPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 3)
	public void verifyRegisterLinkAboutPrep() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 4)
	public void verifyPDFDownloadAboutPrep() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 5)
	public void verifyHomePage() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 6)
	public void verifyRegisterLinkHomePage() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 7)
	public void verifyPDFDownloadHomePage() throws Exception {
		try {
			objCommonFunctions.checkPdfFileDownload();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 8)
	public void verifyPrescribingPrepPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 9)
	public void verifyPrescribingPrepPageLinks() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 10)
	public void logOnResourcesPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 11)
	public void verifyStayInformedPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 12)
	public void verifyStayInformedPageLinks() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 13)
	public void verifyWhoIsPrepForPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 14)
	public void verifyWhoIsPrepForPageLinks() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}
}
