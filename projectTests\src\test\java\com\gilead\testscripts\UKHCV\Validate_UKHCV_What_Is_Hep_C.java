package com.gilead.testscripts.UKHCV;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class Validate_UKHCV_What_Is_Hep_C extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void validateBeFreeOfHepCMenu() throws Exception {
		try {
			objCommonFunctions.clickSubMenu();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 3)
	public void validateWhatIsHepCSection() throws Exception {
		try {
			objCommonFunctions.validateComponentExists();
		} finally {
			checkErrors();
		}
	}
}
