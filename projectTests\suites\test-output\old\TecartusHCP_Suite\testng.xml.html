<html><head><title>testng.xml for TecartusHCP_Suite</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;guice-stage="DEVELOPMENT"&nbsp;name="TecartusHCP_Suite"&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="1"&nbsp;name="Test&nbsp;under&nbsp;EntireSuite_Suite1"&nbsp;parallel="classes"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;parameter&nbsp;name="RunID"&nbsp;value="0"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;Test&nbsp;under&nbsp;EntireSuite_Suite1&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;TecartusHCP_Suite&nbsp;--&gt;
<br/></tt></body></html>