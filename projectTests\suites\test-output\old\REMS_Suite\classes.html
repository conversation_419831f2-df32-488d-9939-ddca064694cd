<table border='1'>
<tr>
<th>Class name</th>
<th>Method name</th>
<th>Groups</th>
</tr><tr>
<td>com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>toVerifyYescartaTecartusREMSSite</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>toVerifyYescartaTecartusREMSSite</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>toVerifyCriticalComponentsYescartaTecartusREMSSite</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
</table>
