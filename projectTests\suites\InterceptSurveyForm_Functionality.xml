<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="InterceptSurveyForm_Suite">


	<test name="Test under EntireSuite_Suite1" thread-count="1"
		parallel="classes">
		<parameter name="RunID" value="0" />
		<!--<test name="Test under EntireSuite_Suite1" thread-count="1" > -->
		<classes>
			
			<class
				name="com.gilead.testscripts.Sprint2.TC001_InterceptSurveyformtriggeredin_What_is_CART_video"
			/>
			<!--<class
				name="com.gilead.testscripts.Sprint2.TC002_InterceptSurveyformtriggeredin_Preparing_for_CART_video"
			/>
			<class
				name="com.gilead.testscripts.Sprint2.TC003_InterceptSurveyformtriggeredin_What_is_like_to_get_CART_video"
			/>
			<class
				name="com.gilead.testscripts.Sprint2.TC004_InterceptSurveyformtriggeredin_Explaining_How_CART_works_video"
			/>
			<class
				name="com.gilead.testscripts.Sprint2.TC005_InterceptSurveyformtriggeredin_Patient_stories_My_CART_experience_video"
			/>
			<class
				name="com.gilead.testscripts.Sprint2.TC006_InterceptSurveyformtriggeredin_Patient_stories_After_my_CART_Infusion_video"
			/>
			<class
				name="com.gilead.testscripts.Sprint2.TC007_InterceptSurveyformtriggeredin_Caregiver_stories_My_CART_Expereience_video"
			/>
			<class
				name="com.gilead.testscripts.Sprint2.TC008_InterceptSurveyformtriggeredin_Caregiver_stories_Taking_care_of_yourself_video"
			/>
			<class
				name="com.gilead.testscripts.Sprint2.TC009_InterceptSurveyformtriggeredin_Patient_and_caregiver_stories_video"
			/>
			<class
				name="com.gilead.testscripts.Sprint2.TC010_InterceptSurveyformtriggeredin_Patient_Stories_My_health_care_team_video"
			/>-->
		</classes>

	</test>
</suite>