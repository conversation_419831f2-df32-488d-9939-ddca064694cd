package com.gilead.base;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import org.testng.TestNG;
import org.testng.xml.XmlClass;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlSuite.ParallelMode;
import org.testng.xml.XmlTest;

import com.gilead.maintenance.Settings;
import com.gilead.maintenance.TestExecutionInfo;
import com.gilead.reports.SeleniumTestParameters;
import com.gilead.utils.ServiceRegister;

@SuppressWarnings("unused")
public class TestJob implements Callable<ServiceRegister> {

	private List<SeleniumTestParameters> params;
	//private ServiceRegister register = ServiceRegister.getInstance();
	final int TOTAL_THREADS;

	public TestJob(List<SeleniumTestParameters> params, int threadCount) {
		this.params = params;
		TOTAL_THREADS = threadCount;
	}

	@Override
	public ServiceRegister call() {
		try {
			
			Properties properties = Settings.getInstance();
			int totalThreads = Integer.parseInt(properties.getProperty("TestRunnerThreads"));
			
			TestConfigurationInfo info = new TestConfigurationInfo();
			TestNG testng = new TestNG();
			//register.setName(Thread.currentThread().getName());

			List<TestExecutionInfo> executionsInfo = null;

			if (null == params) {
				executionsInfo = info.getTestExecutionsInfo("Regression");
			} else {
				executionsInfo = info.getTestExecutionsInfo("Regression",params);
			}

			Map<String, List<TestExecutionInfo>> currentSuites = executionsInfo.stream()
					.collect(Collectors.groupingBy(TestExecutionInfo::getTestScenario));
			List<XmlSuite> suites = new ArrayList<XmlSuite>();
			List<XmlTest> tests = new ArrayList<XmlTest>();
			List<String> listeners = new ArrayList<String>();
			listeners.add("com.agiletestware.bumblebee.annotations.testng.BumblebeeTestNGListener");
			listeners.add("com.agiletestware.bumblebee.annotations.testng.BumblebeeTestNGReporter");

			for (Map.Entry<String, List<TestExecutionInfo>> tinfo : currentSuites.entrySet()) {

				String currentSuite = tinfo.getKey();
				List<TestExecutionInfo> classes = tinfo.getValue();
				XmlSuite suite = new XmlSuite();
				suite.setName(currentSuite);
				suite.setListeners(listeners);
				XmlTest test = new XmlTest(suite);
				test.setName("Test under " + currentSuite);
				List<XmlClass> xmlClasses = classes.stream().map((config) -> new XmlClass(
						config.getTestCasePackage()+"."+config.getTestCase()))
						.collect(Collectors.toList());
				test.setXmlClasses(xmlClasses);
				test.setParallel(ParallelMode.CLASSES);
				test.setThreadCount(TOTAL_THREADS);
				tests.add(test);
				suites.add(suite);
			}
			testng.setXmlSuites(suites);
			testng.run();
			
			
			return ServiceRegister.getInstance();
			
		} catch (Exception e) {
			
			System.out.println(e.getMessage());
			return ServiceRegister.getInstance();
		}
	}

	

}
