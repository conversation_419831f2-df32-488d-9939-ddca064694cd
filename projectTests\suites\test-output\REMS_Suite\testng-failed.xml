<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite guice-stage="DEVELOPMENT" name="Failed suite [REMS_Suite]">
  <test thread-count="5" name="Test under EntireSuite_Suite1(failed)" parallel="classes">
    <parameter name="RunID" value="0"/>
    <classes>
      <class name="com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="toVerifyCriticalComponentsYescartaTecartusREMSSite"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site -->
      <class name="com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="toVerifyYescartaTecartusREMSSite"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS -->
      <class name="com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="toVerifyYescartaTecartusREMSSite"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site -->
    </classes>
  </test> <!-- Test under EntireSuite_Suite1(failed) -->
</suite> <!-- Failed suite [REMS_Suite] -->
