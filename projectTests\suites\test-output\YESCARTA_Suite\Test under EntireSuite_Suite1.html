<html>
<head>
<title>TestNG:  Test under EntireSuite_Suite1</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Test under EntireSuite_Suite1</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>17/1/0</td>
</tr><tr>
<td>Started on:</td><td>Tue Apr 22 11:16:23 IST 2025</td>
</tr>
<tr><td>Total time:</td><td>925 seconds (925918 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED CONFIGURATIONS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name=PDFverification status=SUCCESS method=PDF_Download_Verification_for_YesCARTA_Website.PDFverification()[pri:2, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b] output={null}], public void com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.PDFverification() throws java.lang.Exception</td>
<td><div><pre>org.openqa.selenium.WebDriverException: Timed out waiting for driver server to stop.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [30d253b9e6edae7f10b6d7d29a0facfa, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57833}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57833/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: 30d253b9e6edae7f10b6d7d29a0facfa
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:132)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
	at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
	at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
	at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
	at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
	at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.ExecutionException: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: driver.version: unknown
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:128)
	... 29 more
Caused by: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: driver.version: unknown
	at org.openqa.selenium.os.OsProcess.waitFor(OsProcess.java:174)
	at org.openqa.selenium.os.CommandLine.waitFor(CommandLine.java:127)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.lambda$execute$2(DriverCommandExecutor.java:122)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)
	... 3 more
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1907960477", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1907960477'><pre>org.openqa.selenium.WebDriverException: Timed out waiting for driver server to stop.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [30d253b9e6edae7f10b6d7d29a0facfa, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57833}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57833/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: 30d253b9e6edae7f10b6d7d29a0facfa
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:132)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
	at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
	at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
	at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
	at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
	at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:660)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.ExecutionException: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: driver.version: unknown
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:128)
	... 29 more
Caused by: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: driver.version: unknown
	at org.openqa.selenium.os.OsProcess.waitFor(OsProcess.java:174)
	at org.openqa.selenium.os.CommandLine.waitFor(CommandLine.java:127)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.lambda$execute$2(DriverCommandExecutor.java:122)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)
	... 3 more
</pre></div></td>
<td>26</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name=navigationVerification status=SUCCESS method=Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.navigationVerification()[pri:2, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be] output={null}], public void com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.navigationVerification() throws java.lang.Exception</td>
<td><div><pre>org.openqa.selenium.WebDriverException: Timed out waiting for driver server to shutdown.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [d117071334df6b37d2c842a5c3425aa7, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57183}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57183/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: d117071334df6b37d2c842a5c3425aa7
	at org.openqa.selenium.remote.service.DriverService.stop(DriverService.java:277)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:129)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
	at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
	at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
	at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
	at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
	at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.openqa.selenium.net.UrlChecker$TimeoutException: Timed out waiting for http://localhost:62897/shutdown to become unavailable after 3005 ms
	at org.openqa.selenium.net.UrlChecker.waitUntilUnavailable(UrlChecker.java:130)
	at org.openqa.selenium.remote.service.DriverService.stop(DriverService.java:273)
	... 30 more
Caused by: java.util.concurrent.TimeoutException
	at java.util.concurrent.FutureTask.get(FutureTask.java:205)
	at org.openqa.selenium.net.UrlChecker.waitUntilUnavailable(UrlChecker.java:128)
	... 31 more
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace367571741", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace367571741'><pre>org.openqa.selenium.WebDriverException: Timed out waiting for driver server to shutdown.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [d117071334df6b37d2c842a5c3425aa7, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57183}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57183/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: d117071334df6b37d2c842a5c3425aa7
	at org.openqa.selenium.remote.service.DriverService.stop(DriverService.java:277)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:129)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
	at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
	at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
	at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
	at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
	at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:660)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.openqa.selenium.net.UrlChecker$TimeoutException: Timed out waiting for http://localhost:62897/shutdown to become unavailable after 3005 ms
	at org.openqa.selenium.net.UrlChecker.waitUntilUnavailable(UrlChecker.java:130)
	at org.openqa.selenium.remote.service.DriverService.stop(DriverService.java:273)
	... 30 more
Caused by: java.util.concurrent.TimeoutException
	at java.util.concurrent.FutureTask.get(FutureTask.java:205)
	at org.openqa.selenium.net.UrlChecker.waitUntilUnavailable(UrlChecker.java:128)
	... 31 more
</pre></div></td>
<td>17</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders()'><b>verifyNavigationHeaders</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkAssertion: Error -  Navigation Menu is not enabled in the page: &quot;https://www.yescarta.com/managing-side-effects#infusion-and-monitoring&quot; even after waiting for 10 Seconds
	at com.gilead.reports.Report.updateTestLog(Report.java:423)
	at com.gilead.maintenance.ALMFunctions.ThrowException(ALMFunctions.java:252)
	at com.gilead.maintenance.WebDriverUtil.waitUntilElementEnabled(WebDriverUtil.java:559)
	at com.gilead.maintenance.CommonActionsAndFunctions.clickByJS(CommonActionsAndFunctions.java:501)
	at businesscomponents.CommonFunctions.navigateAndVerifyMenu(CommonFunctions.java:3920)
	at businesscomponents.CommonFunctions.accessNavigationMenu(CommonFunctions.java:4983)
	at com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders(Navigation_and_Functionality_Verification_for_YesCARTA_Website.java:29)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1395265820", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1395265820'><pre>com.gilead.config.FrameworkAssertion: Error -  Navigation Menu is not enabled in the page: &quot;https://www.yescarta.com/managing-side-effects#infusion-and-monitoring&quot; even after waiting for 10 Seconds
	at com.gilead.reports.Report.updateTestLog(Report.java:423)
	at com.gilead.maintenance.ALMFunctions.ThrowException(ALMFunctions.java:252)
	at com.gilead.maintenance.WebDriverUtil.waitUntilElementEnabled(WebDriverUtil.java:559)
	at com.gilead.maintenance.CommonActionsAndFunctions.clickByJS(CommonActionsAndFunctions.java:501)
	at businesscomponents.CommonFunctions.navigateAndVerifyMenu(CommonFunctions.java:3920)
	at businesscomponents.CommonFunctions.accessNavigationMenu(CommonFunctions.java:4983)
	at com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders(Navigation_and_Functionality_Verification_for_YesCARTA_Website.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>369</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website</td>
<td></td>
<td>39</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.navigationVerification()'><b>navigationVerification</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website</td>
<td></td>
<td>272</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site.verifyClinicalTrialResults()'><b>verifyClinicalTrialResults</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site</td>
<td></td>
<td>146</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site.verifyManagingSideEffectsSite()'><b>verifyManagingSideEffectsSite</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site</td>
<td></td>
<td>60</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website</td>
<td></td>
<td>41</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website</td>
<td></td>
<td>52</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.PDFverification()'><b>PDFverification</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website</td>
<td></td>
<td>27</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site</td>
<td></td>
<td>28</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.PIandNavigationVerification()'><b>PIandNavigationVerification</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website</td>
<td></td>
<td>68</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.verifyReceivingYescarta()'><b>verifyReceivingYescarta</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site</td>
<td></td>
<td>109</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site.verifySupportAndResourcesSite()'><b>verifySupportAndResourcesSite</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site</td>
<td></td>
<td>167</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site</td>
<td></td>
<td>29</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website</td>
<td></td>
<td>21</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site</td>
<td></td>
<td>16</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site</td>
<td></td>
<td>52</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site</td>
<td></td>
<td>35</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.verifyYescartaSite()'><b>verifyYescartaSite</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site</td>
<td></td>
<td>107</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
</table><p>
</body>
</html>