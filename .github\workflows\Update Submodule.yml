name: Update Submodule

on:
  workflow_dispatch:

jobs:
  update-submodule:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout main repository
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.GIT_TOKEN }}
        submodules: recursive  
        ref: ${{ github.ref_name }}

    - name: Retrive User Details
      run: |
          sudo apt-get update
          sudo apt-get install -y jq
          user_name=$(curl -s -H "Authorization: token ${{ secrets.GIT_TOKEN }}" https://api.github.com/user)
          echo "user_login=$(echo $user_name | jq -r '.login')" >> $GITHUB_ENV
          user_email=$(curl -s -H "Authorization: token ${{ secrets.GIT_TOKEN }}" https://api.github.com/user/emails)
          echo "user_login_email=$(echo $user_email | jq -r '.[0].email')" >> $GITHUB_ENV
          
    - name: Configure git
      run: |
        git config --global user.name "${{ env.user_login }}"
        git config --global user.email "${{ env.user_email }}"
        
    - name: Initialize submodule
      run: |
          git submodule sync
          git submodule update --init --recursive
      
    - name: Reset submodule to latest remote state on main branch
      run: |
          cd GileadCore
          git fetch --all 
          git reset --hard origin/main
          cd ..
    - name: Commit and Push Changes if any
      run: |
          git add .
          git commit -m "Submodule updated to latest commit from Remote" || echo "No commit to Change"
          git push origin HEAD:${{ github.ref_name }}
    env:
        GITHUB_TOKEN: ${{ secrets.GIT_TOKEN }}
