package com.gilead.base;

import java.io.IOException;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.IExecutionListener;

import com.gilead.utils.PasswordDecrypter;

import rest.connector.Dao;
import rest.connector.RestConnector;
import rest.constants.AuthType;
import rest.utils.Util;

public class ALMExecutionListener implements IExecutionListener {
	
	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());

	
	@Override
	public void onExecutionStart() {
		InputStream stream = ALMExecutionListener.class.getResourceAsStream("/Global Settings.properties");
		Properties properties = new Properties();
		try {
			properties.load(stream);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		String almIntegration = properties.getProperty("AlmIntegration");
		if(Boolean.valueOf(almIntegration)) {
			connectGTEST();
		}
	}
	
	@Override
	public void onExecutionFinish() {
		InputStream stream = ALMExecutionListener.class.getResourceAsStream("/Global Settings.properties");
		Properties properties = new Properties();
		try {
			properties.load(stream);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		String almIntegration = properties.getProperty("AlmIntegration");
		if(Boolean.valueOf(almIntegration)) {
			disconnectGTEST();
		}
	}
	
	private void connectGTEST() {
		try {
		
			RestConnector.instance().init(Util.getAlmProperty("host"), Util.getAlmProperty("port"),
					Util.getAlmProperty("domain"), Util.getAlmProperty("project"));
			logger.info(String.format("Connected to Host %s, Domain %s, Project %s successfully", 
					Util.getAlmProperty("host"),
					Util.getAlmProperty("domain"),
					Util.getAlmProperty("project")));
			
			if(Util.getAlmProperty("authtype").equalsIgnoreCase(AuthType.BASIC.toString())) {
				Dao.login(Util.getAlmProperty("username"),
						PasswordDecrypter.decrypt(Util.getAlmProperty("password")), AuthType.BASIC);
			} else if(Util.getAlmProperty("authtype").equalsIgnoreCase(AuthType.OAUTH2.toString())) {
				Dao.login(Util.getAlmProperty("clientid"),
						Util.getAlmProperty("secret"), AuthType.OAUTH2);
			}
			
			if(Boolean.valueOf(Util.getAlmProperty("rest_session_idle_timeout_validation"))) {
				int minutes = Integer.parseInt(Util.getAlmProperty("rest_session_idle_timeout"));
				SessionValidation.validateTimeout(minutes);
			}
			
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}
	
	private void disconnectGTEST() {
		try {
			Dao.logout();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
