<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.YescartaHCP.verifyHowYescartaWorksSite" tests="2" failures="1" timestamp="2025-04-15T12:56:25 IST" time="142.605" errors="0">
  <testcase name="invokeURL" time="6.030" classname="com.gilead.testscripts.YescartaHCP.verifyHowYescartaWorksSite"/>
  <system-out/>
  <testcase name="verifyHowYescartaWorksMenuAndSite" time="136.575" classname="com.gilead.testscripts.YescartaHCP.verifyHowYescartaWorksSite">
    <failure type="com.gilead.config.FrameworkAssertion" message="User is unable to view valid results">
      <![CDATA[com.gilead.config.FrameworkAssertion: User is unable to view valid results
at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
at com.gilead.testscripts.YescartaHCP.verifyHowYescartaWorksSite.verifyHowYescartaWorksMenuAndSite(verifyHowYescartaWorksSite.java:32)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- verifyHowYescartaWorksMenuAndSite -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.YescartaHCP.verifyHowYescartaWorksSite -->
