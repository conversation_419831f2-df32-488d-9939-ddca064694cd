id: comment_method
message: "Provide javadoc headers for all methods"
language: java
severity: warning
rule:
  kind: method_declaration
  not:
    follows:
      kind: block_comment
---
id: comment_class
message: "Provide javadoc headers for all class"
language: java
severity: warning
rule:
  kind: class_declaration
  not:
    follows:
      kind: block_comment
---
id: comment_constructors
message: "Provide javadoc headers for all constructors"
language: java
severity: warning
rule:
  kind: constructor_declaration
  not:
    follows:
      kind: block_comment