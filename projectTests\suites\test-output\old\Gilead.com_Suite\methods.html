<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Gilead.com_Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:30</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:33</td>   <td>3075</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:58</td>   <td>88499</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:27:11</td>   <td>-78969</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-2@1135846825</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/02 13:31:27</td>   <td>177601</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_StoryPage_TC004.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">invokeURL</td> 
  <td>Thread8@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:38</td>   <td>68688</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:31:18</td>   <td>167971</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:27:11</td>   <td>-78969</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:31:18</td>   <td>168728</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread7@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:31:40</td>   <td>190662</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread8@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:41</td>   <td>71655</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:05</td>   <td>-24483</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:32:26</td>   <td>236135</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread8@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:27:11</td>   <td>-78969</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-2@1135846825</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/02 13:27:52</td>   <td>-38090</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_HomePage_TC001.verifyHeader()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">verifyHeader</td> 
  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:06</td>   <td>-23399</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/02 13:30:16</td>   <td>105972</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_SciencePipeline_TC006.verifyPipeline()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">verifyPipeline</td> 
  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:59</td>   <td>29817</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:31:18</td>   <td>168728</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread7@1719578079</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:29:38</td>   <td>68681</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:36</td>   <td>66237</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:01</td>   <td>-29029</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:30:56</td>   <td>146160</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:30:56</td>   <td>146163</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:29</td>   <td>-526</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:39</td>   <td>69606</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:12</td>   <td>-17489</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:30</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:30:46</td>   <td>136719</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:12</td>   <td>-17477</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:36</td>   <td>66237</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:31:18</td>   <td>167969</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:06</td>   <td>-23399</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:06</td>   <td>-23399</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:31:18</td>   <td>167971</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:32:12</td>   <td>222234</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:30:46</td>   <td>136719</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/02 13:28:29</td>   <td>-526</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_NewsPressRelease_TC003.newsPressRelease()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">newsPressRelease</td> 
  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/02 13:31:18</td>   <td>168729</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_CompanyStatement_TC005.verifyCompanyStatement()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">verifyCompanyStatement</td> 
  <td>Thread7@1719578079</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/02 13:28:17</td>   <td>-12735</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_NewsPressRelease_TC003.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">invokeURL</td> 
  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:28:33</td>   <td>3077</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:05</td>   <td>-24483</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:29</td>   <td>-526</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:30:56</td>   <td>146163</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/02 13:29:58</td>   <td>88499</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_ScienceTherapeuticAreas_TC007.verifySTA()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">verifySTA</td> 
  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:30:16</td>   <td>105971</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:01</td>   <td>-29029</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:06</td>   <td>-23399</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/02 13:27:27</td>   <td>-62787</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_GlobalSearchFuntionality_TC002.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">invokeURL</td> 
  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:28:12</td>   <td>-17477</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:29:41</td>   <td>71655</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:31:14</td>   <td>164069</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:01</td>   <td>-29029</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:27:10</td>   <td>-79294</td> <td title="&gt;&gt;CRAFTLiteTestCase.setUpTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&gt;&gt;setUpTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1640296160</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/02 13:27:31</td>   <td>-58772</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_HomePage_TC001.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">invokeURL</td> 
  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/02 13:31:40</td>   <td>190662</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_StoryPage_TC004.verifyStoryPage()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">verifyStoryPage</td> 
  <td>Thread8@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:38</td>   <td>68676</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:59</td>   <td>29817</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:31:14</td>   <td>164069</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/02 13:29:43</td>   <td>73797</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_ScienceTherapeuticAreas_TC007.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">invokeURL</td> 
  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:32:25</td>   <td>234899</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread8@1135846825</td>   <td></td> </tr>
<tr bgcolor="b0fd6f">  <td>25/04/02 13:28:39</td>   <td>9502</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_Medicines_TC008.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">invokeURL</td> 
  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="b0fd6f">  <td>25/04/02 13:28:59</td>   <td>29817</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_Medicines_TC008.verifyMedicinesPage()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">verifyMedicinesPage</td> 
  <td>Thread4@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:41</td>   <td>71646</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:58</td>   <td>88499</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:33</td>   <td>3077</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:29:39</td>   <td>69606</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@1719578079</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/02 13:28:05</td>   <td>-24483</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_GlobalSearchFuntionality_TC002.globalSearchFuntionality()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8]">globalSearchFuntionality</td> 
  <td>Thread1@1135846825</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/02 13:29:54</td>   <td>84754</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_SciencePipeline_TC006.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">invokeURL</td> 
  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:28:01</td>   <td>-29029</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/02 13:28:01</td>   <td>-29029</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_HomePage_TC001.verifyMenuComponents()[pri:4, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">verifyMenuComponents</td> 
  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:31:40</td>   <td>190662</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread8@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:32:25</td>   <td>234899</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread8@1135846825</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:32:12</td>   <td>222234</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:32:26</td>   <td>236162</td> <td title="&lt;&lt;BaseTest.afterSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&lt;&lt;afterSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1640296160</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/02 13:27:57</td>   <td>-32937</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_HomePage_TC001.verifyFooter()[pri:3, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">verifyFooter</td> 
  <td>Thread2@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:32:15</td>   <td>225080</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:27:11</td>   <td>-78969</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@1719578079</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/02 13:30:16</td>   <td>105971</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@1719578079</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 13:32:26</td>   <td>236809</td> <td title="&lt;&lt;CRAFTLiteTestCase.tearDownTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36]">&lt;&lt;tearDownTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1640296160</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/02 13:31:04</td>   <td>153883</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gilead_CompanyStatement_TC005.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096]">invokeURL</td> 
  <td>Thread7@1719578079</td>   <td></td> </tr>
</table>
