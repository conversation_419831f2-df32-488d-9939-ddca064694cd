package init;

import java.io.IOException;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.config.Configurator;
import org.testng.TestNG;
import org.testng.xml.XmlClass;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlTest;

import com.gilead.base.ALMExecutionListener;
import com.gilead.config.FrameworkAssertion;
import com.gilead.maintenance.ExcelDataAccessforxlsm;
import com.gilead.maintenance.ResultSummaryManager;
import com.gilead.utils.Util;
import com.gilead.reports.FrameworkParameters;

public class ALMRunner {

	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());
	
	public static void main(String[] args)
			throws ClassNotFoundException, InstantiationException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException {

        // Disable all logging messages
		Configurator.setRootLevel(org.apache.logging.log4j.Level.OFF);
        
		if (args.length != 2) {
			throw new FrameworkAssertion(
					"Required arguments (TestScenario, TestCase, PackageName, TestID) are not passed.");
		}
		String currentTestCase = args[0];
		String intTestID = args[1];
		String strPackageName = "";
		ResultSummaryManager.getInstance().setRelativePath();
		logger.info("Execution Started");
		try {			
			InputStream stream = ALMExecutionListener.class.getResourceAsStream("/Global Settings.properties");
			Properties properties = new Properties();
			try {
				properties.load(stream);
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			String sheetName = properties.getProperty("Scope");
			ExcelDataAccessforxlsm runManagerAccess = new ExcelDataAccessforxlsm(
					FrameworkParameters.getInstance().getRelativePath() + Util.getFileSeparator() + "src" + Util.getFileSeparator()
							+ "test" + Util.getFileSeparator() + "resources",
					"Run Manager");
			runManagerAccess.setDatasheetName(sheetName);

			runManagerAccess.setDatasheetName(sheetName);
			
			String[] keys = {"TestScenario", "TestCase", "TestInstance", "Package"};
			List<Map<String, String>> values = runManagerAccess.getValues(keys);

			for (int currentTestInstance = 0; currentTestInstance < values.size(); currentTestInstance++) {

				Map<String, String> row = values.get(currentTestInstance);
				String strTestCaseName = row.get("TestCase");

				if (strTestCaseName.equalsIgnoreCase(currentTestCase)) {
					strPackageName = row.get("Package");
					
				}
			}
			TestNG testng = new TestNG();
			List<XmlSuite> suites = new ArrayList<XmlSuite>();
			List<XmlTest> tests = new ArrayList<XmlTest>();
			List<String> listeners = new ArrayList<String>();
			XmlSuite suite = new XmlSuite();
			suite.setName("ALMRunner_Suite");
			suite.setListeners(listeners);
			XmlTest test = new XmlTest(suite);
			test.setName("Test under ALMRunner_Suite");
			test.addParameter("RunID", intTestID);
			List<XmlClass> xmlClasses = new ArrayList<XmlClass>();
			xmlClasses.add(new XmlClass(strPackageName + "." + currentTestCase));
			test.setXmlClasses(xmlClasses);
			tests.add(test);
			suites.add(suite);
			
			testng.setXmlSuites(suites);
			testng.run();

		} catch (Exception e) {

			System.out.println(e.getMessage());

		}
	}

}
