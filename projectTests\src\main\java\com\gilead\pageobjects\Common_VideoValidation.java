package com.gilead.pageobjects;

import org.openqa.selenium.By;

/**
 * UI Map for Common Objects for Video Validation
 */
public class Common_VideoValidation {

	
	public By videoLink;
	public By videoBtn;
	public static final By cookiesCloseBtn = By.xpath("//div[@id='onetrust-close-btn-container']/button");
	public static final By checkEnableBtn=By.xpath("*//span[text()='(check all that apply)']");
	public static final By videoPlayBtn = By.xpath("//i[contains(@class,'hs-play')]");
	public static final By vidoes = By.xpath("(//video[@class='w-100 active '])");
	public static final By videoPlayIcon = By.xpath("//div[contains(@class,'PlayButton_modules')]");
	public static final By videoCloseBtn = By.xpath("//a[@class='icon icon--small icon-close-green btn-modal-close']|(//a[@data-bs-dismiss='modal'])");
	public static final By langSwitchBtn = By.xpath("(//a[@class='everett_PT_2'])|(//a[@class='kevin_esp'])|(//a[@class='steve_esp'])");
	public static final By langSwitchSingleBtn = By.xpath("(//a[@class='everett_PT_2'])|(//a[@class='kevin_esp'])|(//a[@class='steve_esp'])");
	public static final String videoPlayer = "div.vp-video";
	public static final By formBox = By.xpath("(//div[@class='modal-content']//form)[2]");
	public static final By closeBtn = By.xpath("(.//*[@role='dialog']//*[text()='Close'])|(.//button[@aria-label='Close'])|(//a[@class='open-close-btn'])|(//div[@class='modal-header']//a[@aria-label='Close video'])");
	public static final By activeVidoes = By.cssSelector("video.w-100.active,video.gl-video-embeded,div#movie_player");
	public static final String customVideo = "(//div[@class='profileVideo custom-video'])|(//div[contains(@class,'gl-video-content')])|(//div[contains(@class,'vid-container')])|(.//div[(@class='vp-video')])|(//div[@class='video']//video)|(//div[contains(@class,'video-window')])|(//div[@class='video-wrap' or @class='video-player__poster'])|(//div[@class='video-callout__video'])|(//div[@class='video-player'])|(//div[@class='richtext-container px-4 w-auto mw-100 mb-4 pl-md-0 ml-md-0']/video)";
	public static final By selectHCP = By.xpath("(//div[@role='dialog']//div[@class='modal-content text-'])|(//div[@class='hcp-modal-content-container'])|(//div[@class='modal-content'])");
	public static final String activeVideo = "video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video";
		public Common_VideoValidation(String strVideoBtn) {
		
			String videoContent ="(.//div[(@class='"+ strVideoBtn +"')])";
			//|(//img[contains(@alt,'"+strVideoBtn+"')])|(//div[@class='cta-wrapper']//a[contains(@class,'"+ strVideoBtn +"')])[2] -> Gilead_UK - |(//video)"|//"+strVideoBtn+"
			videoLink = By.xpath("(.//div[contains(@class,'"+ strVideoBtn +"')])|(//img[contains(@alt,'"+strVideoBtn+"')])|(//div[@class='cta-wrapper']//a[contains(@class,'"+ strVideoBtn +"')])[2]|(//a[contains(@class,'"+strVideoBtn+"')])");
			//videoBtn = By.xpath("//div[@class='d-none d-lg-block cmp-herobanner__cta']//a[contains(@class,'link-to-video')]//following-sibling::span[contains(text(),'"+strVideoBtn+"')]");
			videoBtn = By.xpath("//div[contains(@class,'cta-wrapper')]//a[contains(@class,'link-to-video') and contains(@data-label,'"+strVideoBtn+"')]");
	}
}
