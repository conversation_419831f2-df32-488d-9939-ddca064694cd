<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website" tests="2" failures="1" timestamp="2025-04-22T11:31:50 IST" time="414.046" errors="0">
  <testcase name="invokeURL" time="44.155" classname="com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website"/>
  <system-out/>
  <testcase name="verifyNavigationHeaders" time="369.891" classname="com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website">
    <failure type="com.gilead.config.FrameworkAssertion" message="Error -  Navigation Menu is not enabled in the page: &quot;https://www.yescarta.com/managing-side-effects#infusion-and-monitoring&quot; even after waiting for 10 Seconds">
      <![CDATA[com.gilead.config.FrameworkAssertion: Error -  Navigation Menu is not enabled in the page: "https://www.yescarta.com/managing-side-effects#infusion-and-monitoring" even after waiting for 10 Seconds
at com.gilead.reports.Report.updateTestLog(Report.java:423)
at com.gilead.maintenance.ALMFunctions.ThrowException(ALMFunctions.java:252)
at com.gilead.maintenance.WebDriverUtil.waitUntilElementEnabled(WebDriverUtil.java:559)
at com.gilead.maintenance.CommonActionsAndFunctions.clickByJS(CommonActionsAndFunctions.java:501)
at businesscomponents.CommonFunctions.navigateAndVerifyMenu(CommonFunctions.java:3920)
at businesscomponents.CommonFunctions.accessNavigationMenu(CommonFunctions.java:4983)
at com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders(Navigation_and_Functionality_Verification_for_YesCARTA_Website.java:29)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- verifyNavigationHeaders -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website -->
