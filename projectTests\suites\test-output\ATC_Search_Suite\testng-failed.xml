<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite guice-stage="DEVELOPMENT" name="Failed suite [ATC_Search_Suite]">
  <test thread-count="5" name="Test under EntireSuite_Suite1(failed)" parallel="classes">
    <parameter name="RunID" value="0"/>
    <classes>
      <class name="com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="invokeURL"/>
          <include name="verifyManagingSideEffectsSite"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site -->
      <class name="com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="verifyClinicalTrialResults"/>
          <include name="beforeClass"/>
          <include name="invokeURL"/>
          <include name="beforeMethod"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site -->
      <class name="com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="verifyReceivingYescarta"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="invokeURL"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site -->
      <class name="com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="verifyNavigationHeaders"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="invokeURL"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website -->
      <class name="com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="PDFverification"/>
          <include name="invokeURL"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website -->
      <class name="com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="invokeURL"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="PIandNavigationVerification"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website -->
      <class name="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="invokeURL"/>
          <include name="verifyYescartaSite"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site -->
      <class name="com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site">
        <methods>
          <include name="invokeURL"/>
          <include name="verifySupportAndResourcesSite"/>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site -->
      <class name="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="navigationVerification"/>
          <include name="afterClass"/>
          <include name="invokeURL"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website -->
    </classes>
  </test> <!-- Test under EntireSuite_Suite1(failed) -->
</suite> <!-- Failed suite [ATC_Search_Suite] -->
