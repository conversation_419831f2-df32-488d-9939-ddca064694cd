package com.gilead.pageobjects;

import org.openqa.selenium.By;

/**
 * UI Map for Common Objects
 */
public class Common {

	public By textbox, textarea, dropdown, combobox, navigationList, button, dropDownOptions, comboboxValue,
			listViewValue;
	public By globalSearchResults, inlineEditbutton, readOnlyFields, checkbox;
	public By tab, dropdownValue, loginVerification, message, radiobutton, label, globalSearchResultVal,
			defaultDropdownValue;
	public By dateField, dateFieldValue;
	public By listValue, highlightsItem, sectionTitle, registartionTaskDetails, registartionTaskStatus,
			cellOrderDetails;
	// DX Somke Test cases New
	public By verifyLinks, confirmationPopup, acceptDeclineBtn, componetentExists, preCondition, subMenu, menu,
			registerLinks;
	// Additional patterns for Excel column mappings
	public By pdfFileLink, menuLinkStatic, staticMenuLocator, homePageTitle, pageTitles, navigateBack, componentExists, generalComponent, profileName, elementToCheck;
	// DX Functional Testcases
	public By link, subNavigationLink, surveyBtn, surveyMessage;
	public By medicalCenterIcon, medicalCenterName, medicalCenterLoc, medicalCenterLink, confirmationBtn;
	public By searchResults, confirmationLeavingBtn, spanText, accordianBtn, resourcesBtn;
	// DX Functional Testcases
	// div[@id='locator-search']//input[@class='mapboxgl-ctrl-geocoder--input']
	public static final By cookiesHandleBtn = By.xpath(
			"//*[@id='onetrust-accept-btn-handler']|//div[@id='onetrust-accept-btn-handler']/button|//*[contains(@id, 'cookiesjsr')]//*[contains(text(), 'Accept all')]|(//div[@id='onetrust-close-btn-container']/button)|(.//button[@id='onetrust-reject-all-handler'])");
	public static final By cookiesAcceptBtn = By.xpath(
			"//*[@id='onetrust-accept-btn-handler']|//div[@id='onetrust-accept-btn-handler']/button|//*[contains(@id, 'cookiesjsr')]//*[contains(text(), 'Accept all')]");
	public static final By nextBtn = By.xpath(
			"(//div[contains(@class,'cta-wrapper')]//input[@value='Next'])|(//div[contains(@class,'cta-wrapper')]//input[@value='Close'])");
	public static final By checkEnableBtn = By.xpath("*//span[text()='(check all that apply)']");

	public static final By atcTextBox = By.xpath("//div[@id='locator-search']//input");
	public static final By timeAndDateLabel = By.xpath("//div[@class='locator__detail__times']");
	public static final By generalInfoLabel = By.xpath("(//div[@class='locator__detail__factbox__label'])[1]");
	public static final By generalInfoValues = By.xpath(
			"//div[@class='locator__detail__factbox__label']//following-sibling::div[@class='locator__detail__factbox__value']");
	public static final By directionsAndArrivalLabel = By
			.xpath("(//div[@class='locator__detail__section open']//following-sibling::h2)[1]");
	public static final By carTConsulationAndCareTeam = By
			.xpath("(//div[@class='locator__detail__section open']//following-sibling::h2)[2]");
	public static final By printBtn = By.xpath("//button[@id='locator-print']");
	public static final By useLocatorTool = By.xpath("//div[@class='cta-wrapper']/a[text()='Use the locator tool']");
	public static final By searchForm = By.xpath("//div[@id='searchForm']//input");
	public static final By searchBtn = By.xpath("//div[@class='search-section']/a[@class='search-toggler']");
	public static final By searchShowResults = By
			.xpath("//div[@class='content search-results']//following-sibling::p[@class='dark-purple']");
	public static final By searchLoadMoreBtn = By.xpath("//button[@id='searchLoadMore']");
	public static final By btnShare = By.xpath("//*[contains(text(),'SHARE') or text()='Share']//parent::a");
	public static final By btnFeedBack = By.xpath("//div[@class='feedback-btn js-sendfeedback']");
	public static final By btnDownload = By.xpath(
			"//div[@class='download-content']//following-sibling::a[contains(@href,'.pdf')]|(//div[contains(@class,'boxed-content')]//following-sibling::a[contains(@href,'.pdf')])");
	public static final By seeMoreBtn = By.xpath("//span[@class='locator__detail__section__icon']");
	public static final By cookiesCloseBtn1 = By
			.xpath("(//div[@id='onetrust-close-btn-container']/button)|(.//button[@id='onetrust-reject-all-handler'])");// |(//a[@class='open-close-btn'])
	// DX CICD Smoke Test cases
	public By headerMenu, subMenuDD, searchTag, selectStatusMenu, recruitmentStatus, headerLabel;
	public static final By heroBannerContent = By.xpath("//div[@class='herobanner-content']");
	public static final By studyStatusBtn = By.xpath("//button[@id='studyStatusApply']");
	public static final By searchResultCount = By.xpath(
			"//h1[@class='search-results-title']//*[text()]|(//h6[contains(text(),'Locations')]//following::*[text()])");

	public static final By learnmoreBtn = By.xpath("(//button[contains(@class,'search-card-button')])");

	public static final By contactUsPopup = By.xpath("//div[@id='contactUsPopup']//div[@class='modal-content']");
	public static final By contactUsCloseBtn = By
			.xpath("//*[contains(text(),'Contact Center Numbers')]//following-sibling::button[@class='close']");
	public static final By gileadLogo = By.xpath("//a[@class='navbar-brand']");
	public static final By searchCard = By.xpath("//span[@class='search-highlight']");
	public static final By cardCloseBtn = By.xpath("//div[@id='profilePopup']//button");
	public By headerElement, learnMoreLink, accordianTitle, searchCardComponentBtn, searchResultsCount, cardlink,
			linkUnderHeaderSection, allLinksUnderHeaderSection, headerSection, paitentLinks;
	// Yescarta Test Cases
	public By navigationMenu, simpleGenericText, genericNavigationMenu, genericTextUsingClass,
			genericLinkedTextUsingAncestorClass, simpleLinkedText, genericLinkedTextUsingAncestorID, linkedText,
			simpleH3Header, genericElementWithID, genericElementWithClass, genericTextElementWithClass,
			genericElementWithTitle, genericLinkedTextUsingClass, genericLinkedTextUsingDataPaneID,
			genericTextUsingAncestorID, genericTextUsingAncestorClass, genericPlayVideo, genericIFrame,
			genericIFrameUsingAncestorClass, genericText, caregiverRoleNavigationMenu, imageWithAlternateText,
			simpleH2Header, imageWithAlternateTextUsingAncestorID, imageWithAncestorSiblingText,
			genericElementWithExactClass, textWithDataPaneID;
	public static final By videoClose_btn = By.xpath(
			"//a[contains(.,'Important Facts')]/following-sibling::a[contains(.,'Close')]|//*[@class='gl-cmp-modal modal modal-video fade show']//button");
	public static final By managingSideEffects_drpdwn = By
			.xpath("//a[contains(.,'About YESCARTA')]/following::ul//a[contains(., 'Managing side effects')]");

	/**
	 * Constructor to handle multiple locators using one parameter
	 * 
	 * @param strLabel - Parameters for xpath locator
	 */

	public Common(String strLabel) {
		// DX CICD Smoke Test cases
		allLinksUnderHeaderSection = By.xpath("//*[contains(text(),'" + strLabel + "')]/following-sibling::div//a");
		searchResultsCount = By.xpath("//h1[@class='search-results-title']//*[text()='" + strLabel
				+ "']|(//h6[contains(text(),'Locations')]//following::*[text()='" + strLabel
				+ "'])|(//h2[contains(text(),'" + strLabel + "')]//following::div[@class='content-batch'])");
		searchCardComponentBtn = By.xpath("//div[contains(@id,'searchResuts')]//ancestor::button[contains(text(),'"
				+ strLabel + "')]|(//div[contains(@class,'btn-red d-none')]//ancestor::button[contains(text(),'"
				+ strLabel + "')])");
		paitentLinks = By.xpath(
				"//*[contains(text(),'Patient Steering Council Advisors for TNBC')]/following::*[contains(@data-gtm,'"
						+ strLabel + "')]");
		accordianTitle = By.xpath("(//*[contains(text(),'" + strLabel + "')])/following::div[@id='headingFAQ']");
		headerElement = By.xpath("//h2[contains(text(),'" + strLabel + "')]|(//h3[contains(text(),'" + strLabel
				+ "')])|(//h2[contains(text(),'" + strLabel
				+ "')]//following::div[@class='card-container'])|(//*[text()='" + strLabel + "'])|(//div[@class='"
				+ strLabel + "']//h1)|(//*[contains(text(),'" + strLabel + "')])[last()]");
		headerSection = By.xpath("(//*[contains(text(),'" + strLabel + "')])[last()]");
		learnMoreLink = By.xpath("//h2[text()='" + strLabel
				+ "']//following::div[@class='card-body-footer']//a|(//h3[text()='" + strLabel
				+ "']//following::div[@class='multi-cta-container']//a)|(//div[contains(@class,'bc-featured-card')]//h2[text()='"
				+ strLabel + "']//following::div[@class='owl-item active']//a)|(//h2[contains(text(),'" + strLabel
				+ "')]//following::div[@class='ourvision-link']//a)|(//h2[contains(text(),'" + strLabel
				+ "')]//following::a[@class='btn-trial btn-white'])|(//*[text()='" + strLabel
				+ "']//following::div[@class='bottom-info']//a)");
		headerMenu = By
				.xpath("//ul[contains(@class,'navbar-nav justify-content')]//following-sibling::a[contains(text(),'"
						+ strLabel + "')]");
		subMenuDD = By.xpath("//div[contains(@id,'header-dropdown-menu')]//following-sibling::a[contains(text(),'"
				+ strLabel + "')]");

		searchTag = By.xpath("//div[@id='searchTags']//*[text()='" + strLabel + "']");
		selectStatusMenu = By.xpath("//button[contains(text(),'" + strLabel + "')]");
		recruitmentStatus = By.xpath("//*[@data-key='" + strLabel + "']");
		headerLabel = By.xpath("//div[@class='row justify-content-center']//h2[contains(text(),'" + strLabel + "')]");
		// DX Functional Testcases
		surveyMessage = By.xpath(".//*[contains(text(), \"" + strLabel
				+ "\")]|(//div[@class='modal-interceptsurvey__title']//*[contains(text(),'" + strLabel + "')])");
		surveyBtn = By.xpath("//div[@class='field-block']//input[@value='" + strLabel + "']");
		accordianBtn = By
				.xpath("//div[contains(@class,'accordion-header')]//span[contains(text(),'" + strLabel + "')]");
		resourcesBtn = By
				.xpath("//*[text()='" + strLabel + "']//parent::div[@class='cta-wrapper']|(//*[contains(text(),'"
						+ strLabel + "')]//following-sibling::input)");
		// ATC Search
		medicalCenterIcon = By.xpath("//div[text()='" + strLabel
				+ "']//parent::div[@class='locator__center__details']//following-sibling::div[@class='locator__center__photo']");
		medicalCenterName = By
				.xpath("//div[@class='locator__centers']//following-sibling::div[text()='" + strLabel + "']");
		medicalCenterLoc = By.xpath("//div[@data-center='" + strLabel + "']");
		medicalCenterLink = By.xpath("(//a[contains(text(),'" + strLabel + "')])[last()]");
		confirmationBtn = By.xpath("(//*[text()='" + strLabel + "'])|(//div[@class='modal-footer']//a[contains(text(),'"
				+ strLabel + "')])|(//div[@id='modalLeavingSiteInter']//following-sibling::span[text()='" + strLabel
				+ "'])");
		confirmationLeavingBtn = By
				.xpath("//div[@id='modalLeavingSiteInter']//following-sibling::span[text()='" + strLabel + "']");
		link = By.xpath("(//nav[@id='primaryNavigation']//a[normalize-space(text())='" + strLabel
				+ "'])|(//a[@aria-label='Leave this website' and contains(text(),'" + strLabel + "')])|(//button[@id='"
				+ strLabel + "'])|(//a[text()='" + strLabel
				+ "'])|(//a[contains(@class,'cta-link') and contains(text(),'" + strLabel
				+ "')])|(//a[@class='hit-area']//span[text()='" + strLabel
				+ "'])|(//a[@class=' nav-link']/span[contains(text(),'" + strLabel
				+ "')])|(//div[@class='feedback-btn js-sendfeedback']//a[@aria-label='" + strLabel
				+ "'])|(//div[@class='rating-wrapper']//input[@data-sc-field-name='" + strLabel
				+ "'])|(//div[contains(@class,'" + strLabel
				+ "')]//a[contains(@data-gtm,'Download/Print')])|(//ul[contains(@class,'gl-unorder-list')]//a[contains(text(),' "
				+ strLabel + " ')])|(//div[@class='cta-wrapper']//a[normalize-space(text())='" + strLabel
				+ "'])|//a[normalize-space(text())='" + strLabel + "']|(//ul[@class='navbar-nav ']//*[contains(text(),'"
				+ strLabel + "')])");
		radiobutton = By.xpath("(//input[@data-sc-field-name='" + strLabel
				+ "'])|(//label[@class='form-check-label']//child::input[@id='" + strLabel
				+ "'])|(*//label[contains(@class,'radio-inline')]//span[text()='" + strLabel + "'])");
		subNavigationLink = By
				.xpath("//ul[@class='dropdown-menu show']//following-sibling::a[contains(text(),'" + strLabel + "')]");
		searchResults = By.xpath("//h2[contains(normalize-space(.),'" + strLabel + "')]/span");

		// second or condition for textbox is to handle dialog textbix in CMTS
		textbox = By.xpath("(//input[@data-sc-field-name='" + strLabel
				+ "'])|(//label[@class='form-check-label']//child::input[@id='" + strLabel + "'])|(//input[@id='"
				+ strLabel + "'])|(//*[contains(text(),'" + strLabel + "')]//following-sibling::span)");
		textarea = By.xpath("(//input[@placeholder ='" + strLabel + "'])");
		dropdown = By.xpath("(.//*[text()='" + strLabel + "']//ancestor::div[contains(@class,'form-group')]//select)");
		combobox = By.xpath("//input[@id=string(//label[text()='" + strLabel + "']/@for)]");

		button = By.xpath("(//div[@id='searchTags']//*[text()='" + strLabel + "'])|(//*[@data-key='" + strLabel
				+ "'])|(//ul[contains(@class,'navbar-nav justify-content')]//following-sibling::a[contains(text(),'"
				+ strLabel + "')])|(//button[contains(text(),'" + strLabel + "')])|(//a[contains(text(),'" + strLabel
				+ "')])|(//a[contains(@data-gtm,'" + strLabel + "')])|(//button[@id='" + strLabel
				+ "'])|(//button[@data-nctid='" + strLabel + "'])");

		checkbox = By.xpath("(.//*[text()='" + strLabel
				+ "']//ancestor::lightning-input[contains(@class,'form-element')]//input[@type='checkbox'])|(.//*[text()='"
				+ strLabel
				+ "']//ancestor::*[contains(@class,'tableRow') or contains(@class,'checkbox')]//input[@type='checkbox'])|(//label[@for='"
				+ strLabel + "'])");
		tab = By.xpath(
				"(//ul[@id='utilityButtons' or @id='utilityNav']//following-sibling::*//a[contains(normalize-space(.),'"
						+ strLabel + "')])");

		message = By.xpath(".//*[text()='" + strLabel + "']");
		radiobutton = By.xpath("(//input[@data-sc-field-name='" + strLabel
				+ "'])|(//label[@class='form-check-label']//child::input[@id='" + strLabel
				+ "'])|(*//label[contains(@class,'radio-inline')]//span[text()='" + strLabel + "'])|(//*[text()='"
				+ strLabel + "']//ancestor::div[contains(@class,'radio-button')])");

		dateFieldValue = By.xpath("//td[@data-date='" + strLabel + "']//div[@class='fc-event-title']");
		spanText = By.xpath("//h1[contains(text(),'" + strLabel + "')]|//h2[contains(text(),'" + strLabel
				+ "')]|//h3[contains(.,'" + strLabel + "')]");
		// Yescarta
		navigationMenu = By
				.xpath("//nav[contains(@class,'navbar navbar-expand-lg')]//following-sibling::a[contains(text(),'"
						+ strLabel + "')]");
		genericPlayVideo = By.xpath("//p[contains(., '" + strLabel
				+ "')]/parent::div/following-sibling::a|//*[contains(@data-title,'" + strLabel + "')]");
		genericIFrame = By.xpath("//iframe[@id = '" + strLabel + "']");
		linkedText = By.xpath("//a[.='" + strLabel + "']|//a[text()='" + strLabel + "']");
		simpleGenericText = By.xpath("//*[contains(.,'" + strLabel + "')]");
		simpleH3Header = By.xpath("//h3[contains(.,'" + strLabel + "')]");
		simpleH2Header = By.xpath("//h2[contains(.,'" + strLabel + "')]");
		genericElementWithID = By.xpath("//*[@id='" + strLabel + "']");
		genericElementWithClass = By.xpath("//*[contains(@class, '" + strLabel + "')]");
		genericElementWithExactClass = By.xpath("//*[(@class = '" + strLabel + "')]");
		genericTextElementWithClass = By.xpath("//*[contains(@class, '" + strLabel + "')][text()]");
		genericElementWithTitle = By.xpath("//*[@title =  '" + strLabel + "']");
		simpleLinkedText = By.xpath("//a[contains(.,'" + strLabel + "')]");
		genericIFrameUsingAncestorClass = By.xpath("//*[contains(@class, '" + strLabel + "')]//iframe");
		imageWithAlternateText = By.xpath("//img[contains(@alt,'" + strLabel + "')]");
		imageWithAncestorSiblingText = By.xpath("//*[contains(text(), '" + strLabel + "')]/following-sibling::*//img");
		// DX Smoke Test cases
		// Supported label names for GileadCommercial_Smoke consolidated tests:
		// GileadHIV: AboutUs, Events, HIVTesting, HIVCareContinuum, HomePage
		// P2PTalkPrep: PeerInsights, VerifyComponentPresent
		// TalkPrep: AboutPrep, PrescribingPrep, Resources, StayInformed, WhoIsPrepFor, PDFDownload
		// UKHCV: GileadAndElimination, BeFreeOfHepC, EliminationPartners, WhatIsHepC, HepCKi, EliminationResources
		verifyLinks = By.xpath("(//li[@class='menu-level-1']//*[contains(text(),'" + strLabel
				+ "')])|(//a[@class='ui-tabs-anchor'][contains(text(),'" + strLabel
				+ "')])|(//a[@class='nav-link ']//*[contains(text(),'" + strLabel
				+ "')])");
		confirmationPopup = By.xpath("//a[contains(normalize-space(.), '" + strLabel + "')]");
		acceptDeclineBtn = By.xpath("//a[contains(text(), '" + strLabel + "')]");
		componetentExists = By.xpath("(//div[contains(@class,'row justify-content-center')]//*[contains(text(),'"
				+ strLabel + "')])|(//label[contains(text(),'" + strLabel
				+ "')])|(//div[@class='hero-banner-content']//a[@href='" + strLabel + "'])");
		preCondition = By.xpath("//*[contains(text(),'" + strLabel + "')]");
		menu = By.xpath("//a[contains(@id,'navbarDropdownMenuLink')]//*[contains(text(),'" + strLabel + "')]");
		subMenu = By.xpath("//a[@class='dropdown-item ']//*[contains(text(),'" + strLabel + "')]");
		registerLinks = By.xpath("//div[contains(@class,'card-wrapper')]//*[contains(text(),'" + strLabel + "')]");
		// Additional patterns for PDF files and common elements
		pdfFileLink = By.xpath("//a[contains(@href,'pdf') and contains(text(),'" + strLabel + "')]|(//a[contains(@href,'.pdf') and contains(@aria-label,'" + strLabel + "')])|(//a[contains(@download,'" + strLabel + "')])");
		menuLinkStatic = By.xpath("//nav//a[contains(text(),'" + strLabel + "')]|(//ul[contains(@class,'navbar-nav')]//a[contains(text(),'" + strLabel + "')])|(//div[contains(@class,'menu')]//a[contains(text(),'" + strLabel + "')])");
		staticMenuLocator = By.xpath("//nav[contains(@class,'navbar')]//a[contains(text(),'" + strLabel + "')]|(//div[contains(@class,'menu-container')]//a[contains(text(),'" + strLabel + "')])|(//ul[contains(@class,'nav')]//a[contains(text(),'" + strLabel + "')])");
		homePageTitle = By.xpath("//title[contains(text(),'" + strLabel + "')]|(//h1[contains(text(),'" + strLabel + "')])|(//div[contains(@class,'page-title') and contains(text(),'" + strLabel + "')])");
		pageTitles = By.xpath("//title[contains(text(),'" + strLabel + "')]|(//h1[contains(text(),'" + strLabel + "')])|(//h2[contains(text(),'" + strLabel + "')])|(//div[contains(@class,'title') and contains(text(),'" + strLabel + "')])");
		navigateBack = By.xpath("//a[contains(@class,'back') and contains(text(),'" + strLabel + "')]|(//button[contains(@class,'back') and contains(text(),'" + strLabel + "')])|(//a[contains(text(),'Back') and contains(text(),'" + strLabel + "')])");
		componentExists = By.xpath("//div[contains(@class,'component') and contains(text(),'" + strLabel + "')]|(//section[contains(@class,'section') and contains(text(),'" + strLabel + "')])|(//article[contains(text(),'" + strLabel + "')])|(//span[contains(text(),'" + strLabel + "')])|(//p[contains(text(),'" + strLabel + "')])");
		generalComponent = By.xpath("//*[contains(text(),'" + strLabel + "')]|(//div[contains(@class,'" + strLabel + "')])|(//span[contains(@class,'" + strLabel + "')])|(//section[contains(@class,'" + strLabel + "')])");
		profileName = By.xpath("//div[contains(@class,'profile')]//span[contains(text(),'" + strLabel + "')]|(//div[contains(@class,'user')]//span[contains(text(),'" + strLabel + "')])|(//a[contains(@class,'profile') and contains(text(),'" + strLabel + "')])");
		elementToCheck = By.xpath("//div[contains(@data-testid,'" + strLabel + "')]|(//span[contains(@data-testid,'" + strLabel + "')])|(//section[contains(@data-testid,'" + strLabel + "')])|(//article[contains(@data-testid,'" + strLabel + "')])");

	}

	/**
	 * Constructor to handle multiple locators using two parameters
	 * 
	 * @param strLabel1 - Parameters for xpath locator
	 * @param strLabel2 - Parameters for xpath locator
	 */
	public Common(String strLabel1, String strLabel2) {
		cardlink = By.xpath(
				"//*[text()='" + strLabel1 + "']//following::*[contains(text(),'" + strLabel2 + "')]//ancestor::a");
		dropDownOptions = By.xpath("((.//*[text()='" + strLabel1
				+ "']//ancestor::div[contains(@class,'form-group')]//select)//option[text()='" + strLabel1 + "'])");
		defaultDropdownValue = By.xpath(
				"//button[@id=string(//label[text()='" + strLabel1 + "']/@for)]//span[text()='" + strLabel2 + "']");
		textbox = By.xpath(
				"//*[text()='" + strLabel1 + "']//parent::*//label[text()='" + strLabel2 + "']//parent::*//input");
		linkUnderHeaderSection = By.xpath("//*[contains(text(),'" + strLabel1 + "')]/following::*[@href='" + strLabel2
				+ "']|(//*[contains(text(),'" + strLabel1 + "')]/ancestor::a[@href='" + strLabel2
				+ "'])|(//*[contains(text(),'" + strLabel1 + "')]/following::a[text()='" + strLabel2
				+ "'])|(//*[contains(text(),'" + strLabel1 + "')]/following::*[contains(@data-gtm,'" + strLabel2
				+ "')])");
		genericTextUsingAncestorClass = By
				.xpath("//*[contains(@class, '" + strLabel1 + "')]//*[contains(text(), '" + strLabel2 + "')]");
		genericTextUsingClass = By
				.xpath("//*[contains(@class, '" + strLabel1 + "') and contains(text(), '" + strLabel2 + "')]");
		genericTextUsingAncestorID = By
				.xpath("//*[contains(@id, '" + strLabel1 + "')]//*[contains(text(), '" + strLabel2 + "')]");
		genericLinkedTextUsingAncestorID = By
				.xpath("//*[contains(@id, '" + strLabel1 + "')]//a[contains(text(), '" + strLabel2 + "')]");
		genericLinkedTextUsingAncestorClass = By
				.xpath("//*[contains(@class, '" + strLabel1 + "')]//a[contains(text(), '" + strLabel2 + "')]");
		genericText = By.xpath("//" + strLabel1 + "[contains(.,'" + strLabel2 + "')]");
		caregiverRoleNavigationMenu = By
				.xpath("(//nav[contains(@class,'navbar navbar-expand-lg')]//following-sibling::*[contains(text(),'"
						+ strLabel1 + "')])[" + strLabel2 + "]");
		imageWithAlternateTextUsingAncestorID = By
				.xpath("//*[contains(@id, '" + strLabel1 + "')]//img[contains(@alt,'" + strLabel2 + "')]");
		textWithDataPaneID = By.xpath("//*[@data-pane-id='" + strLabel1 + "' and contains(.,'" + strLabel2 + "')]");
	}

	/**
	 * Constructor to handle multiple locators using two parameters
	 * 
	 * @param strLabel1 - Parameters for xpath locator
	 * @param strLabel2 - Parameters for xpath locator
	 * @param strLabel3 - Parameters for xpath locator
	 */
	public Common(String strLabel1, String strLabel2, String strLabel3) {
		genericTextUsingAncestorID = By
				.xpath("//ancestor::*[@id='" + strLabel1 + "']//" + strLabel2 + "[contains(.,'" + strLabel3 + "')]");
	}

}
