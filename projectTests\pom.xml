<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>framework</groupId>
		<artifactId>gileadTestNG</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>projectTests</artifactId>
	<groupId>gilead.apps</groupId>
	<version>0.0.1-SNAPSHOT</version>

	<properties>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<selenium.version>4.1.2</selenium.version>
		<testng.version>7.4.0</testng.version>
		<aspectj.version>1.8.10</aspectj.version>
		<java.client.appium>6.1.0</java.client.appium>
		<testClass>default</testClass>
		<almRUNID>0</almRUNID>
		<environment>Application_URL</environment>
	</properties>

	<dependencies>
		<dependency>
			<groupId>gilead.apps</groupId>
			<artifactId>gileadCore</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>project</finalName>
		<testResources>
			<testResource>
				<directory>${project.basedir}/src/test/resources</directory>
			</testResource>
		</testResources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0-M5</version>
				<configuration>
					<suiteXmlFiles>
						<suiteXmlFile>${suiteXmlFile}</suiteXmlFile>
					</suiteXmlFiles>
					<systemPropertyVariables>
						<environment>${environment}</environment>
					</systemPropertyVariables>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.2.0</version>
				<configuration>
					<excludes>
						<exclude>**/logs/**</exclude>
					</excludes>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<mainClass>init.ALMRunner</mainClass>
							<classpathPrefix>.</classpathPrefix>
							<classpathLayoutType>repository</classpathLayoutType>
						</manifest>
					</archive>
				</configuration>
				<executions>
					<execution>
						<phase>prepare-package</phase>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>3.5.0</version>
				<executions>
					<execution>
						<id>alm-execution</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>
								<argument>-classpath</argument>
								<classpath />
								<argument>init.ALMRunner</argument>
								<argument>${testClass}</argument>
								<argument>${almRUNID}</argument>
							</arguments>
						</configuration>
					</execution>
					<execution>
						<id>testng-generation</id>
						<goals>
							<goal>java</goal>
						</goals>
						<configuration>
							<mainClass>smoketest.DynamicTestNGRunner</mainClass>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>