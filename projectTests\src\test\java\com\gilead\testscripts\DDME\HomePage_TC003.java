package com.gilead.testscripts.DDME;

import org.testng.annotations.AfterClass;
import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

import com.gilead.base.BaseTest;
import com.gilead.pageobjects.Common;

import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class HomePage_TC003 extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.invokeUrl();
			objCommonFunctions.closeCookies();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyReusableCompontes_HomePage() throws Exception {
		try {
			long lngMinTimeOutInSeconds = Long.parseLong(properties.getProperty("MinObjectSyncTimeout"));


			objCommonFunctions.verifyObjectExistance(Common.heroBannerContent,
					"Envisioning a healthier world for all people", "exist", "HeroBanner", driver.getTitle());
			objCommonFunctions.clickMenu("Find A Trial", "Virology", driver.getTitle());
			objCommonFunctions.navigationSteps(driver.getTitle());
			objCommonFunctions.click(Common.studyStatusBtn, lngMinTimeOutInSeconds, "Button", " Apply Filters", "Click",
					true);
			objCommonFunctions.verifyObjectExistance(Common.searchResultCount, "48", "exist", "Count",
					driver.getTitle());
			objCommonFunctions.enterTextarea("Keyword or condition", "Cancer", driver.getTitle());
			objCommonFunctions.clickButton("Find Trials", driver.getTitle());
			objCommonFunctions.verifyObjectExistance(Common.searchCard, "Cancer", "exist", "Card Compontent",
					driver.getTitle());
			objCommonFunctions.click(Common.gileadLogo, lngMinTimeOutInSeconds, "Button", " Gilead Logo", "Click",
					true);
			objCommonFunctions.verifyAllLinkUnderHeaderSection("Want to learn more about our clinical trials?");
			objCommonFunctions.verifyAllLinkUnderHeaderSection("Featured Trials");
			objCommonFunctions
					.verifyAllLinkUnderHeaderSection("Our vision is to create a healthier world for all people.");
			objCommonFunctions.verifyObjectExistance(new Common("Discover our WHY?").headerElement, "Discover our WHY?",
					"exist", "Header Element", driver.getTitle());

			// objCommonFunctions.validateAccordian("Discover our WHY?");
			objCommonFunctions.validateAccordian("Your Questions Answered");
		} finally {
			checkErrors();
		}
	}

	@AfterClass
	public void resetHashMap() {
		CommonFunctions.resetHashMap();
	}

}
