<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS" tests="3" failures="0" timestamp="2025-05-13T20:35:54 IST" time="73.674" errors="2">
  <testcase name="afterMethod" time="25.242" classname="com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS">
    <error type="org.openqa.selenium.WebDriverException" message="Timed out waiting for driver server to stop.
Build info: version: &#039;4.1.2&#039;, revision: &#039;9a5a329c5a&#039;
System info: host: &#039;Z1VD7STHPRDN255&#039;, ip: &#039;************&#039;, os.name: &#039;Windows 10&#039;, os.arch: &#039;amd64&#039;, os.version: &#039;10.0&#039;, java.version: &#039;1.8.0_291&#039;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [ef0e09041baa69c443d80b1ea80ee2fe, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:63219}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:63219/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: ef0e09041baa69c443d80b1ea80ee2fe">
      <![CDATA[org.openqa.selenium.WebDriverException: Timed out waiting for driver server to stop.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [ef0e09041baa69c443d80b1ea80ee2fe, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:63219}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:63219/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: ef0e09041baa69c443d80b1ea80ee2fe
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:132)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:660)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.ExecutionException: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: driver.version: unknown
at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:128)
... 29 more
Caused by: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: driver.version: unknown
at org.openqa.selenium.os.OsProcess.waitFor(OsProcess.java:174)
at org.openqa.selenium.os.CommandLine.waitFor(CommandLine.java:127)
at org.openqa.selenium.remote.service.DriverCommandExecutor.lambda$execute$2(DriverCommandExecutor.java:122)
at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)
... 3 more
]]>
    </error>
  </testcase> <!-- afterMethod -->
  <system-out/>
  <testcase name="invokeURL" time="18.330" classname="com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS"/>
  <system-out/>
  <testcase name="toVerifyYescartaTecartusREMSSite" time="30.102" classname="com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS">
    <error type="java.lang.RuntimeException" message="Error while verifying file download &#039;yescarta-pi&#039; on page &#039;REMS&#039;: Downloaded file &#039;yescarta-pi&#039; not found in path &#039;C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles&#039;">
      <![CDATA[java.lang.RuntimeException: Error while verifying file download 'yescarta-pi' on page 'REMS': Downloaded file 'yescarta-pi' not found in path 'C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles'
at businesscomponents.CommonFunctions.handleFileDownload(CommonFunctions.java:4156)
at businesscomponents.CommonFunctions.verifyYescartaTecartusREMSSite(CommonFunctions.java:5732)
at com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS.toVerifyYescartaTecartusREMSSite(To_Download_Resources_From_YescartaTecartusREMS.java:29)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.RuntimeException: Downloaded file 'yescarta-pi' not found in path 'C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles'
at businesscomponents.CommonFunctions.handleFileDownload(CommonFunctions.java:4152)
... 17 more
]]>
    </error>
  </testcase> <!-- toVerifyYescartaTecartusREMSSite -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS -->
