package com.gilead.testscripts.Smoke_Testcases;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;


public class Vekluryhcp_Stay_Informed_Signup_Form_TC003 extends BaseTest {

	CommonFunctions objCommonFunctions;


	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyCriticalComponents() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPage();
			//objCommonFunctions.validateComponentExists();
		} finally {
			checkErrors();
		}
	}
}
