package businesscomponents;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.channels.FileLock;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Queue;
import java.util.Random;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFFormulaEvaluator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.Alert;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Keys;
import org.openqa.selenium.NoAlertPresentException;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.remote.CapabilityType;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.applitools.eyes.TestResults;
import com.gilead.applitools.ApplitoolsTestResultsHandler;
import com.gilead.base.RunContext;
import com.gilead.config.FrameworkException;
import com.gilead.maintenance.CommonActionsAndFunctions;
import com.gilead.maintenance.FileLockMechanism;
import com.gilead.pageobjects.Common;
import com.gilead.pageobjects.Common_SmokeTestcases;
import com.gilead.pageobjects.Common_VideoValidation;
import com.gilead.reports.ScriptHelper;
import com.gilead.reports.Status;
import com.gilead.utils.Util;

/**
 * public class CommonFunctions extends CommonActionsAndFunctions
 *
 * @return
 */
public class CommonFunctions extends CommonActionsAndFunctions {

	/**
	 * public CommonFunctions(ScriptHelper scriptHelper)
	 *
	 * @return
	 */
	public CommonFunctions(ScriptHelper scriptHelper) {
		super(scriptHelper);
		windowName = new HashMap<String, String>();
	}

	/**
	 * public CommonFunctions(RunContext runContext)
	 *
	 * @return
	 */
	public CommonFunctions(RunContext runContext) {
		super(runContext);
		windowName = new HashMap<String, String>();
	}

	/**
	 * public final long timeOutInSeconds =
	 * Long.parseLong(properties.getProperty("ObjectSyncTimeout"));
	 *
	 * @return
	 */
	public final long timeOutInSeconds = Long.parseLong(properties.getProperty("ObjectSyncTimeout"));
	/**
	 * public final long lngPagetimeOutInSeconds =
	 * Long.parseLong(properties.getProperty("PageLoadTimeout"));
	 *
	 * @return
	 */
	public final long lngPagetimeOutInSeconds = Long.parseLong(properties.getProperty("PageLoadTimeout"));
	/**
	 * public final long lngMinTimeOutInSeconds =
	 * Long.parseLong(properties.getProperty("MinObjectSyncTimeout"));
	 *
	 * @return
	 */
	public final long lngMinTimeOutInSeconds = Long.parseLong(properties.getProperty("MinObjectSyncTimeout"));
	/**
	 * public final long loadingWindowTimeOut =
	 * Long.parseLong(properties.getProperty("LoadingWindowTimeOut"));
	 *
	 * @return
	 */
	public final long loadingWindowTimeOut = Long.parseLong(properties.getProperty("LoadingWindowTimeOut"));
	/**
	 * public final long CtrlTimeOut =
	 * Long.parseLong(properties.getProperty("UploadControlTimeout"));
	 *
	 * @return
	 */
	public final long CtrlTimeOut = Long.parseLong(properties.getProperty("UploadControlTimeout"));
	/**
	 * public final long staleTimeOut =
	 * Long.parseLong(properties.getProperty("StaleTimeOut"));
	 *
	 * @return
	 */
	public final long staleTimeOut = Long.parseLong(properties.getProperty("StaleTimeOut"));
	/**
	 * public final long invisibilityTimeout =
	 * Long.parseLong(properties.getProperty("InvisibilityTimeout"));
	 *
	 * @return
	 */
	public final long invisibilityTimeout = Long.parseLong(properties.getProperty("InvisibilityTimeout"));
	/**
	 * protected static final Logger logger =
	 * Logger.getLogger(CommonFunctions.class.getName());
	 *
	 * @return
	 */
	protected static final Logger logger = Logger.getLogger(CommonFunctions.class.getName());
	/**
	 * public static int waitTimeOver = 0;
	 *
	 * @return
	 */
	public static int waitTimeOver = 0;
	/**
	 * private HashMap<String, String> windowName;
	 *
	 * @return
	 */
	private HashMap<String, String> windowName;
	/**
	 * private final ReadWriteLock readWriteLock = new ReentrantReadWriteLock();
	 *
	 * @return
	 */
	private final ReadWriteLock readWriteLock = new ReentrantReadWriteLock();
	/**
	 * private final Lock readLock = readWriteLock.readLock();
	 *
	 * @return
	 */
	private final Lock readLock = readWriteLock.readLock();
	boolean blnAlreadyLoggedIn = false;
	/**
	 * private static final ThreadLocal<List<String>> lstCheckPass =
	 * ThreadLocal.withInitial(ArrayList::new);
	 *
	 * @return
	 */
	private static final ThreadLocal<List<String>> lstCheckPass = ThreadLocal.withInitial(ArrayList::new);
	/**
	 * private static final ThreadLocal<List<String>> lstCheckFail =
	 * ThreadLocal.withInitial(ArrayList::new);
	 *
	 * @return
	 */
	private static final ThreadLocal<List<String>> lstCheckFail = ThreadLocal.withInitial(ArrayList::new);

	/**
	 * private static ThreadLocal<HashMap<String, String>> threadlocalHashmap =
	 * ThreadLocal.withInitial(HashMap::new);
	 *
	 * @return
	 */
	private static ThreadLocal<HashMap<String, String>> threadlocalHashmap = ThreadLocal.withInitial(HashMap::new);

	ApplitoolsTestResultsHandler testResultHandler = null;
	TestResults testResult = null;

	List<String> strLstDuplicate = new ArrayList<String>();
	List<String> strLstValidLinks = new ArrayList<String>();
	List<String> strLstRedirectionLinks = new ArrayList<String>();
	List<String> strLstCilentErrorLinks = new ArrayList<String>();
	List<String> strLstServerErrorLinks = new ArrayList<String>();
	List<String> strLstExpectionList = new ArrayList<String>();

	int iTotal2XX = 0;
	int iTotal3XX = 0;
	int iTotal4XX = 0;
	int iTotal5XX = 0;

	/**
	 * Function to Add Values in HashMap.
	 *
	 * @param key
	 * @param value
	 */
	/**
	 * public static void addToHashMap(String key, String value)
	 *
	 * @return
	 */
	public static void addToHashMap(String key, String value) {
		HashMap<String, String> hashmap = threadlocalHashmap.get();
		hashmap.put(key, value);
	}

	/**
	 * Function to return HashMap 'value' for the 'Key'.
	 *
	 * @param key
	 * @return String Value
	 */
	/**
	 * public static String getHashMapValue(String key)
	 *
	 * @return
	 */
	public static String getHashMapValue(String key) {
		HashMap<String, String> hashmap = threadlocalHashmap.get();
		if (!hashmap.get(key).isEmpty() && hashmap != null)
			return hashmap.get(key);
		else if (hashmap == null)
			return "HashMap Null";
		else {

			return "Key Not Found In HashMap,Please Check the Key";
		}
	}

	/**
	 * Function to remove Value from HashMap
	 *
	 * @param key
	 */
	/**
	 * public static void removeHashMap(String key)
	 *
	 * @return
	 */
	public static void removeHashMap(String key) {
		HashMap<String, String> hashmap = threadlocalHashmap.get();
		hashmap.remove(key);
	}

	/**
	 * Function to remove Value from HashMap
	 *
	 *
	 */
	/**
	 * public static void resetHashMap()
	 *
	 * @return
	 */
	public static void resetHashMap() {

		HashMap<String, String> hashmap = threadlocalHashmap.get();
		hashmap.clear();
	}

	/**
	 * Function to pause the execution for the specified time period
	 *
	 * @param milliSeconds The wait time in milliseconds
	 * @param elementName  The name of the element Name for which the execution is
	 *                     paused
	 * @param pageName     Page Name in which Element is available
	 */
	/**
	 * public void waitFor(long milliSeconds)
	 *
	 * @return
	 */
	public void waitFor(long milliSeconds) {
		try {
			Thread.sleep(milliSeconds);
		} catch (InterruptedException e) {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Wait for Element",
					"Script should be able to set to pause for the mentioned duration",
					"Error - Unable to wait for elementName in page:" + driver.getTitle(), true);
		}
	}

	/**
	 * Method to fill input form in page
	 *
	 * @param strParameters - Parameters for the form
	 * @param strPageName   - Name of the page in which form exists @
	 */
	/**
	 * public void FillInputForm(String strSheetName, String strColumnName, String
	 * strScenario, String strDelimiter,
	 *
	 * @return
	 */
	public void FillInputForm(String strSheetName, String strColumnName, String strScenario, String strDelimiter,
			boolean blnIncludeDelimiter, String strPageName) {
		String strInputParameters = getConcatenatedStringFromExcel(strSheetName, "Input_Parameters",
				"Concatenate_Flag_Input", strScenario, "~", true, true);
		String[] arrParameters_Vs_Value = strInputParameters.split("~");

		for (int index = 0; index < arrParameters_Vs_Value.length; index++) {
			strInputParameters = getConcatenatedStringFromExcel(strSheetName, "Input_Parameters",
					"Concatenate_Flag_Input", strScenario, "~", true, true);
			arrParameters_Vs_Value = strInputParameters.split("~");
			if (arrParameters_Vs_Value[index].trim().length() > 0) {
				String[] arrCriteria = StringUtils.split(arrParameters_Vs_Value[index], ";");

				String strLabel = "";
				String strElementType = "";
				String strValue = "";
				String strStorage = "";
				for (int k = 0; k < arrCriteria.length; k++) {
					if (arrCriteria[k].trim().length() > 0) {
						switch (StringUtils.substringBefore(arrCriteria[k], "=").toLowerCase()) {
						case "element label":
							strLabel = StringUtils.substringAfter(arrCriteria[k], "=");
							break;
						case "element type":
							strElementType = StringUtils.substringAfter(arrCriteria[k], "=").toLowerCase();
							break;
						case "element value":
							strValue = StringUtils.substringAfter(arrCriteria[k], "=");
							break;
						case "section name":
							StringUtils.substringAfter(arrCriteria[k], "=");
							break;
						case "page name":
							strPageName = StringUtils.substringAfter(arrCriteria[k], "=");
							break;
						case "window name":
							StringUtils.substringAfter(arrCriteria[k], "=");
							break;
						case "storage":
							strStorage = StringUtils.substringAfter(arrCriteria[k], "=");
							break;

						default:
							ALMFunctions.ThrowException("Test Data",
									"Test Data must be provided in the pre-defined format",
									"Unhandled case " + StringUtils.substringBefore(arrCriteria[k], "="), false);
						}
					}
				}
				if (strElementType.trim().length() > 0) {
					if (strStorage.contains("getmap")) {
						strValue = getHashMapValue(strValue);
					}
					switch (strElementType.toLowerCase()) {
					case "textarea":
						enterTextarea(strLabel, strValue, strPageName);
						break;
					case "textbox":
						enterTextbox(strLabel, strValue, strPageName);
						break;
					case "dropdown":
						selectDropdown(strLabel, strValue, strPageName);
						break;
					case "combobox":
						enterComboBox(strLabel, strValue, strPageName);
						break;
					case "radiobutton":
						selectRadioButton(strValue, strPageName);
						break;
					case "inlineeditbutton":
						clickInlineEditButton(strLabel, strPageName);
						break;
					case "checkbox":
						clickCheckBox(strLabel, strPageName);
						break;
					case "click":
						clickButton(strValue, strPageName);
						break;
					case "screenshot":
						driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
						report.updateTestLog(strLabel,
								"Screenshot taken should be Taken for " + strLabel + "in " + strPageName,
								"Screenshot taken is Taken for " + strLabel + "in " + strPageName, Status.PASS);

						break;
					default:
						ALMFunctions.ThrowException("Test Data",
								"Only Pre-Defined Fields Type must be provided in the test data sheet",
								"Error - Unhandled Field Type " + strElementType, false);
						break;
					}

				}
				/*
				 * if(strStorage.trim().length() > 0){
				 * dataTable.putData("Parametrized_Checkpoints", strStorage, strValue);
				 * report.updateTestLog("Store Field Value",
				 * strValue+" should be stored in the "+strStorage,
				 * strValue+" is stored in the "+strStorage, Status.DONE); }
				 *
				 * if(strStorage.trim().length()>0 && strValue.trim().isEmpty()){
				 * dataTable.putData("Parametrized_Checkpoints", strStorage, strValue); }
				 */
				if (strStorage != null && strStorage.trim().length() > 0
						&& !strStorage.toLowerCase().contains("hashmap")
						&& !strStorage.toLowerCase().contains("getmap")) {
					/*
					 * dataTable.putData(StringUtils.substringBefore(strStorage,
					 * "!"),StringUtils.substringAfter(strStorage, "!"), strValue);
					 * report.updateTestLog("Store Field Value", strValue +
					 * " should be stored in the " + strStorage, strValue + " is stored in the " +
					 * StringUtils.substringAfter(strStorage, "!"), Status.DONE);
					 */
					dataTable.putData("Parametrized_Checkpoints", strStorage, strValue);
					report.updateTestLog("Store Field Value", strValue + " should be stored in the " + strStorage,
							strValue + " is stored in the " + strStorage, Status.DONE);
				} else if (strStorage != null && strStorage.trim().length() > 0
						&& strStorage.toLowerCase().contains("hashmap")) {
					if (!StringUtils.substringAfter(strStorage, "!").isEmpty()) {
						addToHashMap(StringUtils.substringAfter(strStorage, "!").trim(), strValue);
						dataTable.putData("Parametrized_Checkpoints",
								StringUtils.substringAfter(strStorage, "!").trim(), strValue);
					} else {
						addToHashMap(strLabel.trim(), strValue);
					}
				}
			}

		}

	}

	/**
	 * Function to validate the all accordian in webpage
	 *
	 * @param strUrl
	 * @param strPageName
	 */
	/**
	 * public void validateAccordian(String strAccordianName)
	 *
	 * @return
	 */
	public void validateAccordian(String strAccordianName) {

		List<WebElement> all_Accordians = driver.findElements(new Common(strAccordianName).accordianTitle);
		String strPageName = driver.getTitle();
		for (WebElement webElement : all_Accordians) {
			String strAccordianBtn = webElement.getText();
			pagescroll(webElement, strPageName);
			click(webElement, lngMinTimeOutInSeconds, webElement.getText(), "Accordian", strPageName, true);

			if (objectExists(webElement, "isEnabled", loadingWindowTimeOut, "Login", "Button", "Home Page", false)) {
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Accordian Functioanality",
						strAccordianBtn + "-Accordian should able to expand and Collapose",
						strAccordianBtn + " - Accordian is able to expand and Collapose", true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Accordian Functioanality",
						strAccordianBtn + "-Accordian should able to expand and Collapose",
						strAccordianBtn + " - Accordian is not able to expand and Collapose", true);
			}
		}
	}

	/**
	 * Function to navigate to the URL
	 */
	/**
	 * public void launchURL()
	 *
	 * @return
	 */
	public void launchURL() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strUrl = dataTable.getData("SmokeCICD", "Application_URL");
		driver.get(strUrl);
		String strHomePageTitle = dataTable.getData("SmokeCICD", "HomePageTitle");
		boolean verifySiteIsUpAndRunning = verifySiteIsUpAndRunning(strHomePageTitle);
		if (verifySiteIsUpAndRunning) {
			if (!(dataTable.getData("SmokeCICD", "TC_ID").equalsIgnoreCase("yescartahcp"))) {
				handleCookiePopup();
			}
			if (!dataTable.getData("SmokeCICD", "PreCondition").isEmpty()) {
				preCondition();
			}
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		}

	}

	/**
	 * Function to verify the site is up and Running
	 */
	/**
	 * public boolean verifySiteIsUpAndRunning(String strActualTitle)
	 *
	 * @return
	 */
	public boolean verifySiteIsUpAndRunning(String strActualTitle) {
		String strExpectedTitle = driver.getTitle();
		boolean flag = false;
		if (strExpectedTitle.contains(strActualTitle)) {
			report.updateTestLog("Invoke URL",
					" URL:<a href = " + strExpectedTitle + ">" + strExpectedTitle + "</a>" + "Should be invoked",
					" URL:<a href = " + strActualTitle + ">" + strActualTitle + "</a>" + " is invoked", Status.PASS);
			flag = true;
		} else if (strExpectedTitle.contains("404") || strExpectedTitle.contains("Not Found")) {
			System.out.println("=> Client Error: 404 Not Found");
			report.updateTestLog("Verify URLs using Page title", "Page should be broken and 404 should not be found",
					" Page is broken and Client Error: 404 Not Found", Status.FAIL);
			driver.quit();
		} else if (strExpectedTitle.contains("500") || strExpectedTitle.contains("Internal Server Error")) {
			System.out.println("=> Server Error: 500 Internal Server Error");
			report.updateTestLog("Verify URLs using Page title", "Page should be broken and 500 should not be found",
					" Page is down and Server Error: 500 Internal Server Error", Status.FAIL);
		} else if (!strExpectedTitle.contains(strActualTitle)) {
			report.updateTestLog("Verify Home Page URL ", "Page should be Up and Running",
					"Page is redirected to another site :" + driver.getTitle(), Status.DONE);
		} else {
			System.out.println("=> No server or client errors detected based on the page title.");
		}
		return flag;
	}

// Exlude the URL's ends with this values
	/**
	 * private static final Set<String> ignoreExtension = new
	 * HashSet<>(Arrays.asList("pptx", "csv", "xlsx", "ashx", "pdf",
	 *
	 * @return
	 */
	private static final Set<String> ignoreExtension = new HashSet<>(Arrays.asList("pptx", "csv", "xlsx", "ashx", "pdf",
			"css", "js", "png", "mp4", "zip", "video", "Video", "= 1"));

	/**
	 * public Set<String> extractAllLinksFromMenu()
	 *
	 * @return
	 */
	public Set<String> extractAllLinksFromMenu() {
		By menuLocator = By.xpath(dataTable.getData("SmokeCICD", "StaticMenuLocator"));
		Set<String> linkUrls = null;
		String strTestCaseName = dataTable.getData("SmokeCICD", "TC_ID");
		if (strTestCaseName.equalsIgnoreCase("gnet") || strTestCaseName.equalsIgnoreCase("gnet_knet")) {
// use By locator and use object exisit
			By hamburgerIcon = By.xpath("//i[@aria-label='hamburger']");
			if (objectExists(hamburgerIcon, "isDisplayed", lngMinTimeOutInSeconds, "Logo Check", "Image",
					driver.getTitle(), false)) {
				click(hamburgerIcon, lngMinTimeOutInSeconds, "Hamburger Icon", "Menu", strTestCaseName, true);
			}
		}
// Extract all <a> tags from the menu bar WebElement
		List<WebElement> links = driver.findElements(menuLocator);
// Collect href attributes from <a> tags, excluding empty href attributes
		Set<String> uniqueLinks = links.stream().map(link -> link.getAttribute("href"))
				.filter(href -> href != null && !href.isEmpty() && !href.equals("javascript:;"))
				.collect(Collectors.toSet());
		if (strTestCaseName.equalsIgnoreCase("gnet") || strTestCaseName.equalsIgnoreCase("gnet_knet")) {
			String strGnet = "https://gileadconnect.sharepoint.com/";
			linkUrls = uniqueLinks.stream().filter(Objects::nonNull).filter(url -> !url.contains("#"))
					.filter(url -> !url.contains("pdf")).filter(url -> url.contains(strGnet))
					.filter(url -> ignoreExtension.stream().noneMatch(url::endsWith)).collect(Collectors.toSet());
		} else {
			String strRootUrl = dataTable.getData("SmokeCICD", "Application_URL");
			linkUrls = uniqueLinks.stream().filter(Objects::nonNull).filter(url -> !url.contains("#"))
					.filter(url -> !url.contains("pdf")).filter(url -> url.startsWith(strRootUrl))
					.filter(url -> ignoreExtension.stream().noneMatch(url::endsWith)).collect(Collectors.toSet());
		}
// Print the unique links
		linkUrls.forEach(System.out::println);
		return linkUrls;
	}

	/**
	 * Method to removeoverlay in thew webpage
	 *
	 *
	 * @return
	 */
	public void removeOverlay() {
		String strOverlayLocator = dataTable.getData("FunctionalRegression", "RemoveOverlay");
		JavascriptExecutor jsExecutor = (JavascriptExecutor) driver.getWebDriver();
		jsExecutor.executeScript("document.querySelector('" + strOverlayLocator + "').style.display='none';");
	}

	/**
	 * public void verifyLogoAndUrl(String strURL, String strLogo)
	 *
	 * @return
	 */
	public void verifyLogoAndUrl(String strURL, String strLogo) {
		driver.navigate().to(strURL);
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		preCondition();
		By verifyLogo = By.xpath(dataTable.getData("SmokeCICD", "LogoLocator"));
		String strTestCaseName = dataTable.getData("SmokeCICD", "TC_ID");
		closeCookies();
		String strCurrentUrl = driver.getCurrentUrl();
		if (strCurrentUrl.contains(strURL)) {
			report.updateTestLog("Verify WebPage URL",
					strURL + " given URL should be matched with launched URL: " + strCurrentUrl,
					strURL + " given URL is matched with launched URL: " + strCurrentUrl, Status.DONE);
// Check if the Logo is present
			if (objectExists(verifyLogo, "isDisplayed", lngMinTimeOutInSeconds, "Logo Check", "Image",
					driver.getTitle(), false)) {
				report.updateTestLog("Verify Logo in the WebPage",
						strTestCaseName + " Logo should be Present in WebPage",
						strTestCaseName + " Logo is Present in the WebPage", Status.PASS);
			} else {
				report.updateTestLog("Verify Logo in the WebPage",
						strTestCaseName + " Logo should be Present in WebPage",
						strTestCaseName + " Logo is not Present in the WebPage", Status.FAIL);
			}
		} else {
			report.updateTestLog("Verify WebPage URL",
					strURL + " given URL should be matched with launched URL: " + strCurrentUrl,
					strURL + " given URL is not matched with launched URL: " + strCurrentUrl, Status.FAIL);
		}

	}

	/**
	 * public void verifyRandomPageNavigation()
	 *
	 * @return
	 */
	public void verifyRandomPageNavigation() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strRootURL = dataTable.getData("SmokeCICD", "Application_URL");
		Set<String> allLinks;
// Get the Crawler condition from SmokeCICD
		boolean crawlEnabled = dataTable.getData("SmokeCICD", "CrawlEnabled").equalsIgnoreCase("Yes");
// if crawler is enabled then store all the URL's into a Set
		if (crawlEnabled) {
			allLinks = crawl(strRootURL);
			logger.info("Crawled and found " + allLinks.size() + " unique links.");
			checkRandomLinks(allLinks, 5);
		} else {
			Set<String> allLinksFromMenu = extractAllLinksFromMenu();
			checkRandomLinks(allLinksFromMenu, 5);
		}

	}

	/**
	 * public List<String> checkRandomLinks(Set<String> allLinks, int
	 * numberOfLinksToCheck)
	 *
	 * @return
	 */
	public List<String> checkRandomLinks(Set<String> allLinks, int numberOfLinksToCheck) {
		Random random = new Random();
		List<String> linksList = new ArrayList<>(allLinks);
		List<String> randomLinks = new ArrayList<>();
		int linksChecked = 0;
		String strLogo = dataTable.getData("SmokeCICD", "LogoLocator");
		while (linksChecked < numberOfLinksToCheck && !linksList.isEmpty()) {
// Select a random link
			String link = linksList.remove(random.nextInt(linksList.size()));
			randomLinks.add(link);
			verifyLogoAndUrl(link, strLogo);
			linksChecked++;
		}
		return randomLinks;
	}

	/**
	 * This method is the get all the URL's for the particular website
	 *
	 * @return - Set of URL's based on filter conditions
	 */
	/**
	 * public Set<String> crawl(String rootUrl)
	 *
	 * @return
	 */
	public Set<String> crawl(String rootUrl) {
		long startTime = System.currentTimeMillis();
		Queue<String> urlQueue = new LinkedList<>();
		Set<String> visitedUrls = new LinkedHashSet<>();

		urlQueue.add(rootUrl);
		visitedUrls.add(rootUrl);

		logger.info("Crawling started from root URL: " + rootUrl);

		try {
			while (!urlQueue.isEmpty()) {
				String currentUrl = urlQueue.remove();
				try {
					driver.get(currentUrl);
					logger.info("Crawling: " + currentUrl);
// Filter conditions by iterating each URL's
					List<WebElement> linkElements = driver.findElements(By.tagName("a"));
					List<String> linkUrls = linkElements.stream().map(el -> el.getAttribute("href"))
							.filter(Objects::nonNull).filter(url -> !url.contains("#"))
							.filter(url -> !url.contains("pdf")).filter(url -> url.startsWith(rootUrl))
							.filter(url -> ignoreExtension.stream().noneMatch(url::endsWith))
							.map(CommonFunctions::normalizeURL).filter(visitedUrls::add).collect(Collectors.toList());
					urlQueue.addAll(linkUrls);
					logger.fine("Found " + linkUrls.size() + " new links on " + currentUrl);
				} catch (NoSuchElementException e) {
					logger.warning("No links found on: " + currentUrl + ", skipping...");
				}
			}
		} catch (Exception e) {
			logger.severe("Exception during crawl: " + e.getMessage());
		}

		logger.info("Crawling completed. Total unique URLs found: " + visitedUrls.size());
		long endTime = System.currentTimeMillis();
		logger.info("Crawling time taken: " + (endTime - startTime) + " ms");
		return visitedUrls;
	}

// Support method to remove the "/" in the end of every URL's to avoid
// duplication
	/**
	 * public static String normalizeURL(String url)
	 *
	 * @return
	 */
	public static String normalizeURL(String url) {
		return url.endsWith("/") ? url.substring(0, url.length() - 1) : url;
	}

	/**
	 * This method will handle the cookie pop up after pre-condition
	 *
	 * @WebDriver - driver for current test case
	 * @testCaseData - Contains the test case data for each test case
	 */
	/**
	 * public void handleCookiePopup()
	 *
	 * @return
	 */
	public void handleCookiePopup() {
		try {
			if (objectExists(Common.cookiesHandleBtn, "isEnabled", lngMinTimeOutInSeconds, "Cookies Popup",
					"Close Button", driver.getTitle(), false)) {
				click(Common.cookiesHandleBtn, lngMinTimeOutInSeconds, "Cookies Close Popup", "Button",
						driver.getTitle(), false);
				report.updateTestLog("Accept Cookies Button", " Cookies Button should be Accept",
						" Cookies Button is Accepted", Status.DONE);
			} else if (objectExists(Common.cookiesAcceptBtn, "isEnabled", lngMinTimeOutInSeconds, "Cookies Popup",
					"Accept Button", driver.getTitle(), false)) {
				click(Common.cookiesAcceptBtn, lngMinTimeOutInSeconds, "Cookies Accept Popup", "Button",
						driver.getTitle(), false);
				report.updateTestLog("Accept Cookies Button", " Cookies Button should be Accept",
						" Cookies Button is Accepted", Status.DONE);
			} else {
				logger.warning("Could not find cookie popup. Proceeding without handling.");
			}
		} catch (Exception e) {
			logger.warning("Exception occurs while handling cookie");
		}
	}

	/**
	 * This method will handle the pre-condition before taking the screenshot
	 *
	 * @WebDriver - driver for current test case
	 * @testCaseData - Contains the test case data for each test case
	 */
	/**
	 * public void preCondition()
	 *
	 * @return
	 */
	public void preCondition() {
		if (!dataTable.getData("FunctionalRegression", "PreCondition").isEmpty()) {
// Get the pre-condition locator and click if present
			By preCondition = new Common(dataTable.getData("FunctionalRegression", "PreCondition")).preCondition;
			if (objectExists(preCondition, "isEnabled", lngMinTimeOutInSeconds, "Per Condition", "Button",
					"Accept Button", false)) {
				click(preCondition, lngMinTimeOutInSeconds, "Pre Condition", "Button", driver.getTitle(), true);
			}
		}
// Verify if login check is applicable for current test case
		if (!dataTable.getData("FunctionalRegression", "LoginLocators").isEmpty()) {
// perform the login steps if applicable
			login();
		} else {
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		}
	}

	/**
	 * This method will handle the login functionality
	 *
	 * @WebDriver - driver for current test case
	 * @testCaseData - Contains the test case data for each test case
	 */
	/**
	 * public void login()
	 *
	 * @return
	 */
	public void login() {
		String[] getUserDetails = dataTable.getData("UIConfigurations", "LoginLocators").split(";");
		if (getUserDetails.length == 3) {
			By user = By.xpath(getUserDetails[0]);
			By password = By.xpath(getUserDetails[1]);
			By submit = By.xpath(getUserDetails[2]);
			String strUserName = dataTable.getData("UIConfigurations", "UserName");
			String strPassword = dataTable.getData("UIConfigurations", "Password");
			if (!strUserName.isEmpty() && !strPassword.isEmpty()) {
// sendKeys(driver, user, waitForElement, testCaseData.get("UserName"));
				sendkeys(user, lngMinTimeOutInSeconds, "UserName", "Input", driver.getTitle(), "Text Field", true);
				try {
					sendkeys(password, lngMinTimeOutInSeconds, "Password", "Input", driver.getTitle(), "Text Field",
							true);
				} catch (Exception e) {
// TODO Auto-generated catch block
					e.printStackTrace();
				}
				clickByJS(submit, lngMinTimeOutInSeconds, "Button", "Submit Button", driver.getTitle(), true);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
// update the login Check to avoid the repeated check
// testCaseData.put("loginCheck", "Completed");
			} else {
				logger.severe("User details not found");
				throw new IllegalArgumentException("User Details is not provided");
			}
		} else {
			logger.severe("Locators details missing for user details");
			throw new IllegalArgumentException("Locators details missing for user details");
		}
	}

	/**
	 * public void verifyShareButton()
	 *
	 * @return
	 */
	public void verifyShareButton() {
		List<WebElement> all_Accordians = driver.findElements(Common.btnShare);
		String strPageName = driver.getTitle();
		for (WebElement webElement : all_Accordians) {
			String strAccordianBtn = webElement.getText();
			pagescroll(webElement, strPageName);
			clickByJS(webElement, lngMinTimeOutInSeconds, webElement.getText(), "Accordian", strPageName, true);

			if (objectExists(webElement, "isEnabled", loadingWindowTimeOut, "Login", "Button", "Home Page", false)) {
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Share Button in the webPage",
						strAccordianBtn + "-Share Button should be displayed as Facebook\n" + "linkedin\n" + "X\n"
								+ "Email\n" + "Copy link",
						strAccordianBtn + " - Share is able to displayed as Facebook\r\n" + "linkedin\n" + "X\n"
								+ "Email\n" + "Copy link",
						true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Share Button in the webPage",
						strAccordianBtn + "-Share Button should be displayed as Facebook\n" + "linkedin\n" + "X\n"
								+ "Email\n" + "Copy link",
						strAccordianBtn + " - Share is not able to displayed as Facebook\n" + "linkedin\n" + "X\n"
								+ "Email\n" + "Copy link",
						true);
			}
			clickByJS(webElement, lngMinTimeOutInSeconds, webElement.getText(), "Accordian", strPageName, true);
		}
	}

	/**
	 * Function to validate the success message using current Page Url
	 *
	 * @param strUrl
	 * @param strPageName
	 */
	/**
	 * public void verifyMessage(String strUrl, String strPageName)
	 *
	 * @return
	 */
	public void verifyMessage(String strUrl, String strPageName) {
		driverUtil.waitUntilPageUrlContains(strUrl, lngPagetimeOutInSeconds);
		String strCurrentUrl = driver.getCurrentUrl();
		if (strCurrentUrl.contains(strUrl)) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Success Message",
					strUrl + "-Success Page should be displayed", strUrl + " - Success Page is displayed", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Success Message",
					strUrl + "-Success Page should be displayed", strUrl + " - Success Page is not displayed", true);
		}
	}

// DX Functional Test cases
	/**
	 * Method to Navigate to menu and submenu
	 *
	 * @param elementName- name of the menu to be clicked
	 * @param strValue     - name of the submenu to be clicked
	 * @param strPageName  - Page Name in which the control is available
	 * @return No return value
	 */

	/**
	 * public void navigateToMenu(String elementName, String strValue, String
	 * strPageName)
	 *
	 * @return
	 */
	public void navigateToMenu(String elementName, String strValue, String strPageName) {
		By navigation = null;
		By subNavigation = null;
		Common nav1 = new Common(elementName);
		navigation = nav1.link;
		Common nav2 = new Common(strValue);
		subNavigation = nav2.subNavigationLink;
		if (objectExists(navigation, "isDisplayed", lngMinTimeOutInSeconds, elementName, "Link", strPageName, true)) {
			driverUtil.waitUntilStalenessOfElement(navigation, strPageName);
			mouseOverandClick(navigation, lngMinTimeOutInSeconds, elementName, "Link", strPageName, false);
			if (objectExists(subNavigation, "isDisplayed", lngMinTimeOutInSeconds, strValue, "Link", strPageName,
					false)) {
				click(subNavigation, lngMinTimeOutInSeconds, strValue, "Link", strPageName, true);

			}
		}
	}

	/**
	 * Function to close cookies button in web page.
	 *
	 * @return No return value
	 */
	/**
	 * public void closeCookies()
	 *
	 * @return
	 */
	public void closeCookies() {
		if (objectExists(Common.cookiesHandleBtn, "isDisplayed", lngMinTimeOutInSeconds, "login Verication Message",
				"Message", driver.getTitle(), false)) {
			click(Common.cookiesHandleBtn, lngMinTimeOutInSeconds, "Cookies Close Button", "Button", driver.getTitle(),
					false);
			report.updateTestLog("Accept Cookies Button", " Cookies Button should be invoked",
					" Cookies Button is invoked", Status.DONE);
		}
	}

	/**
	 * Function to click continue button in web page.
	 *
	 * @return No return value
	 */
	/**
	 * public void continueOnYescartaHCP()
	 *
	 * @return
	 */
	public void continueOnYescartaHCP() {
		if (objectExists(new Common("ctn-btn", "Continue").genericTextUsingClass, "isDisplayed", lngMinTimeOutInSeconds,
				"Continue", "Button", driver.getTitle(), false)) {
			click(new Common("ctn-btn", "Continue").genericTextUsingClass, lngMinTimeOutInSeconds, "Continue", "Button",
					driver.getTitle(), false);
			report.updateTestLog(
					"Launch https://www.yescartahcp.com/2l-large-b-cell-lymphoma- site in standard browser and devices and click on 'continue'",
					"User Should be able to launch the site and Home page should be displayed on the screen.",
					"Continue button is clicked", Status.DONE);
		}
	}

	/**
	 * Function to click 'Allow All' button in web page.
	 *
	 * @return No return value
	 */
	/**
	 * public void allowAllCookies()
	 *
	 * @return
	 */
	public void allowAllCookies() {
		if (objectExists(Common.cookiesAcceptBtn, "isDisplayed", lngMinTimeOutInSeconds, "Allow All Cookies", "Button",
				driver.getTitle(), false)) {
			clickByJS(Common.cookiesAcceptBtn, lngMinTimeOutInSeconds, "Allow All Cookies", "Button", driver.getTitle(),
					false | objectExists(new Common("cookiesjsr", "Accept all").genericTextUsingAncestorID,
							"isDisplayed", lngMinTimeOutInSeconds, "Accept All Cookies", "Button", driver.getTitle(),
							false));
			report.updateTestLog("Selection bar for cookie handling should be visible",
					"User should be able to click on 'Allow All'", "'Allow All' button is clicked", Status.PASS);
		} else {
			report.updateTestLog("Selection bar for cookie handling should be visible",
					"User should be able to click on 'Allow All'", "'Allow All' button is not visible", Status.IGNORE);
		}
	}

	/**
	 * Function to click 'Allow All' button in web page.
	 *
	 * @return No return value
	 */
	/**
	 * public void acceptAllCookies()
	 *
	 * @return
	 */
	public void acceptAllCookies() {
		if (objectExists(new Common("cookiesjsr", "Accept all").genericTextUsingAncestorID, "isDisplayed",
				lngMinTimeOutInSeconds, "Accept All Cookies", "Button", driver.getTitle(), false)) {
			clickByJS(new Common("onetrust-accept-btn-handler").genericElementWithID, lngMinTimeOutInSeconds,
					"Allow All Cookies", "Button", driver.getTitle(),
					false | objectExists(new Common("cookiesjsr", "Accept all").genericTextUsingAncestorID,
							"isDisplayed", lngMinTimeOutInSeconds, "Accept All Cookies", "Button", driver.getTitle(),
							false));
			report.updateTestLog("Selection bar for cookie handling should be visible",
					"User should be able to click on 'Allow All'", "'Allow All' button is clicked", Status.PASS);
		} else {
			report.updateTestLog("Selection bar for cookie handling should be visible",
					"User should be able to click on 'Allow All'", "'Allow All' button is not visible", Status.IGNORE);
		}
	}

	/**
	 * Function to log onto Carthope website
	 *
	 * @return No return value
	 */
	/**
	 * public void logonCarthope()
	 *
	 * @return
	 */
	public void logonCarthope() {
		if (objectExists(new Common("Yes, I am").simpleLinkedText, "isDisplayed", lngMinTimeOutInSeconds, "Yes, I am",
				"Button", "Carthope", false)) {
			click(new Common("Yes, I am").simpleLinkedText, lngMinTimeOutInSeconds, "Yes, I am", "Button", "Carthope",
					false);
			report.updateTestLog(
					"Launch Carthope site in standard browser and devices and click on 'Yes I am' under 'Are you a US healthcare professional?'",
					"User Should be able to launch the site and Home page should be displayed on the screen",
					"'Yes, I am' button is clicked", Status.PASS);
		} else {
			report.updateTestLog(
					"Launch Carthope site in standard browser and devices and click on 'Yes I am' under 'Are you a US healthcare professional?'",
					"User Should be able to launch the site and Home page should be displayed on the screen",
					"'Yes, I am' button is not clicked", Status.FAIL);
		}
	}

	/**
	 * Function to navigate to the URL
	 */
	/**
	 * public void invokeUrl()
	 *
	 * @return
	 */
	public void invokeUrl() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strUrl = dataTable.getData("General_Data", "Application_URL");
		driver.get(strUrl);

		report.updateTestLog("Invoke URL", " URL:<a href = " + strUrl + ">" + strUrl + "</a>" + "Should be invoked",
				" URL:<a href = " + strUrl + ">" + strUrl + "</a>" + " is invoked", Status.DONE);
		windowName.put("Window1", driver.getWindowHandle());
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
	}

	/**
	 * Function to fill the form Using Form Fill
	 */
	/**
	 * public void reqFormFill(String strKeyword, String strScenario)
	 *
	 * @return
	 */
	public void reqFormFill(String strKeyword, String strScenario) {
		setKeywordCount(strKeyword);
		FillInputForm("FillForm", "Input_Parameters", strScenario, "$", true, "Not Specified");
	}

	/**
	 * Function to navigate to the URL
	 */
	/**
	 * public void launchApplication()
	 *
	 * @return
	 */
	public void launchApplication() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strUrl = dataTable.getData("FunctionalRegression", "Application_URL");
		driver.get(strUrl);
		String strHomePageTitle = dataTable.getData("FunctionalRegression", "HomePageTitle");
		boolean verifySiteIsUpAndRunning = verifySiteIsUpAndRunning(strHomePageTitle);
		if (verifySiteIsUpAndRunning) {
			if (!(dataTable.getData("FunctionalRegression", "TC_ID").equalsIgnoreCase("yescartahcp"))) {
				handleCookiePopup();
			}
			if (!dataTable.getData("FunctionalRegression", "PreCondition").isEmpty()) {
				preCondition();
			}
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		}
	}

	/**
	 * public void verifyLinksInWebPage()
	 *
	 * @return
	 */
	public void verifyLinksInWebPage() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName = driver.getTitle();
		String[] strArrLinkLocs = dataTable.getData("FunctionalRegression", "LinkStaticLocator").split(";");
		String[] strArrPageTitle = dataTable.getData("FunctionalRegression", "PageTitles").split(";");
		String[] strArrNavBack = dataTable.getData("FunctionalRegression", "NavigateBack").split(";");
		int i = 0;
		String strPageTitle = null;
		for (String strLinkLoc : strArrLinkLocs) {
			strPageTitle = strArrPageTitle.length > i ? strArrPageTitle[i] : "";
			String strNavBack = strArrNavBack.length > i ? strArrNavBack[i] : "";
			By eleLink = By.xpath(strLinkLoc);

			if (objectExists(eleLink, "isDisplayed", lngMinTimeOutInSeconds, strPageTitle, "Element Existence",
					strPageName, false)) {
				pagescroll(eleLink, strPageName);
				clickLinks(eleLink, strPageTitle, strNavBack);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Link in webPage",
						strPageTitle + " should be display in the " + strPageName,
						strPageTitle + " is not displayed in the " + strPageName, true);
			}
			i++;
		}
	}
	/**
	 * public void verifyLinksInWebPage()
	 *
	 * @return
	 */
	public void verifyLinksInWebPageLoc() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName = driver.getTitle();
		String[] strArrLinkLocs = dataTable.getData("FunctionalRegression", "LinkStaticLocator").split(";");
		String[] strArrPageTitle = dataTable.getData("FunctionalRegression", "PageTitles").split(";");
		String[] strArrNavBack = dataTable.getData("FunctionalRegression", "NavigateBack").split(";");
		int i = 0;
		String strPageTitle = null;
		for (String strLinkLoc : strArrLinkLocs) {
			strPageTitle = strArrPageTitle.length > i ? strArrPageTitle[i] : "";
			String strNavBack = strArrNavBack.length > i ? strArrNavBack[i] : "";
			By eleLink = new Common_SmokeTestcases(strLinkLoc).verifyLinks;
			if (objectExists(eleLink, "isDisplayed", lngMinTimeOutInSeconds, strPageTitle, "Element Existence",
					strPageName, false)) {
				pagescroll(eleLink, strPageName);
				clickLinks(eleLink, strPageTitle, strNavBack);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Link in webPage",
						strPageTitle + " should be display in the " + strPageName,
						strPageTitle + " is not displayed in the " + strPageName, true);
			}
			i++;
		}
	}
	/**
	 * public void clickLinks(WebElement cardComponent, String strPageTitle)
	 *
	 * @return
	 */
	public void clickLinks(By cardComponent, String strPageTitle, String strNavBack) {
		String strLinkName = getText(cardComponent, lngMinTimeOutInSeconds, "LinkName", driver.getTitle());
		//remove click
		if (objectExists(cardComponent, "isDisplayed", lngMinTimeOutInSeconds, strLinkName, "Element Existence",
				driver.getTitle(), false)) {
			click(cardComponent, lngMinTimeOutInSeconds, "Link ", strLinkName, driver.getTitle(), true);
		}
		boolean blnPageTitleExists = strPageTitle.isEmpty();
		boolean linkOpenedInNewTab = isLinkOpenedInNewTab();
		if (linkOpenedInNewTab) {
			if (blnPageTitleExists) {
				verifySiteUsingPageTitle();
			} else {
				verifySiteUsingPageTitle(strPageTitle);
			}
			switchBackToPreviousWindow();
		} else {
			if (blnPageTitleExists) {
				verifySiteUsingPageTitle();
			} else {
				verifySiteUsingPageTitle(strPageTitle);
			}
			if (strNavBack.equalsIgnoreCase("true")) {
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			} else {
				driver.navigate().back();
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}

		}
	}

	public void verifyISI() {
		String strISIExpand = dataTable.getData("FunctionalRegression", "ISITrayLocator");
		By isiLocator = By.xpath(strISIExpand);
		if (objectExists(isiLocator, "isDisplayed", lngMinTimeOutInSeconds, "ISI Expand", "Element Existence",
				driver.getTitle(), false)) {
			click(isiLocator, lngMinTimeOutInSeconds, "ISI ", "ISI Expand", driver.getTitle(), true);
			report.updateTestLog("Verify ISI tray", "ISI content should be able to view and Expand",
					" ISI content is able to view and Expand", Status.PASS);
		} else {
			System.out.println("=> Client Error: 404 Not Found");
			report.updateTestLog("Verify ISI tray", "ISI content should be able to view and Expand",
					" ISI content is not able to view and Expand", Status.FAIL);

		}
	}

	/**
	 * Function to verify the site is up and Running
	 *
	 */
	/**
	 * public boolean verifySiteUsingPageTitle(String strActualTitle)
	 *
	 * @return
	 */
	public boolean verifySiteUsingPageTitle(String strActualTitle) {
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		String strExpectedTitle = driver.getTitle();
		boolean flag = false;
		if (strExpectedTitle.contains(strActualTitle)) {
			report.updateTestLog("Verify URLs using Page title", "Page should be Up and Running",
					" Page is up and Running", Status.PASS);
			flag = true;
		} else if (strExpectedTitle.contains("404") || strExpectedTitle.contains("Not Found")) {
			System.out.println("=> Client Error: 404 Not Found");
			report.updateTestLog("Verify URLs using Page title", "Page should be broken and 404 should not be found",
					" Page is broken and Client Error: 404 Not Found", Status.FAIL);

		} else if (strExpectedTitle.contains("500") || strExpectedTitle.contains("Internal Server Error")) {
			System.out.println("=> Server Error: 500 Internal Server Error");
			report.updateTestLog("Verify URLs using Page title", "Page should be broken and 500 should not be found",
					" Page is down and Server Error: 500 Internal Server Error", Status.FAIL);
		} else {
			System.out.println("=> No server or client errors detected based on the page title.");
		}
		return flag;
	}

	/**
	 * Function to verify the site is up and Running
	 *
	 */
	/**
	 * public boolean verifySiteUsingPageTitle()
	 *
	 * @return
	 */
	public boolean verifySiteUsingPageTitle() {
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		String strExpectedTitle = driver.getTitle();
		boolean flag = false;
		if (strExpectedTitle.contains("404") || strExpectedTitle.contains("Not Found")) {
			System.out.println("=> Client Error: 404 Not Found");
			report.updateTestLog("Verify URLs using Page title", "Page should be broken and 404 should not be found",
					" Page is broken and Client Error: 404 Not Found", Status.FAIL);

		} else if (strExpectedTitle.contains("500") || strExpectedTitle.contains("Internal Server Error")) {
			System.out.println("=> Server Error: 500 Internal Server Error");
			report.updateTestLog("Verify URLs using Page title", "Page should be broken and 500 should not be found",
					" Page is down and Server Error: 500 Internal Server Error", Status.FAIL);
		} else {
			driverUtil.waituntilContainsPresentInPageTitle(lngMinTimeOutInSeconds, strExpectedTitle, strExpectedTitle,
					flag);
			report.updateTestLog(
					"Verify URLs using Page title", "Page should be Up and Running", " Page is up and Running and"
							+ " URL:<a href = " + driver.getCurrentUrl() + ">" + driver.getCurrentUrl() + "</a>",
					Status.PASS);
			flag = true;
		}
		return flag;
	}

	/**
	 * public void verifyMenuLinks()
	 *
	 * @return
	 */
	public void verifyMenuLinks() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String[] menuLocators = dataTable.getData("FunctionalRegression", "MenuLinkStaticLocator").split(";");
		String[] strArrPageTitle = dataTable.getData("FunctionalRegression", "PageTitles").split(";");
		int i = 0;

		for (String menuLocator : menuLocators) {
			WebElement element = driver.findElement(By.xpath(menuLocator));
			String strPageTitle = strArrPageTitle[i];
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			String menuName = element.getText();
			clickMenuLinks(element, menuName, strPageTitle);

			i++;
		}
	}

	/**
	 * public void clickMenuLinks(WebElement cardComponent, String strLinkName,
	 * String strPageTitle)
	 *
	 * @return
	 */
	public void clickMenuLinks(WebElement cardComponent, String strLinkName, String strPageTitle) {

		if (objectExists(cardComponent, "isDisplayed", lngMinTimeOutInSeconds, strLinkName, "Element Existence",
				driver.getTitle(), false)) {
			driverUtil.waitUntilStalenessOfElement(cardComponent, strPageTitle);
			pagescroll(cardComponent, strPageTitle);
			click(cardComponent, lngMinTimeOutInSeconds, "Link ", strLinkName, driver.getTitle(), true);
		} else {

			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Link in webPage",
					strPageTitle + " should be display in the " + strPageTitle,
					strPageTitle + " is not displayed in the " + strPageTitle, true);

		}

		boolean linkOpenedInNewTab = isLinkOpenedInNewTab();
		if (linkOpenedInNewTab) {

			verifySiteUsingPageTitle(strPageTitle);

			switchBackToPreviousWindow();
		} else {
			verifySiteUsingPageTitle(strPageTitle);
			driver.navigate().back();
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		}
	}

	/**
	 * Function to validate the all accordian in webpage
	 *
	 * @param strUrl
	 * @param strPageName
	 */
	/**
	 * public void validateAccordianAndLinks()
	 *
	 * @return
	 */
	public void validateAccordianAndLinks() {
		String strAccordianLocator = dataTable.getData("FunctionalRegression", "AccordianStaticLocator");
		String[] strAccLinksLocator = dataTable.getData("FunctionalRegression", "LinksUnderAccordianSection")
				.split(";");
		List<WebElement> all_Accordians = driver.findElements(By.xpath(strAccordianLocator));
		int iNum = 0;
		String strPageName = driver.getTitle();
		for (WebElement accordian : all_Accordians) {
			String strAccordianBtn = accordian.getText();
			pagescroll(accordian, strPageName);
			click(accordian, lngMinTimeOutInSeconds, strAccordianBtn, "Accordian", strPageName, true);

			if (objectExists(accordian, "isEnabled", loadingWindowTimeOut, "Login", "Button", "Home Page", false)) {
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Accordian Functioanality",
						strAccordianBtn + "-Accordian should able to expand and Collapose",
						strAccordianBtn + " - Accordian is able to expand and Collapose", true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Accordian Functioanality",
						strAccordianBtn + "-Accordian should able to expand and Collapose",
						strAccordianBtn + " - Accordian is not able to expand and Collapose", true);
			}
			verifyLinksUnderAccordian(strAccLinksLocator[iNum]);
			iNum++;
		}
	}

	/**
	 * public void verifyLinksUnderAccordian(String strAccordLinksLoc)
	 *
	 * @return
	 */
	public void verifyLinksUnderAccordian(String strAccordLinksLoc) {
		List<WebElement> accordianLinks = driver.findElements(By.xpath(strAccordLinksLoc));
		for (WebElement accordianLink : accordianLinks) {
			if (objectExists(accordianLink, "isDisplayed", lngMinTimeOutInSeconds, "Accordian", "Element Existence",
					driver.getTitle(), false)) {
				driverUtil.waitUntilElementEnabled(accordianLink, lngMinTimeOutInSeconds, "", "", "");
				click(accordianLink, lngMinTimeOutInSeconds, "Link ", "Accordian Link", driver.getTitle(), true);
				WebElement proceedBtn = driver.findElement(By.xpath("//*[contains(text(),'Continue')]"));
				if (objectExists(proceedBtn, "isDisplayed", lngMinTimeOutInSeconds, "Accordian", "Element Existence",
						driver.getTitle(), false)) {
					driverUtil.waitUntilElementEnabled(By.xpath("//*[contains(text(),'Continue')]"),
							lngMinTimeOutInSeconds);
					click(proceedBtn, lngMinTimeOutInSeconds, "Link ", "Accordian Link", driver.getTitle(), false);
				}
			}
			String strURL = accordianLink.getAttribute("href");
			boolean linkOpenedInNewTab = isLinkOpenedInNewTab();
			if (linkOpenedInNewTab) {
				verifySiteUsingPageTitle();
				switchBackToPreviousWindow();
			} else if (strURL.endsWith("pdf")) {
				report.updateTestLog("PDF URL",
						" URL:<a href = " + strURL + ">" + strURL + "</a>" + "Should be invoked",
						" URL:<a href = " + strURL + ">" + strURL + "</a>" + " is invoked", Status.DONE);
			} else {
				verifySiteUsingPageTitle();
				driver.navigate().back();
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}
		}

	}

	/**
	 * Function to check whether the file got downloaded in the given path and it
	 * should contains 1 file
	 *
	 * @param downloadedFile File to check
	 * @return boolean value of whether the file got downloaded or not
	 */

	/**
	 * public boolean checkFileDownloaded(File downloadedFile, String strFilePath,
	 * String strFileName)
	 *
	 * @return
	 */
	public boolean checkFileDownloaded(File downloadedFile, String strFilePath, String strFileName) {
		File[] files = downloadedFile.listFiles();
		for (long stop = System.nanoTime() + TimeUnit.SECONDS.toNanos(lngMinTimeOutInSeconds); stop > System
				.nanoTime();) {
			if (files != null && files.length == 1) {
				try {

					ALMFunctions.UpdateReportLogAndALMForPassStatus("Download File",
							"'" + strFileName + "' should be downloaded in file path: " + strFilePath,
							"'" + "<a href=" + new File(strFilePath).toURI().toURL() + ">" + strFileName
									+ "</a>' is downloaded in the file path: " + strFilePath,
							true);

				} catch (MalformedURLException e) {
					e.printStackTrace();
				}
				return true;
			} else {
				try {
					ALMFunctions.UpdateReportLogAndALMForFailStatus("Download File",
							"'" + strFileName + "' should be downloaded in file path: " + strFilePath,
							"'" + "<a href=" + new File(strFilePath).toURI().toURL() + ">" + strFileName
									+ "</a>' is not downloaded in the file path: " + strFilePath,
							true);

				} catch (MalformedURLException e) {
					e.printStackTrace();
				}
			}
		}
		return false;
	}

	/**
	 * Function to play video until end of the video before 10 seconds
	 *
	 * @return No return value
	 */
	/**
	 * public void videoPlay(String strValues)
	 *
	 * @return
	 */
	public void videoPlay(String strValues) {
		Common_VideoValidation objVideo = new Common_VideoValidation(strValues);
// pagescroll(objVideo.videoBtn, "Scroll to video");
// click(objVideo.videoBtn, lngMinTimeOutInSeconds, "Video Playing", "Started Playing", driver.getTitle(), true);
		JavascriptExecutor jsExecutor = (JavascriptExecutor) driver.getWebDriver();
		waitFor(5000);// wait
		String strActivevideos = Common_VideoValidation.activeVideo;
		jsExecutor.executeScript("document.querySelector('" + strActivevideos + "').pause();");
// Seek the video to 10 seconds
		jsExecutor.executeScript("document.querySelector('" + strActivevideos + "').currentTime=15;");
// driverUtil.waitUntilVideoSeekComplete();
		WebElement videoPlay = driver.findElement(By.tagName("video"));
		double dblSeekableEnd = Double.parseDouble(videoPlay.getAttribute("duration"));
		double dblEndDuration = Math.round(dblSeekableEnd * 0.80);
// driverUtil.waitUntilVideoSeekComplete();
		ALMFunctions.UpdateReportLogAndALMForPassStatus("Video Play at 75 %",
				"Video should be played in the " + driver.getTitle(), "Video is displayed in the " + driver.getTitle(),
				true);
// Seek the video to the end Duration - 15 of the Video
		String strSeekSplitDuration2 = String.valueOf(dblEndDuration);
		jsExecutor.executeScript(
				"document.querySelector('" + strActivevideos + "').currentTime=" + strSeekSplitDuration2 + ";");
// driverUtil.waitUntilVideoSeekComplete();
		waitFor(2000); // wait
// clickByJS(Common_VideoValidation.closeBtn, lngMinTimeOutInSeconds, "75% of Video", "video Close Button",
// driver.getTitle(), true);
// waitFor(2000); // wait
// driverUtil.waitUntilElementVisible(Common_VideoValidation.formBox, timeOutInSeconds);
	}

	/**
	 * Method to verify the state of an object
	 *
	 * @param strFieldName- name of an object
	 * @param strValueState - Object type and object state should be provided with
	 *                      '!' in data sheet
	 * @param strPageName   - Page Name in which the control is available
	 * @return No return value
	 */
	/**
	 * public void verifyObjectState(String strFieldName, String strValueState,
	 * String strPageName)
	 *
	 * @return
	 */
	public void verifyObjectState(String strFieldName, String strValueState, String strPageName) {
		By locator = null;

		String strElementType = "";
		String strElementState = "";
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds, strPageName);
		String[] strObjectStateValue = strValueState.split("!");
		strElementType = strObjectStateValue[0];
		strElementState = strObjectStateValue[1];

		switch (strElementType.toLowerCase()) {
		case "textbox":
			locator = new Common(strFieldName).textbox;
			break;

		case "link":
			locator = new Common(strFieldName).link;
			break;

		case "message":
			locator = new Common(strFieldName).surveyMessage;
			break;

		case "button":
			locator = new Common(strFieldName).textbox;
			break;
		default:
			ALMFunctions.ThrowException("Verify Object State", "Only pre-defined control must be provided",
					"Unhandled control " + strElementType, false);
			break;
		}

		switch (strElementState.toLowerCase()) {

		case "notexist":
			strElementState = "not exist";
			verifyObjectExistance(locator, strFieldName, strElementState, strElementType, strPageName);
			break;
		case "exist":
			strElementState = "exist";
			verifyObjectExistance(locator, strFieldName, strElementState, strElementType, strPageName);
			break;
		case "mandatory":
			strElementState = "mandatory";
			verifyObjectExistance(locator, strFieldName, strElementState, strElementType, strPageName);
			break;
		case "nonmandatory":
			strElementState = "non mandatory";
			verifyObjectExistance(locator, strFieldName, strElementState, strElementType, strPageName);
			break;
		default:
			ALMFunctions.ThrowException("Verify Object State", "Only pre-defined control must be provided",
					"Unhandled control " + strElementState, false);
			break;
		}
	}

	/**
	 * Method to verify the existence of an object. This method is part of
	 * verifyobjectstate method
	 *
	 * @param locator,         locator value of element
	 * @param strButtonName,   fieldName of the button
	 * @param strElementState, state of the element
	 * @param strElementType,  type of the element
	 * @param strPageName      - Page Name in which the control is available
	 * @return No return value
	 */

	/**
	 * public void verifyObjectExistance(By locator, String strButtonName, String
	 * strElementState, String strElementType,
	 *
	 * @return
	 */
	public void verifyObjectExistance(By locator, String strButtonName, String strElementState, String strElementType,
			String strPageName) {

		switch (strElementState) {
		case "exist":
			if (objectExists(locator, "isDisplayed", lngMinTimeOutInSeconds, strButtonName, "Element Existence",
					strPageName, false)) {
				pagescroll(locator, strPageName);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Given Object Exists",
						"'" + strButtonName + "' " + strElementType + " should be display in the " + strPageName,
						"'" + strButtonName + "' " + strElementType + " is displayed in the " + strPageName, true);

			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus(strButtonName,
						"'" + strButtonName + "' " + strElementType + " should be display in the " + strPageName,
						"'" + strButtonName + "' " + strElementType + " is not displayed in the " + strPageName, true);

			}

			break;

		case "not exist":
			if (objectExists(locator, "isDisplayed", lngMinTimeOutInSeconds, strButtonName, "Element Existence",
					strPageName, false)) {
				pagescroll(locator, strPageName);
				ALMFunctions.UpdateReportLogAndALMForFailStatus(strButtonName,
						"'" + strButtonName + "' " + strElementType + " should not display in the " + strPageName,
						"'" + strButtonName + "' " + strElementType + " is displayed in the " + strPageName, true);

			} else {

				ALMFunctions.UpdateReportLogAndALMForPassStatus(strButtonName,
						"'" + strButtonName + "' " + strElementType + " should not display in the " + strPageName,
						"'" + strButtonName + "' " + strElementType + " is not displayed in the " + strPageName, true);

			}

			break;
		case "mandatory":
			if (objectExists(locator, "isPresent", lngMinTimeOutInSeconds, strButtonName, "Mandatory check",
					strPageName, false)) {
				pagescroll(locator, strPageName);
				ALMFunctions.UpdateReportLogAndALMForPassStatus(strButtonName,
						strButtonName + " " + strElementType + " should be display as mandatory in the " + strPageName,
						strButtonName + " " + strElementType + " is displayed as mandatory in the " + strPageName,
						true);

			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus(strButtonName,
						strButtonName + " " + strElementType + " should be display as mandatory in the " + strPageName,
						strButtonName + " " + strElementType + " is not displayed as mandatory in the " + strPageName,
						true);

			}

			break;
		case "non mandatory":
			if (objectExists(locator, "isDisplayed", lngMinTimeOutInSeconds, strButtonName, "Non Mandatory check",
					strPageName, false)) {
				pagescroll(locator, strPageName);
				ALMFunctions.UpdateReportLogAndALMForFailStatus(strButtonName,
						strButtonName + " " + strElementType + " should not display as mandatory in the " + strPageName,
						strButtonName + " " + strElementType + " is displayed as mandatory in the " + strPageName,
						true);

			} else {

				ALMFunctions.UpdateReportLogAndALMForPassStatus(strButtonName,
						strButtonName + " " + strElementType + " should not display as mandatory in the " + strPageName,
						strButtonName + " " + strElementType + " is not displayed as mandatory in the " + strPageName,
						true);

			}

			break;

		default:
			ALMFunctions.ThrowException("Verify object", "Only pre-defined control must be provided",
					"Unhandled control " + strElementState, false);
			break;
		}

	}

	/**
	 * Function to click the required fileds in SurveyForm
	 *
	 * @param strValues - Value of Web Element to be click
	 * @return No return value
	 */

	/**
	 * public void surveyFormCheck(String strValues)
	 *
	 * @return
	 */
	public void surveyFormCheck(String strValues) {
		Common objBtn = new Common(strValues);
		String strPageName = driver.getTitle();
		if (objectExists(objBtn.surveyBtn, "isEnabled", lngMinTimeOutInSeconds, strValues, "Radio Button",
				"Survey Form", false)) {
			click(objBtn.surveyBtn, lngMinTimeOutInSeconds, strValues, "Button", driver.getTitle(), true);

			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Survey Button is clicked",
					strValues + " should be display as button in the " + strPageName,
					strValues + " is displayed as button in the " + strPageName, true);

		} else if (objectExists(Common.checkEnableBtn, "isDisplayed", lngMinTimeOutInSeconds, strValues, "Check Box",
				"Survey Form", false)) {
			String[] strCheckboxText = strValues.split("!");

			for (String strText : strCheckboxText) {
				Common objCheckBox = new Common(strText);
				click(objCheckBox.surveyBtn, lngMinTimeOutInSeconds, strValues, "Button", driver.getTitle(), true);
			}
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Survey Button is clicked",
					strValues + " should be display as button in the " + strPageName,
					strValues + " is displayed as button in the " + strPageName, true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Survey Button is clicked",
					strValues + " should be display as button in the " + strPageName,
					strValues + " is not displayed as button in the " + strPageName, true);
		}

		pagescroll(Common.nextBtn, strValues);
		click(Common.nextBtn, lngMinTimeOutInSeconds, strValues, strValues, strValues, false);
		waitFor(3000);// wait
		if (isAlertPresent()) {
			System.out.println("Alert Is Present");
			acceptAlert();
		} else {
			System.out.println("Alert is not Present");
		}
	}

	/**
	 * public boolean isAlertPresent()
	 *
	 * @return
	 */
	public boolean isAlertPresent() {
		try {
			driver.switchTo().alert();
			return true;
		} catch (NoAlertPresentException e) {
			return false;
		}
	}

	/**
	 * public void acceptAlert()
	 *
	 * @return
	 */
	public void acceptAlert() {
		Alert alert = driver.switchTo().alert();
		alert.accept();
	}

	/**
	 * Function to select the member button in web page.
	 *
	 * @return No return value
	 */
	/**
	 * public void selectHCP(String strMemberBtn)
	 *
	 * @return
	 */
	public void selectHCP(String strMemberBtn) {
// String strMemberBtn = dataTable.getData("General_Data", "MemberType");
		click(new Common(strMemberBtn).medicalCenterLink, lngMinTimeOutInSeconds, "I am a US healthcare professional",
				"Button", driver.getTitle(), false);
		report.updateTestLog("Select Memeber Button", "\"" + strMemberBtn + "\"Button should be invoked",
				"\"" + strMemberBtn + "\"Button is invoked", Status.DONE);
	}

	/**
	 * public void clickButtonCTA()
	 *
	 * @return
	 */
	public void clickButtonCTA() {
		if (objectExists(Common.useLocatorTool, "isEnabled", lngMinTimeOutInSeconds, "Button", "Radio Button",
				"Survey Form", false)) {

			pagescroll(Common.useLocatorTool, "Locator Tool");
			click(Common.useLocatorTool, lngMinTimeOutInSeconds, "Button", "CTA Button", "Locator Tool", true);
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify CTA Button - Use the Locator Tool",
					"CTA Button should be display as icon in the " + "Use the Locator Tool",
					"CTA Button is displayed as icon in the " + "Use the Locator Tool", true);

		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify CTA Button - Use the Locator Tool",
					"CTA Button should be display as icon in the " + "Use the Locator Tool",
					"CTA Button is displayed as icon in the " + "Use the Locator Tool", true);
		}

	}

	/**
	 * Function to the values in the searchBox
	 *
	 * @param strSearchText - Value of Text
	 * @return No return value
	 */
	/**
	 * public void enterTextInSearchBox(String strSearchText)
	 *
	 * @return
	 */
	public void enterTextInSearchBox(String strSearchText) {
		click(Common.searchBtn, lngMinTimeOutInSeconds, strSearchText, "SearchButton", driver.getTitle(), true);
		sendkeys(Common.searchForm, lngMinTimeOutInSeconds, strSearchText, "SearchBox", driver.getTitle(), "Text Field",
				true);
		waitFor(3000);
		sendkeys(Common.searchForm, lngMinTimeOutInSeconds, Keys.ENTER, "SearchBox", driver.getTitle(), false);
	}

	/**
	 * Method to delete that particular file it doesn't contains original name of
	 * the file from the folder: 'externalFiles' in the given directory which is
	 * related to G360_ROW Homepage Chart
	 *
	 *
	 * @param - fileDir - Location of the downloaded file
	 * @param - strFileName - Name of the respective File
	 * @return - No return
	 * <AUTHOR>
	 */

	/**
	 * public void deleteFolderContent(String strFileName, File fileDir)
	 *
	 * @return
	 */
	public void deleteFolderContent(String strFileName, File fileDir) {
		String[] listFiles = fileDir.list();
		for (String file : listFiles) {
			if (file != null && !file.isEmpty()) {
				if (file.equalsIgnoreCase(strFileName)) {
					continue;
				} else {
					File currentFile = new File(fileDir.getPath(), file);
					currentFile.delete();
					System.out.println("File Name: " + file + " is deleted");
				}
			} else {
				ALMFunctions.ThrowException("Verify file downloaded in the respective path",
						file + ", should be download in the respective path",
						file + ", is not downloaded in the respective path", false);

			}
		}
	}

	/**
	 * Method to extract the file name from the URL and replace '%20' with spaces.
	 *
	 * @param url The URL from which to extract the file name.
	 * @return The extracted file name with '%20' replaced by space.
	 */
	/**
	 * public String getFileNameFromURL(String url)
	 *
	 * @return
	 */
	public String getFileNameFromURL(String url) {
		try {
// Create a URL object from the string
			URL urlObj = new URL(url);
			String path = urlObj.getPath();

// Extract the file name from the path (after the last "/")
			String fileName = path.substring(path.lastIndexOf("/") + 1);

// Replace '%20' with space
			fileName = fileName.replace("%20", " ");

			return fileName;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * This method extracts and returns the content inside the first pair of
	 * parentheses found in the input string.
	 *
	 * @param input The input string to search for text inside parentheses.
	 * @return The content inside parentheses or an empty string if no parentheses
	 *         are found.
	 */
	/**
	 * public String extractTextInParentheses(String input)
	 *
	 * @return
	 */
	public String extractTextInParentheses(String input) {
// Matches text inside the first set of parentheses
		String regex = "\\(([^()\\s]+).*?\\):";
		/*
		 * if (input.contains("(GS-US-320-0108 (China)):")) { regex = "^\\(([^\\(]+)"; }
		 * else { // Define the regex pattern to match the desired format (e.g.,
		 * GS-US-320-0108 or // 123-456-789) regex = "\\(([A-Za-z0-9-]+)\\)"; }
		 */

// Compile the regular expression
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(input);

// Check if there's a match and return the content inside parentheses
		if (matcher.find()) {
			return matcher.group(1).trim(); // Extracts the text inside the parentheses
		} else {
			return ""; // Return an empty string if no parentheses are found
		}
	}

	/**
	 * This method deletes all PDF files from the specified directory.
	 *
	 * @param directoryPath The path of the directory where the PDF files are
	 *                      located.
	 * @return true if all PDF files were successfully deleted, false otherwise.
	 */
	/**
	 * public boolean deleteAllPDFFiles(String directoryPath)
	 *
	 * @return
	 */
	public boolean deleteAllPDFFiles(String directoryPath) {
// Create a File object for the given directory path
		File directory = new File(directoryPath);

// Check if the directory exists and if it is indeed a directory
		if (directory.exists() && directory.isDirectory()) {
// List all files in the directory
			File[] files = directory.listFiles();

			if (files != null) {
				boolean allDeleted = true;

// Loop through all files in the directory
				for (File file : files) {
// Check if the file is a PDF (ends with .pdf)
					if (file.isFile() && file.getName().endsWith(".pdf")) {
// Attempt to delete the PDF file
						boolean deleted = file.delete();
						if (!deleted) {
							allDeleted = false; // If a file failed to delete, mark as false
							System.out.println("Failed to delete: " + file.getName());
						} else {
							System.out.println("Deleted: " + file.getName());
						}
					}
				}

// Return whether all PDF files were successfully deleted
				return allDeleted;
			}
		} else {
			System.out.println("Invalid directory path or directory does not exist.");
		}

// Return false if there was an issue
		return false;
	}

	/**
	 * public void clickOnLoadMoreBtn()
	 *
	 * @return
	 */
	public void clickOnLoadMoreBtn() {
// Loop to click the button while it's invisible
		WebElement button = null;
		int i = 0;
		boolean waitUntilElementVisible = driverUtil.waitUntilElementVisible(By.xpath("//button[@id='searchLoadMore']"),
				lngMinTimeOutInSeconds, "LoadMoreBtn", "Button", "DDME", false);
		do {
			try {
				button = driver.findElement(Common.searchLoadMoreBtn);
// Wait for a specified interval (e.g., 2 seconds) before trying again
				driverUtil.waitUntilStalenessOfElement(button, "DDME"); // Adjust the sleep time as needed (in
// milliseconds)

				if (objectExists(button, "isDisplayed", timeOutInSeconds, "DDME", "Button", "Page", false)) {

					driverUtil.waitUntilStalenessOfElement(button, "DDME");

					pagescroll(button, "DDME");
					clickByJS(button, lngPagetimeOutInSeconds, "Load More", "Button", "DDME", false);

					i++;
				}
				System.out.println(i);

			} catch (Exception e) {
				System.out.println("Button is visible, clicking anyway...");
				button.click();
				System.out.println("Button not found.");
				break; // Exit if the button is not found on the page
			}

		} while (waitUntilElementVisible);
	}

	/**
	 * Function to the values in the searchBox
	 *
	 * @param strSearchText - Value of Text
	 * @return No return value
	 */
	/**
	 * public void verifySearchResults(String strSearchText)
	 *
	 * @return
	 */
	public void verifySearchResults(String strSearchText) {
		Common objSearchResults = new Common(strSearchText);
		driverUtil.waitUntilPageReadyStateComplete(lngPagetimeOutInSeconds);
		String strPageName = driver.getTitle();
		if (objectExists(objSearchResults.searchResults, "isEnabled", lngMinTimeOutInSeconds, strSearchText, "Label",
				strPageName, false)) {

			pagescroll(objSearchResults.searchResults, strPageName);
			WebElement eleSearch = driver.findElement(objSearchResults.searchResults);
			ALMFunctions.UpdateReportLogAndALMForPassStatus(
					"Verify Search Results", strSearchText + " should be display in the " + strPageName, strSearchText
							+ " is displayed in the " + strPageName + " and Searched Value is " + eleSearch.getText(),
					true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Search Results ",
					strSearchText + " should be display in the " + strPageName,
					strSearchText + " is not displayed in the " + strPageName, true);
		}
		WebElement eleSearch = driver.findElement(Common.searchShowResults);
		if (objectExists(Common.searchShowResults, "isEnabled", lngMinTimeOutInSeconds, strSearchText, "Label",
				strPageName, false)) {
			pagescroll(Common.searchShowResults, strPageName);

			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Showing X-X out of XX format",
					strSearchText + " should be display in the " + strPageName,
					strSearchText + " is displayed in the " + strPageName + " and format is: " + eleSearch.getText(),
					true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Showing X-X out of XX format",
					eleSearch.getText() + " should be display in the " + strPageName,
					eleSearch.getText() + " is not displayed in the " + strPageName, true);
		}

	}

	/**
	 * Function to click the required fileds in SurveyForm
	 *
	 * @param strValues - Value of Web Element to be click
	 * @return No return value
	 */
	/**
	 * public void validateATC_Search(String strKeyword)
	 *
	 * @return
	 */
	public void validateATC_Search(String strKeyword) {
		setKeywordCount(strKeyword);
		String[] testData = dataTable.getData("General_Data", "Test_Data1").split("!");
		String strLocation = testData[0];
		String strMedicalCenterName = testData[1];
		pagescroll(Common.atcTextBox, strMedicalCenterName);
		sendkeys(Common.atcTextBox, lngMinTimeOutInSeconds, strLocation, "ATC SearchBox", driver.getTitle(),
				"Text Field", true);
		waitFor(3000);
		sendkeys(Common.atcTextBox, lngMinTimeOutInSeconds, Keys.ENTER, "ATC SearchBox", driver.getTitle(), false);
		Common objMedicalIcon = new Common(strMedicalCenterName);

		String strPageName = "ATC Search Page";
		if (objectExists(objMedicalIcon.medicalCenterIcon, "isEnabled", lngMinTimeOutInSeconds, strMedicalCenterName,
				"Radio Button", "Survey Form", false)) {

			pagescroll(objMedicalIcon.medicalCenterIcon, strPageName);

			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Medical Center Icon",
					strMedicalCenterName + " should be display as icon in the " + strPageName,
					strMedicalCenterName + " is displayed as icon in the " + strPageName, true);

			click(objMedicalIcon.medicalCenterName, lngMinTimeOutInSeconds, strMedicalCenterName, "Icon", strPageName,
					true);

		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Medical Center Icon",
					strMedicalCenterName + " should be display as icon in the " + strPageName,
					strMedicalCenterName + " is not displayed as icon in the " + strPageName, true);
		}
		List<WebElement> eleSeeMoreBtns = driver.findElements(Common.seeMoreBtn);
		for (WebElement eleSeeMoreBtn : eleSeeMoreBtns) {
			click(eleSeeMoreBtn, lngMinTimeOutInSeconds, "Button", strMedicalCenterName, strPageName, true);
		}
		String strMedicalLoc = strMedicalCenterName.toLowerCase().replaceAll(" ", "_");
		Common objMedicalLoc = new Common(strMedicalLoc);

		if (objectExists(objMedicalLoc.medicalCenterLoc, "isEnabled", lngMinTimeOutInSeconds, strMedicalCenterName,
				"Icon", "Mapbox", false)) {
			pagescroll(objMedicalLoc.medicalCenterLoc, strPageName);
			mouseOver(objMedicalLoc.medicalCenterLoc, lngMinTimeOutInSeconds, strMedicalCenterName, "Icon",
					strPageName);
			/*
			 * click(objMedicalLoc.medicalCenterLoc, lngMinTimeOutInSeconds,
			 * strMedicalCenterName, "Icon", strPageName, true);
			 */
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Medical Center Location in the Mapbox",
					strMedicalCenterName + " should be display as Location in the " + "spherical distance.",
					strMedicalCenterName + " is displayed as Location in the " + "spherical distance.", true);

		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Medical Center Location in the Mapbox",
					strMedicalCenterName + " should be display as Location in the " + "spherical distance.",
					strMedicalCenterName + " is not displayed as Location in the " + "spherical distance.", true);
		}

		String strLabel = null;
		if (objectExists(Common.timeAndDateLabel, "isEnabled", lngMinTimeOutInSeconds, strMedicalCenterName,
				"Time and Date Label", "Map Box", false)) {
			mouseOver(Common.timeAndDateLabel, lngMinTimeOutInSeconds, strMedicalCenterName, "Icon", strPageName);
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Time and Date labels are present",
					"DISTANCE TO CENTER and TRAVEL TIME" + " should be display in the Searched Medical Center",
					"DISTANCE TO CENTER and TRAVEL TIME" + " is displayed as Location in the Searched Medical Center",
					true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Time and Date labels are present",
					"DISTANCE TO CENTER and TRAVEL TIME" + " should be display in the Searched Medical Center",
					"DISTANCE TO CENTER and TRAVEL TIME"
							+ " is not displayed as Location in the Searched Medical Center",
					true);
		}
		if (objectExists(Common.generalInfoLabel, "isEnabled", lngMinTimeOutInSeconds, strMedicalCenterName,
				"General Info Label", "Map Box", false)) {
			List<WebElement> eleGeneralInfoValues = driver.findElements(Common.generalInfoValues);
			strLabel = "Address for Insurance Claims Submissions & Phone Number";
			if (eleGeneralInfoValues.get(2).getText().matches("[0-9()\\-\\s]+")) {
				report.updateTestLog("Verify Phone Number should be Numeric",
						"Phone Number should be as Numeric value in the General Information Section",
						"Phone Number is " + eleGeneralInfoValues.get(2).getText()
								+ " only Numeric value in the General Information Section",
						Status.DONE);

			} else if (eleGeneralInfoValues.get(1).getText().equalsIgnoreCase("Please refer to primary address")) {
				report.updateTestLog("Verify Address for Insurance Claims Submissions Value",
						"Please refer to primary address should be displayed under Address for Insurance Claims Submissions",
						eleGeneralInfoValues.get(1).getText()
								+ " is displayed under Address for Insurance Claims Submissions",
						Status.DONE);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Phone Number & Please refer to primary address",
						strLabel + " should be displayed under General Information Section",
						strLabel + " is not displayed under General Information Section", true);
			}
			pagescroll(Common.generalInfoLabel, strPageName);
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify General Information Details are present",
					"Primary Address,Address for Insurance Claims Submissions,Phone,Email & Website"
							+ " should be display in the Searched Medical Center",
					"Primary Address,Address for Insurance Claims Submissions,Phone,Email & Website"
							+ " is displayed as Location in the Searched Medical Center",
					true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify " + strLabel,
					strLabel + " should be display in the Searched Medical Center",
					strLabel + " is not displayed in the Searched Medical Center", true);
		}

		if (objectExists(Common.directionsAndArrivalLabel, "isEnabled", lngMinTimeOutInSeconds, strMedicalCenterName,
				"Radio Button", "Map Box", false)) {
			strLabel = "Driving Directions & Arrival Instructions";
			pagescroll(Common.directionsAndArrivalLabel, strPageName);
			report.updateTestLog("Verify Directions and Arrival",
					strLabel + " should be display in the Searched Medical Center",
					strLabel + " is displayed in the Searched Medical Center", Status.DONE);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify " + strLabel,
					strLabel + " should be display in the Searched Medical Center",
					strLabel + " is not displayed in the Searched Medical Center", true);
		}
		if (objectExists(Common.carTConsulationAndCareTeam, "isEnabled", lngMinTimeOutInSeconds, strMedicalCenterName,
				"Radio Button", "Map Box", false)) {
			strLabel = "CAR T Consultation & Care Team";
			pagescroll(Common.carTConsulationAndCareTeam, strPageName);
			report.updateTestLog("Verify Car T Consulation and care team",
					strLabel + " should be display in the Searched Medical Center",
					strLabel + " is displayed in the Searched Medical Center", Status.PASS);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify " + strLabel,
					strLabel + " should be display in the Searched Medical Center",
					strLabel + " is not displayed in the Searched Medical Center", true);
		}
// validateLinks("OK","Cancel");

	}

	/**
	 * public void removeOverlay(String strOverlayObj)
	 *
	 * @return
	 */
	public void removeOverlay(String strOverlayObj) {
		JavascriptExecutor jsExecutor = (JavascriptExecutor) driver.getWebDriver();
		jsExecutor.executeScript("document.querySelector('" + strOverlayObj + "').style.display='none';");
	}

	/**
	 * public void validateLinks(String strKeyword)
	 *
	 * @return
	 */
	public void validateLinks(String strKeyword) {
		String strPageName = driver.getTitle();
		setKeywordCount(strKeyword);
		String[] testData = dataTable.getData("General_Data", "Test_Data2").split("!");
		String strAccpetBtn = testData[0];
		String strCancelBtn = testData[1];
		click(new Common("Link to center site").medicalCenterLink, lngMinTimeOutInSeconds, strCancelBtn, "Icon",
				strPageName, true);

		verifyDeclineButton(strCancelBtn);
		click(new Common("Link to center site").medicalCenterLink, lngMinTimeOutInSeconds, "Link to center site",
				"Link", strPageName, true);

		verifyAcceptButton(strAccpetBtn);
		click(new Common("Get directions via Google Maps").medicalCenterLink, lngMinTimeOutInSeconds,
				"Get directions via Google Maps", "Link", strPageName, true);

		verifyDeclineButton(strCancelBtn);
		click(new Common("Get directions via Google Maps").medicalCenterLink, lngMinTimeOutInSeconds,
				"Get directions via Google Maps", "Link", strPageName, true);

		verifyAcceptButton(strAccpetBtn);
		pagescroll(Common.printBtn, strPageName);

		if (objectExists(Common.printBtn, "isEnabled", lngMinTimeOutInSeconds, "Print Screen", "Confirmation Button",
				"Print Button", false)) {
			click(Common.printBtn, lngMinTimeOutInSeconds, "Print Screen", "Print Button", strPageName, true);
			waitFor(7000);
			{
				Set<String> windowHandles = driver.getWindowHandles();
				String parentHandle = driver.getWindowHandle();
				for (String handle : windowHandles) {
					if (!handle.equals(parentHandle)) {
						driver.switchTo().window(handle); // wait use
						ALMFunctions.UpdateReportLogAndALMForPassStatus("Print Information",
								"Print Page should be Opened in " + driver.getTitle(),
								"Print Page is Opened in " + driver.getTitle(), true);
						break;
					}
					driver.switchTo().window(parentHandle);

				}
			}
			waitFor(5000);

		} else

		{
			report.updateTestLog("Print Document is Opened", "Print Page should be opened", "Print Page is not opened",
					Status.FAIL);
		}
	}

	/**
	 * public void verifyDeclineButton(String strCancelBtn)
	 *
	 * @return
	 */
	public void verifyDeclineButton(String strCancelBtn) {
		String strPageName = driver.getTitle();

		if (objectExists(new Common(strCancelBtn).confirmationBtn, "isEnabled", lngMinTimeOutInSeconds, strCancelBtn,
				"Confirmation Button", "Map Box", false)) {
			click(new Common(strCancelBtn).confirmationBtn, lngMinTimeOutInSeconds, strCancelBtn, "Icon", strPageName,
					true);
			report.updateTestLog("Verify Same Page is Available when Cancel Button is Clicked",
					"Same Page should be opened in New Window",
					"Same Page" + driver.getTitle() + " is opened in New Window", Status.PASS);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify New Page is Opened",
					"Same Page should be opened in New Window",
					"Same Page is not Appear when Clicking on the cancel Button", true);
		}

	}

	/**
	 * public void verifyAcceptButton(String strAccpetBtn)
	 *
	 * @return
	 */
	public void verifyAcceptButton(String strAccpetBtn) {
		String strPageName = driver.getTitle();
		if (objectExists(new Common(strAccpetBtn).confirmationBtn, "isEnabled", lngMinTimeOutInSeconds, strAccpetBtn,
				"Confirmation Button", "Map Box", false)) {
			click(new Common(strAccpetBtn).confirmationBtn, lngMinTimeOutInSeconds, strAccpetBtn, "Link", strPageName,
					true);
			switchToNewWindow();
			switchBackToPreviousWindow();
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify New Page is Opened",
					"New Page should be opened in New Window",
					"New Page is not opened in New Window & Old Page Title is " + driver.getTitle(), true);
		}
	}

	/**
	 * public void switchToNewWindow()
	 *
	 * @return
	 */
	public void switchToNewWindow() {

		handlePageRefresh(driver.getWebDriver());
		String originalWindow = driver.getWindowHandle();
// Get all window handles
		Set<String> allWindows = driver.getWindowHandles();

// Switch to the new window
		for (String windowHandle : allWindows) {
			if (!windowHandle.equals(originalWindow)) {
				driver.switchTo().window(windowHandle);
				report.updateTestLog("Verify External site is Opened in New Tab",
						"External Site should be opened in New Tab",
						"External site " + driver.getTitle() + " is opened in New Tab", Status.PASS);
				break;
			}
		}
	}

	/**
	 * public void switchBackToPreviousWindow()
	 *
	 * @return
	 */
	public void switchBackToPreviousWindow() {
		String MainWindow = driver.getWindowHandle();
		System.out.println("Main window handle is " + MainWindow);
// To handle all new opened window
		Set<String> s1 = driver.getWindowHandles();
		System.out.println("Child window handle is" + s1);
		Iterator<String> i1 = s1.iterator();
// Here we will check if child window has other child windows and when child
// window
// is the main window it will come out of loop.
		while (i1.hasNext()) {
			String ChildWindow = i1.next();
			if (!MainWindow.equalsIgnoreCase(ChildWindow)) {
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
				driver.close();
				driver.switchTo().window(ChildWindow);

				System.out.println("Child window closed");
			}
		}
	}

	/**
	 * public void switchToChildWindow()
	 *
	 * @return
	 */
	public void switchToChildWindow() {
		String MainWindow = driver.getWindowHandle();
		Set<String> s1 = driver.getWindowHandles();

		waitFor(3000);
		if (s1.size() <= 1) {
			System.out.println("No child windows found to switch to.");
			return;
		}

		for (String ChildWindow : s1) {
			if (!MainWindow.equalsIgnoreCase(ChildWindow)) {
				driver.switchTo().window(ChildWindow);
				System.out.println("Switched to child window: " + ChildWindow);
				break;
			}
		}
	}

	/**
	 * public void switchToChildWindowAndCloseCurrent()
	 *
	 * @return
	 */
	public void switchToChildWindowAndCloseCurrent() {
		String MainWindow = driver.getWindowHandle();
		Set<String> s1 = driver.getWindowHandles();

		waitFor(3000);
		if (s1.size() <= 1) {
			System.out.println("No child windows found to switch to.");
			return;
		}

		for (String ChildWindow : s1) {
			if (!MainWindow.equalsIgnoreCase(ChildWindow)) {
				driver.close();
				driver.switchTo().window(ChildWindow);
				System.out.println("Switched to child window: " + ChildWindow);
				break;
			}
		}
	}

	/**
	 * @param strSheetName               - Name of the Sheet in which data to be
	 *                                   concatenated is present
	 * @param strColumnName              - Column Name in which data to be
	 *                                   concatenated is present
	 * @param strConcatenationFlagColumn - value of concatenationFlagColumn
	 * @param strScenario                - Scenario for which test data is to be
	 *                                   user
	 * @param strDelimiter               - Delimiter to be used during concatenation
	 * @param includeDelimiter           - Boolean to include delimiter or not
	 * @return - Concatenated String
	 */
	/**
	 * public synchronized String getConcatenatedStringFromExcel(String
	 * strSheetName, String strColumnName,
	 *
	 * @return
	 */
	public synchronized String getConcatenatedStringFromExcel(String strSheetName, String strColumnName,
			String strConcatenationFlagColumn, String strScenario, String strDelimiter, boolean blnIncludeDelimiter,
			boolean blnInput) {
		String strValue = "";

		String strFilePath = dataTable.datatablePath + Util.getFileSeparator() + dataTable.datatableName + ".xlsx";
		try {
			readLock.lock();
			XSSFWorkbook wb = openExcelFile(strFilePath);
			wb.setForceFormulaRecalculation(true);
			try {
				HSSFFormulaEvaluator.evaluateAllFormulaCells(wb);
			} catch (Exception e) {
				System.out.println(e.getLocalizedMessage());
			}
			Sheet sheet = getSheetFromXLSWorkbook(wb, strSheetName, strFilePath);
			int intColumnIndexConcatFlag = getColumnIndex(wb, strFilePath, strSheetName, strConcatenationFlagColumn);
			int intColumnIndex = getColumnIndex(wb, strFilePath, strSheetName, strColumnName);
			int intStartRow = getStartRow(wb, strFilePath, strSheetName, sheet, strScenario);
			int intEndRow = getEndRow(wb, sheet, intStartRow, strSheetName);
			getColumnIndex(wb, strFilePath, strSheetName, "Flag_Write_Data_In_This_Sheet");
			getColumnIndex(wb, strFilePath, strSheetName, "Stored_Value");

			for (int i = intStartRow; i < intEndRow; i++) {
				if (sheet.getRow(i) != null) {
					if (getCellValueAsString(wb, sheet.getRow(i).getCell(intColumnIndexConcatFlag))
							.equalsIgnoreCase("yes")) {
						if (blnInput) {
							String strTemp = getCellValueAsString(wb, sheet.getRow(i).getCell(intColumnIndex));
							StringUtils.substringAfter(strTemp, "Storage=");
							StringUtils.substringBetween(strTemp, "Element Value=", ";Storage=");
						}
						if (blnIncludeDelimiter) {
							if (i == intStartRow) {
								strValue = getCellValueAsString(wb, sheet.getRow(i).getCell(intColumnIndex))
										+ strDelimiter;
							} else if (i != intEndRow) {
								strValue = strValue + getCellValueAsString(wb, sheet.getRow(i).getCell(intColumnIndex))
										+ strDelimiter;
							} else {
								strValue = strValue + getCellValueAsString(wb, sheet.getRow(i).getCell(intColumnIndex));
							}
						} else {
							if (i == intStartRow) {
								strValue = getCellValueAsString(wb, sheet.getRow(i).getCell(intColumnIndex));
							} else {
								strValue = strValue + getCellValueAsString(wb, sheet.getRow(i).getCell(intColumnIndex));
							}
						}
					}
				}
			}

			try {
				wb.close();
			} catch (IOException e) {
				ALMFunctions.ThrowException("Excel Close", "Should be able to close excel file",
						"Below Exception is thrown when trying to " + "close excel file found in the path "
								+ strFilePath + "<br><br>" + e.getLocalizedMessage(),
						false);
			}

		} finally {
			readLock.unlock();
		}

		return strValue;
	}

	/**
	 * @param wb           - XSSFWorkbook Object
	 * @param strFilePath  - File Path of the Excel File
	 * @param strSheetName - Sheet Name in which data to be concatenated is present
	 * @param sheet        - Sheet object
	 * @param strScenario  - Scenario for which this test data is going to be used
	 * @return - Index of Start Row
	 */
	/**
	 * public int getStartRow(XSSFWorkbook wb, String strFilePath, String
	 * strSheetName, Sheet sheet, String strScenario)
	 *
	 * @return
	 */
	public int getStartRow(XSSFWorkbook wb, String strFilePath, String strSheetName, Sheet sheet, String strScenario) {
		boolean blnRowFound = false;

		int intTCIDColumnIndex = getColumnIndex(wb, strFilePath, strSheetName, "TC_ID");
		int intScenarioColumnIndex = getColumnIndex(wb, strFilePath, strSheetName, "TC_Scenario");
		int intIterationColumnIndex = getColumnIndex(wb, strFilePath, strSheetName, "Iteration");
		int intSubIterationColumnIndex = getColumnIndex(wb, strFilePath, strSheetName, "SubIteration");
		for (int intNum = 1; intNum < sheet.getLastRowNum(); intNum++) {
			if (sheet.getRow(intNum) != null) {
				if (getCellValueAsString(wb, sheet.getRow(intNum).getCell(intTCIDColumnIndex))
						.equalsIgnoreCase(scriptHelper.getcraftDriver().getTestParameters().getCurrentTestcase())
						&& getCellValueAsString(wb, sheet.getRow(intNum).getCell(intScenarioColumnIndex))
								.equalsIgnoreCase(strScenario)
						&& Integer.valueOf(getCellValueAsString(wb,
								sheet.getRow(intNum).getCell(intIterationColumnIndex))) == (dataTable.currentIteration)
						&& Integer.valueOf(getCellValueAsString(wb, sheet.getRow(intNum)
								.getCell(intSubIterationColumnIndex))) == (dataTable.currentSubIteration)) {
					blnRowFound = true;
					return intNum + 2;
				}
			}
		}
		if (!blnRowFound) {
			ALMFunctions.ThrowException("Test Data", "Test Data should be found in the sheet " + sheet,
					"Error - Test Data with " + "TC_ID as "
							+ scriptHelper.getcraftDriver().getTestParameters().getCurrentTestcase()
							+ " , TC_Scenario as " + strScenario + " , Iteration as " + dataTable.currentIteration
							+ " , SubIteration as " + dataTable.currentSubIteration + " does not exists in the sheet "
							+ strSheetName,
					false);
		}
		return 0;
	}

	/**
	 * @param wb           - XSSFWorkbook Object
	 * @param sheet        - Sheet object
	 * @param intStartRow  - Index of Start Row
	 * @param strSheetName - Sheet Name in which data to be concatenated is present
	 * @return - Index of End Row
	 */
	/**
	 * public int getEndRow(XSSFWorkbook wb, Sheet sheet, int intStartRow, String
	 * strSheetName)
	 *
	 * @return
	 */
	public int getEndRow(XSSFWorkbook wb, Sheet sheet, int intStartRow, String strSheetName) {
		boolean blnEnd = false;
		for (int intNum = intStartRow; intNum <= sheet.getLastRowNum(); intNum++) {
			if (sheet.getRow(intNum) != null) {
				if (getCellValueAsString(wb, sheet.getRow(intNum).getCell(0)).equalsIgnoreCase("end")) {
					blnEnd = true;
					return intNum;
				}
			}
		}
		if (!blnEnd) {
			ALMFunctions.ThrowException("Test Data", "Test Data with End Tag should be found in the sheet " + sheet,
					"Error - Test Data with " + "End Tag does not exists in the sheet " + strSheetName, false);
		}
		return 0;
	}

	/**
	 * public void verifyLinksUnderPatientSection()
	 *
	 * @return
	 */
	public void verifyLinksUnderPatientSection() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName = driver.getTitle();
		String[] strLabelNames = dataTable.getData("General_Data", "ProfileName").split("->");
		String StrHeaderName = dataTable.getData("General_Data", "ElementToCheck");
		String hrefLink = null;

		for (String strLabelName : strLabelNames) {
			By cardComponent = new Common(strLabelName).paitentLinks;
			By headerElement = new Common(StrHeaderName).headerSection;

			if (driver.getCurrentUrl().contentEquals("https://www.gileadclinicaltrials.com/")) {
				driver.navigate().forward();
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}

			if (objectExists(headerElement, "isDisplayed", lngMinTimeOutInSeconds, strLabelName, "Element Existence",
					strPageName, false)) {
				pagescroll(headerElement, strPageName);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Header Element Exists",
						strLabelName + " Header should be display in the " + strPageName,
						strLabelName + "Header is displayed in the " + strPageName, true);

			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Header Element Exists",
						strLabelName + " should be display in the " + strPageName,
						strLabelName + " is not displayed in the " + strPageName, true);

			}

			if (objectExists(cardComponent, "isDisplayed", lngMinTimeOutInSeconds, strLabelName, "Element Existence",
					strPageName, false)) {
				pagescroll(cardComponent, strPageName);
				click(cardComponent, lngMinTimeOutInSeconds, StrHeaderName, "Paitent Link", strPageName, true);
				driverUtil.waitUntilElementVisible(cardComponent, lngMinTimeOutInSeconds);
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Header Element Exists",
						strLabelName + " Header should be display in the " + strPageName,
						strLabelName + "Header is displayed in the " + strPageName, true);

			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Header Element Exists",
						strLabelName + " should be display in the " + strPageName,
						strLabelName + " is not displayed in the " + strPageName, true);

			}

			if (objectExists(Common.cardCloseBtn, "isDisplayed", lngMinTimeOutInSeconds, strLabelName,
					"Element Existence", strPageName, false)) {
				pagescroll(Common.cardCloseBtn, strPageName);
				click(Common.cardCloseBtn, lngMinTimeOutInSeconds, StrHeaderName, "Patient", strPageName, true);
			}
		}
	}

	/**
	 * public void verifyDifferentLinksUnderSameSection()
	 *
	 * @return
	 */
	public void verifyDifferentLinksUnderSameSection() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName = driver.getTitle();
		String[] strLabelNames = dataTable.getData("General_Data", "LabelName").split("->");
		String[] strLinkNames = dataTable.getData("General_Data", "URLName").split("=>");

		for (String strLabelName : strLabelNames) {

			for (String strLinkName : strLinkNames) {
				By cardComponent = new Common(strLabelName, strLinkName).linkUnderHeaderSection;
				By headerElement = new Common(strLabelName).headerSection;
				String strAppURL = dataTable.getData("General_Data", "Application_URL");
				if (!(driver.getCurrentUrl().equalsIgnoreCase(strAppURL))) {
					driver.navigate().to(strAppURL);
					driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
				}
				if (objectExists(headerElement, "isDisplayed", lngMinTimeOutInSeconds, strLabelName,
						"Element Existence", strPageName, false)) {
					pagescroll(headerElement, strPageName);
					driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
					ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Header Element Exists",
							strLabelName + " Header should be display in the " + strPageName,
							strLabelName + "Header is displayed in the " + strPageName, true);

				} else {
					ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Header Element Exists",
							strLabelName + " should be display in the " + strPageName,
							strLabelName + " is not displayed in the " + strPageName, true);

				}

				if (objectExists(cardComponent, "isDisplayed", lngMinTimeOutInSeconds, strLinkName, "Element Existence",
						strPageName, false)) {
					pagescroll(cardComponent, strPageName);
					validateAndCheckLinkRedirection(cardComponent, strLinkName);

				}
			}
		}
	}

	/**
	 * public void validateAndCheckLinkRedirection(By cardComponent, String
	 * strLinkName)
	 *
	 * @return
	 */
	public void validateAndCheckLinkRedirection(By cardComponent, String strLinkName) {
		String hrefLink = null;
		hrefLink = driver.findElement(cardComponent).getAttribute("href");
		testRedirection(hrefLink);

		int responseExactCode = getResponseExactCode(hrefLink);
		if (responseExactCode >= 300 && responseExactCode < 400) {
			if (responseExactCode == 301) {
				checkLinkAndClick(cardComponent, strLinkName);
				switchToNewWindow();
				switchBackToPreviousWindow();
			}
		}
// Check if the response code is a 2xx code (Success)
		else if (responseExactCode >= 200 && responseExactCode < 300) {
			checkLinkAndClick(cardComponent, strLinkName);
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			/*
			 * driver.navigate().back();
			 * driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			 */
			boolean linkOpenedInNewTab = isLinkOpenedInNewTab();
			if (linkOpenedInNewTab) {
				if (strLinkName.startsWith("https://")) {
					checkURLExists(hrefLink);
				} else {
					checkCurrentURL(strLinkName);
				}

// videoPlay("vp-video");
				switchBackToPreviousWindow();
			}

			else if (strLinkName.startsWith("https://")) {
				checkURLExists(hrefLink);
				driver.navigate().back();
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			} else {
				checkCurrentURL(strLinkName);
				driver.navigate().back();
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}

		} else {// if href value is null means this else block will work
			if (responseExactCode == 406) {
				checkLinkAndClick(cardComponent, strLinkName);
				boolean linkOpenedInNewTab = isLinkOpenedInNewTab();
				if (linkOpenedInNewTab) {
					if (strLinkName.startsWith("https://")) {
						checkURLExists(hrefLink);
					} else {
						checkCurrentURL(strLinkName);
					}
					switchBackToPreviousWindow();
				}
			} else {
				checkLinkAndClick(cardComponent, strLinkName);
				driver.navigate().back();
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}

		}
	}

	/**
	 * public void verifyDifferentLinksUnderDifferentSection()
	 *
	 * @return
	 */
	public void verifyDifferentLinksUnderDifferentSection() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName = driver.getTitle();
		String[] strTitleNames = dataTable.getData("General_Data", "TitleName").split("->");
		String[] strLinkNames = dataTable.getData("General_Data", "LinkName").split("=>");
		int i = 0;

		for (String strTitleName : strTitleNames) {
			String strLinkName = strLinkNames[i];
			By cardComponent = new Common(strTitleName, strLinkName).linkUnderHeaderSection;
			By headerElement = new Common(strTitleName).headerSection;

			String strAppURL = dataTable.getData("General_Data", "Application_URL");
			if (!(driver.getCurrentUrl().equalsIgnoreCase(strAppURL))) {
				driver.navigate().to(strAppURL);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}
			if (objectExists(headerElement, "isDisplayed", lngMinTimeOutInSeconds, strTitleName, "Element Existence",
					strPageName, false)) {
				pagescroll(headerElement, strPageName);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Header Element Exists",
						strTitleName + " Header should be display in the " + strPageName,
						strTitleName + " Header is displayed in the " + strPageName, true);

			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Header Element Exists",
						strTitleName + " should be display in the " + strPageName,
						strTitleName + " is not displayed in the " + strPageName, true);

			}

			if (objectExists(cardComponent, "isDisplayed", lngMinTimeOutInSeconds, strLinkName, "Element Existence",
					strPageName, false)) {

				pagescroll(cardComponent, strPageName);
				validateAndCheckLinkRedirection(cardComponent, strLinkName);
			}
			i++;
		}
	}

	/**
	 * public void checkLinkAndClick(By cardComponent, String strLinkName)
	 *
	 * @return
	 */
	public void checkLinkAndClick(By cardComponent, String strLinkName) {
		String hrefLink = driver.findElement(cardComponent).getAttribute("href");
		int responseExactCode = getResponseExactCode(hrefLink);
		if (responseExactCode == 504) {
			driver.navigate().refresh();
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		}
		click(cardComponent, lngMinTimeOutInSeconds, strLinkName, "Link ", driver.getTitle(), true);
	}

	/**
	 * public void checkURLExists(String strLinkName)
	 *
	 * @return
	 */
	public void checkURLExists(String strLinkName) {
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		String currentUrl = driver.getCurrentUrl();
		String removeSpacesFromURL = removeSpacesFromURL(currentUrl);
		if (removeSpacesFromURL.contains(strLinkName)) {
			report.updateTestLog("Verify Page Navigation", "Page should be navigated to : " + driver.getCurrentUrl(),
					"Page is navigated to : " + driver.getCurrentUrl(), Status.PASS);
		} else {
			report.updateTestLog("Verify Page Navigation", "Page should be navigated to : " + driver.getCurrentUrl(),
					"Page is not navigated to : " + driver.getCurrentUrl(), Status.FAIL);
		}
	}

	/**
	 * public void checkCurrentURL(String strLinkName)
	 *
	 * @return
	 */
	public void checkCurrentURL(String strLinkName) {
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		String currentUrl = driver.getCurrentUrl();

		report.updateTestLog("Verify Page Navigation", "Page should be navigated to : " + currentUrl,
				"Page is navigated to : " + currentUrl, Status.PASS);

	}

	/**
	 * public String removeSpacesFromURL(String url)
	 *
	 * @return
	 */
	public String removeSpacesFromURL(String url) {
// Replace '%20' with an empty string
		return url.replace("%20", "");
	}

	/**
	 * public static void handlePageRefresh(WebDriver driver)
	 *
	 * @return
	 */
	public static void handlePageRefresh(WebDriver driver) {
// Define WebDriverWait for waiting elements
		WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));

// Show a loading message
		System.out.println("Refreshing page...");

// Get the current URL to detect page refresh
		String currentURL = driver.getCurrentUrl();

// Refresh the page
		driver.navigate().refresh();

		try {
// Wait for the page to load completely
			wait.until(ExpectedConditions.urlToBe(currentURL)); // Wait until the URL is the same, indicating page is

// Page refresh is complete, stop loading
			System.out.println("Page refresh complete!");

		} catch (Exception e) {
// Timeout if the page doesn't load in the expected time
			System.err.println("Page load timed out after 10 seconds.");
		}
	}

	/**
	 * Method to check if the link opens in a new tab.
	 *
	 * @return True if the link opens in a new tab, false otherwise.
	 */
	public boolean isLinkOpenedInNewTab() {
// Store the current window handle
		String originalWindow = driver.getWindowHandle();

// Wait for the new tab to open
		driver.manage().timeouts().implicitlyWait(5, TimeUnit.SECONDS);

// Check if there is a new window handle
		Set<String> allWindows = driver.getWindowHandles();
		for (String windowHandle : allWindows) {
			if (!windowHandle.equals(originalWindow)) {
				driver.switchTo().window(windowHandle);
				return true;
			}
		}
		return false;
	}

// DX CI CD Pipeline Methods
	/**
	 * public void verifyAccordianFunctionality()
	 *
	 * @return
	 */
	public void verifyAccordianFunctionality() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strAccordian = dataTable.getData("General_Data", "AccordianHeader");
		validateAccordian(strAccordian);
	}

	/**
	 * public void verifySearchResultCount()
	 *
	 * @return
	 */
	public void verifySearchResultCount() {
// setKeywordCount(strKeyword);
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String[] strMenu = dataTable.getData("General_Data", "NavigationMenu").split("->");
		String strMainMenu = strMenu[0];
		String strSubMenu = strMenu[1];
		String[] strSearchCount = dataTable.getData("General_Data", "SearchCount").split("!");

		String strPageName = driver.getTitle();
		verifyObjectExistance(Common.heroBannerContent, "Envisioning a healthier world for all people", "exist",
				"HeroBanner", strPageName);
		clickMenu(strMainMenu, strSubMenu, strPageName);
		navigationSteps(strPageName);
		click(Common.studyStatusBtn, lngMinTimeOutInSeconds, "Button", " Apply Filters", "Click", true);
		verifyObjectExistance(new Common(strSearchCount[0]).searchResultsCount, strSearchCount[0], "exist",
				"Search Results Count", strPageName);

	}

// DX CI CD Pipeline Methods updated
	/**
	 * public void verifyAllLinksUnderHeaderSections()
	 *
	 * @return
	 */
	public void verifyAllLinksUnderHeaderSections() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName = driver.getTitle();
		String[] strHeaderSection = dataTable.getData("General_Data", "HeadingName").split("->");

		for (String strEachLink : strHeaderSection) {

			String strAppURL = dataTable.getData("General_Data", "Application_URL");
			if (!(driver.getCurrentUrl().equalsIgnoreCase(strAppURL))) {
				driver.navigate().to(strAppURL);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}
			By cardComponent = new Common(strEachLink).allLinksUnderHeaderSection;
			By headerElement = new Common(strEachLink).headerSection;
			List<WebElement> allLinks = driver.findElements(cardComponent);
			/*
			 * pagescroll(headerElement, strPageName); if (objectExists(headerElement,
			 * "isDisplayed", lngMinTimeOutInSeconds, strEachLink, "Element Existence",
			 * strPageName, false)) { pagescroll(headerElement, strPageName);
			 * driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			 * ALMFunctions.
			 * UpdateReportLogAndALMForPassStatus("Verify Header Element Exists",
			 * strEachLink + " Header should be display in the " + strPageName, strEachLink
			 * + "Header is displayed in the " + strPageName, true);
			 *
			 * } else { ALMFunctions.
			 * UpdateReportLogAndALMForFailStatus("Verify Header Element Exists",
			 * strEachLink + " should be display in the " + strPageName, strEachLink +
			 * " is not displayed in the " + strPageName, true);
			 *
			 * }
			 */
			for (WebElement link : allLinks) {
				if (objectExists(link, "isDisplayed", lngMinTimeOutInSeconds, strEachLink, "Element Existence",
						strPageName, false)) {
					pagescroll(link, strPageName);
					String hrefLink = link.getAttribute("href");
					testRedirection(hrefLink);

					int responseExactCode = getResponseExactCode(hrefLink);
					if (responseExactCode >= 300 && responseExactCode < 400) {
						if (responseExactCode == 301) {
							click(link, lngMinTimeOutInSeconds, strEachLink, "Link ", strPageName, true);
							switchToNewWindow();
							switchBackToPreviousWindow();
						}
					}
// Check if the response code is a 2xx code (Success)
					else if (responseExactCode >= 200 && responseExactCode < 300) {
						click(link, lngMinTimeOutInSeconds, strEachLink, "Link", strPageName, true);
						driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
						boolean linkOpenedInNewTab = isLinkOpenedInNewTab();
						if (linkOpenedInNewTab) {
							checkURLExists(hrefLink);
							switchBackToPreviousWindow();
						} else {
							checkURLExists(hrefLink);
							driver.navigate().back();
							driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
						}
					}
				}
			}
		}
	}

	/**
	 * public void verifyEachLinkNavigations()
	 *
	 * @return
	 */
	public void verifyEachLinkNavigations() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName1 = driver.getTitle();
		String strUrl = driver.getCurrentUrl();

		report.addTestLogSection(strPageName1 + ":" + " URL:<a href = " + strUrl + ">" + strUrl + "</a>");
		String strLinkType = dataTable.getData("General_Data", "TypeOfLink");
		String strPageName = driver.getTitle();
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
		switch (strLinkType) {
		case "External Link":
			String strCurrentUrl = driver.getCurrentUrl();
			int iResponseCode = getResponseExactCode(strCurrentUrl);
			if ((iResponseCode >= 200 && iResponseCode < 300) || (iResponseCode >= 300 && iResponseCode < 400)) {
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Broken Link in " + strPageName,
						"Expected Response Code: 2XX/3XX", "Actual-ResponseCode:" + iResponseCode + "\n"
								+ " URL:<a href = " + strCurrentUrl + ">" + strCurrentUrl + "</a>",
						false);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Broken Link in " + strPageName,
						"Expected Response Code: 2XX/3XX ", "Actual Response Code: " + iResponseCode + "\n <a href = "
								+ strCurrentUrl + ">" + strCurrentUrl + "</a>",
						false);
			}
			break;

		case "Internal Link":
			List<WebElement> strLstAllLinks = driver.findElements(By.tagName("a"));

			long startTime = System.currentTimeMillis();
			verifyBrokenLinks(strLstAllLinks, strPageName);
			long endTime = System.currentTimeMillis();
			double timeTaken = (endTime - startTime) / 1000.0;
			System.out.println("Time taken to complete API: " + timeTaken + " Seconds");
			long startTime1 = System.currentTimeMillis();
			generateLinkValidation_Report(strLstValidLinks, "2XX");
			generateLinkValidation_Report(strLstRedirectionLinks, "3XX");
			generateLinkValidation_Report(strLstDuplicate, "2XX/3XX");
			generateLinkValidation_Report(strLstCilentErrorLinks, "4XX");
			generateLinkValidation_Report(strLstServerErrorLinks, "5XX");
			long endTime1 = System.currentTimeMillis();
			double timeTaken1 = (endTime1 - startTime1) / 1000.0;
			System.out.println("Time taken to complete Report: " + timeTaken1 + " Seconds");
			System.out.println("Completed...");
			break;

		default:
			ALMFunctions.ThrowException("Test Data",
					"Only Pre-Defined Fields Type must be provided in the test data sheet",
					"Error - Unhandled Field Type " + strLinkType, false);

		}
	}

	/**
	 * Function to generate Report for given List of Url
	 *
	 * @param strLstLinks - List of url which to be generate report
	 * @return - no return parameters
	 */
	/**
	 * public void generateLinkValidation_Report(List<String> strLstLinks, String
	 * respCode)
	 *
	 * @return
	 */
	public void generateLinkValidation_Report(List<String> strLstLinks, String respCode) {
		String strAllDetails, strUrl, strType, strResponseCode = "";
		Iterator<String> linkIterators1 = strLstLinks.iterator();

		if (strLstLinks.isEmpty()) {
			return;
		}
		if (strLstLinks.get(0) != null) {
			strAllDetails = strLstLinks.get(0);
			report.updateTestLog("Identifying No of Links with Response Code " + respCode,
					respCode + ":" + "Links should be dispayed.",
					respCode + ":" + " No.Of Valid Links - " + strLstLinks.size(), Status.PASS);
		}
		while (linkIterators1.hasNext()) {
			strAllDetails = linkIterators1.next();
			String[] arr = strAllDetails.split("'");
			strUrl = arr[0];
			strType = arr[1];
			strResponseCode = arr[2];
			if (strResponseCode.startsWith("4") || strResponseCode.startsWith("5")) {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Broken Link in " + strType,
						"Expected Response Code: " + respCode, "Actual-ResponseCode:" + strResponseCode + "\n"
								+ " URL:<a href = " + strUrl + ">" + strUrl + "</a>",
						false);
				report.updateTestLog("Verify Broken Link in " + strType, "Expected Response Code: " + respCode,
						"Actual-ResponseCode:" + strResponseCode + "\n" + " URL:<a href = " + strUrl + ">" + strUrl
								+ "</a>",
						Status.FAIL);
			} else {
				report.updateTestLog("Verify Broken Link in " + strType, "Expected Response Code: " + respCode,
						"Actual-ResponseCode:" + strResponseCode + "\n" + " URL:<a href = " + strUrl + ">" + strUrl
								+ "</a>",
						Status.DONE);
			}

		}
	}

	/**
	 * Method to verify the Broken Links
	 *
	 * @return No return value
	 * @param strLstLinks,        list of url's need to pass with respect to
	 *                            segregation type.
	 * @param strSegregationType, segregation type of the website.
	 */

	/**
	 * public void verifyBrokenLinks(List<WebElement> strLstLinks, String
	 * strSegregationType)
	 *
	 * @return
	 */
	public void verifyBrokenLinks(List<WebElement> strLstLinks, String strSegregationType) {
		List<String> strLstNewUniqueList = new ArrayList<String>();

		List<String> strLstAllLinks = strLstLinks.parallelStream().map(strLink -> strLink.getAttribute("href"))
				.filter(strHref -> strHref != null && !strHref.isEmpty()).collect(Collectors.toList());

		System.out.println(strLstAllLinks);

		strLstAllLinks.parallelStream().forEach(strLink -> {
			int iResponseCode = getResponseExactCode(strLink);
			if (iResponseCode >= 200 && iResponseCode < 300) {
				strLstValidLinks.add(strLink + " '" + strSegregationType + " '" + iResponseCode + "'");
			} else if (iResponseCode >= 300 && iResponseCode < 400) {
				strLstRedirectionLinks.add(strLink + " '" + strSegregationType + " '" + iResponseCode + "'");
			} else if (iResponseCode >= 400 && iResponseCode < 500) {
				strLstCilentErrorLinks.add(strLink + " '" + strSegregationType + " '" + iResponseCode + "'");
			} else if (iResponseCode >= 500 && iResponseCode < 600) {
				strLstServerErrorLinks.add(strLink + " '" + strSegregationType + " '" + iResponseCode + "'");
			}
		});
		strLstLinks.parallelStream().map(strLink -> strLink.getAttribute("href"))
				.filter(strHref -> strHref != null && !strHref.isEmpty())
				.forEach(strLink -> System.out.println(strLink));
		System.out.println(strLstNewUniqueList);
// System.out.println("Completed...");
	}

	/**
	 * public void verifyLinkUnderHeaderSection()
	 *
	 * @return
	 */
	public void verifyLinkUnderHeaderSection() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strHeaderSection = dataTable.getData("General_Data", "HeaderSection");
		String strCardComponentName = dataTable.getData("General_Data", "CardName");
		String strPageName = driver.getTitle();
		By locator = new Common(strHeaderSection).headerElement;
		if (objectExists(locator, "isDisplayed", lngMinTimeOutInSeconds, strHeaderSection, "Element Existence",
				strPageName, false)) {
			pagescroll(locator, strPageName);
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Header Element Exists",
					strHeaderSection + " Header should be display in the " + strPageName,
					strHeaderSection + "Header is displayed in the " + strPageName, true);

		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Header Element Exists",
					strHeaderSection + " should be display in the " + strPageName,
					strHeaderSection + " is not displayed in the " + strPageName, true);

		}
		By cardComponent = new Common(strHeaderSection, strCardComponentName).cardlink;
		if (objectExists(cardComponent, "isDisplayed", lngMinTimeOutInSeconds, strCardComponentName,
				"Element Existence", strPageName, false)) {
			pagescroll(cardComponent, strPageName);
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);

			String hrefLink = driver.findElement(cardComponent).getAttribute("href");
			testRedirection(hrefLink);
			checkLinkAndClick(cardComponent, hrefLink);
		}
	}

	/**
	 * public void enterTextInKeywordField()
	 *
	 * @return
	 */
	public void enterTextInKeywordField() {
// setKeywordCount(strKeyword);
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String[] strTextField = dataTable.getData("General_Data", "TextFieldLabel").split("!");
		String strLabel = strTextField[0];
		String strText = strTextField[1];
		String strButton = dataTable.getData("General_Data", "Button");
		enterTextarea(strLabel, strText, driver.getTitle());
		clickButton(strButton, driver.getTitle());
		verifyObjectExistance(Common.searchCard, strText, "exist", "Card Compontent", driver.getTitle());
	}

	/**
	 * public void verifyAllLinksUnderHeaderSection()
	 *
	 * @return
	 */
	public void verifyAllLinksUnderHeaderSection() {
// setKeywordCount(strKeyword);
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		click(Common.gileadLogo, lngMinTimeOutInSeconds, "Button", " Gilead Logo", "Click", true);
		String[] strHeaderSections = dataTable.getData("General_Data", "HeaderSection").split("!");
		for (String strHeaderSection : strHeaderSections) {
			verifyAllLinkUnderHeaderSection(strHeaderSection);
		}
	}

	/**
	 * public void verifyElementPresent()
	 *
	 * @return
	 */
	public void verifyElementPresent() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strElement = dataTable.getData("General_Data", "ElementToCheck");
		verifyObjectExistance(new Common(strElement).headerElement, strElement, "exist", "ElementToBeCheck",
				driver.getTitle());
	}

	/**
	 * @param strFilePath     - File Path of the Excel File
	 * @param strSheetName    - Sheet Name in which data to be concatenated is
	 *                        present
	 * @param intRowNumber    - Index of the Row
	 * @param intColumnNumber - Index of the Column
	 * @param strValue        - Value to be written
	 */
	@SuppressWarnings("deprecation")
	/**
	 * public void writeData(String strFilePath, String strSheetName, int
	 * intRowNumber, int intColumnNumber,
	 *
	 * @return
	 */
	public void writeData(String strFilePath, String strSheetName, int intRowNumber, int intColumnNumber,
			String strValue) {
		String strLockFile = "PutData_Lock.xls";
		FileLockMechanism objFileLockMechanism = new FileLockMechanism(
				Long.valueOf(properties.getProperty("FileLockTimeOut")));
		FileLock objFileLock = objFileLockMechanism.SetLockOnFile(strLockFile);
		if (objFileLock != null) {
			synchronized (CommonFunctions.class) {
				XSSFWorkbook wb = openExcelFile(strFilePath);
				Sheet sheet = getSheetFromXLSWorkbook(wb, strSheetName, strFilePath);
				Row row = sheet.getRow(intRowNumber);
				Cell cell = row.createCell(intColumnNumber);
				cell.setCellType(Cell.CELL_TYPE_STRING);
				cell.setCellValue(strValue);
				FileOutputStream fileOutputStream;
				try {
					fileOutputStream = new FileOutputStream(strFilePath);
				} catch (FileNotFoundException e) {
					e.printStackTrace();
					throw new FrameworkException("The specified file \"" + strFilePath + "\" does not exist!");
				}
				try {
					wb.write(fileOutputStream);
					fileOutputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
					throw new FrameworkException(
							"Error while writing into the specified Excel workbook \"" + strFilePath + "\"");
				}
			}
			objFileLockMechanism.ReleaseLockOnFile(objFileLock, strLockFile);
		}
	}

	/**
	 * Function to manage and switch to new window which opened recently
	 *
	 * @param No parameters
	 * @return No return value
	 */

	/**
	 * public void manageAndSwitchNewWindow()
	 *
	 * @return
	 */
	public void manageAndSwitchNewWindow() {
		if (!windowName.isEmpty()) {
			if (driverUtil.waitUntilWindowCountAvailable(windowName.size() + 1, "New Window", lngMinTimeOutInSeconds,
					false)) {
				updateWindowHandle();
			} else if (driverUtil.waitUntilWindowCountAvailable(windowName.size() - 2, "New Window",
					lngMinTimeOutInSeconds, false)) {
				updateWindowHandle();
			} else if (driverUtil.waitUntilWindowCountAvailable(windowName.size() - 1, "New Window",
					lngMinTimeOutInSeconds, false)) {
				updateWindowHandle();
			}
		} else {
			ALMFunctions.ThrowException("Verify New Window Available",
					"New window title as " + driver.getTitle() + " should be displayed",
					"New window " + driver.getTitle() + " is NOT displayed", true);
		}
		if (windowName.size() > 1) {
			report.updateTestLog("Window Count", "Window Count should be more than 1",
					"Actual Window Count is " + windowName.size(), Status.DONE);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Switch Window",
					"Switched to new window title as " + driver.getTitle(),
					"Not Switched to new window Successfully and title as " + driver.getTitle(), true);
		}

		driver.switchTo().window(windowName.get("Window" + windowName.size()));
		driver.manage().window().maximize();
		ALMFunctions.UpdateReportLogAndALMForPassStatus("Switch Window",
				"Switched to new window title as " + driver.getTitle(),
				"Switched to new window Successfully and title as " + driver.getTitle(), true);
	}

	/**
	 * Function to manage and switch to previous window after closing the active
	 * window
	 *
	 * @param No parameters
	 * @return No return value
	 */
	/**
	 * public void closeAndSwitchPreviousWindow()
	 *
	 * @return
	 */
	public void closeAndSwitchPreviousWindow() {
		driver.close();
		windowName.remove("Window" + windowName.size());
		driver.switchTo().window(windowName.get("Window" + windowName.size()));
		report.updateTestLog("Close And Switch PreviousWindow", "New window should be closed -" + driver.getTitle(),
				"New window is closed as per expected -" + driver.getTitle(), Status.DONE);
		driver.manage().window().maximize();
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds, "Window Switch");
		ALMFunctions.UpdateReportLogAndALMForPassStatus("Default Window",
				"Switched to Default window title as " + driver.getTitle(),
				"Switched to Default window Successfully and title as " + driver.getTitle(), true);
	}

	/**
	 * Function to handle window values
	 *
	 * @param No parameters
	 * @return No return value
	 */
	/**
	 * public void updateWindowHandle()
	 *
	 * @return
	 */
	public void updateWindowHandle() {
		if (windowName.size() < driver.getWindowHandles().size()) {
			for (String windowHand : driver.getWindowHandles()) {
				if (!windowName.containsValue(windowHand)) {
					windowName.put("Window" + (windowName.size() + 1), windowHand);
				}
			}
		}
		if (windowName.size() > driver.getWindowHandles().size()) {
			List<String> windowToRemove = new ArrayList<String>();
			for (String strWindowName : windowName.values()) {
				if (!driver.getWindowHandles().contains(strWindowName)) {
					windowToRemove.add(strWindowName);

				}
			}
			windowName.values().removeAll(windowToRemove);
		}

	}

	/**
	 * Function to enter a value in a textbox
	 *
	 * @param strLabel    Name of the textbox
	 * @param strValue    Value to be entered in the textbox
	 * @param strPageName Name of the page
	 */
	/**
	 * public void enterTextbox(String strLabel, String strValue, String
	 * strPageName)
	 *
	 * @return
	 */
	public void enterTextbox(String strLabel, String strValue, String strPageName) {
		By locator = null;
		if (strLabel.contains("!")) {
			String[] strProperties = strLabel.split("!");
			locator = new Common(strProperties[0], strProperties[1]).textbox;
			strLabel = strProperties[0];
		} else {
			locator = new Common(strLabel).textbox;
		}

		if (objectExists(locator, "isDisplayed", timeOutInSeconds, strLabel, "TextBox", strPageName, false)) {
			driverUtil.waitUntilStalenessOfElement(locator, 1, strPageName);
			pagescroll(locator, strPageName);
			if (!strLabel.equalsIgnoreCase("Patient's Phone Number")) {
				click(locator, lngMinTimeOutInSeconds, strLabel, strValue, strPageName, false);
				clear(locator, lngPagetimeOutInSeconds, strValue, strPageName);

			}
			if (strLabel.equalsIgnoreCase("Patient's Phone Number")) {
				WebElement element = driver.findElement(locator);
				Actions actions = new Actions(driver.getWebDriver());
				actions.moveToElement(element).click().sendKeys(strValue).perform();
			}
// driverUtil.waitUntilStalenessOfElement(locator, 3, strPageName);
			sendkeys(locator, lngPagetimeOutInSeconds, strValue, strLabel, "Textbox", strPageName, true);
		} else {
			ALMFunctions.ThrowException("Verify Value is enterted in Textbox",
					strLabel + " textbox should be availble to enter", strLabel + " textbox is not available to enter",
					true);
		}
	}

	/**
	 * public void verifyContactLearnMoreBtn(String strButton)
	 *
	 * @return
	 */
	public void verifyContactLearnMoreBtn(String strButton) {
		By locator = new Common(strButton).searchCardComponentBtn;
		String strPageName = driver.getTitle();
		List<WebElement> eleSearchCardComponents = driver.findElements(locator);
		for (WebElement eleSearchCardComponent : eleSearchCardComponents) {
			if (strButton.equalsIgnoreCase("Contact")) {
				if (objectExists(locator, "isDisplayed", timeOutInSeconds, strButton, "Button", strPageName, false)) {
					click(eleSearchCardComponent, lngMinTimeOutInSeconds, strButton, "Button", strPageName, true);
					driverUtil.waitUntilElementVisible(Common.contactUsPopup, lngMinTimeOutInSeconds, strPageName,
							strButton, strPageName, false);
					ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify " + strButton + " button is present",
							strButton + " information Popup should be opened",
							strButton + " information Popup is opened", true);
					click(Common.contactUsCloseBtn, lngMinTimeOutInSeconds, strButton, "Close Button", strPageName,
							true);
				} else {
					ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify " + strButton + " button is present",
							strButton + " information Popup Should be opened ",
							strButton + " information Popup is not opened", true);
				}
			} else {
				String strID = eleSearchCardComponent.getAttribute("data-nctid");
				System.out.println(strID);
				String baseUrl = "https://wwwt103.gileadclinicaltrials.com/study?nctid={nctid}&lat=&lng=&locationCountry=&distance=100";
// Dynamic nctid String dynamicNctid = "NCT00000000";
// Change this value as needed // Replace the placeholder with the dynamic nctid
				String finalUrl = baseUrl.replace("{nctid}", strID);
				testRedirection(finalUrl);
			}

		}

	}

	/**
	 * Method to get Response Code for link
	 *
	 * @param strUrl - pass the Url to get response code
	 * @return iRespCode - integer value
	 */

	/**
	 * public int getResponseExactCode(String strUrl)
	 *
	 * @return
	 */
	public int getResponseExactCode(String strUrl) {
		HttpURLConnection huc = null;
		int iRespCode = 0;

		DesiredCapabilities handlSSLErr = new DesiredCapabilities();
		handlSSLErr.setCapability(CapabilityType.ACCEPT_SSL_CERTS, true);
// driver = new CraftDriver(handlSSLErr);
		try {
			if (!(strUrl.contains("mailto:") || strUrl.contains("javascript") || strUrl.contains("tel:")
					|| strUrl.startsWith("http:") || strUrl.contains("businesswire.") || strUrl.startsWith("file:"))) {
				huc = (HttpURLConnection) (new URL(strUrl).openConnection());
				huc.setInstanceFollowRedirects(false);
				huc.setRequestProperty("User-Agent", "Mozilla/5.0...");
				huc.connect();
				iRespCode = huc.getResponseCode();
			}
		} catch (MalformedURLException e) {
			System.out.println(e + " URL " + strUrl);
			e.printStackTrace();

		} catch (Exception e) {
			if (!(strUrl.contains("facebook") || strUrl.contains("twitter") || strUrl.contains("youtube")
					|| strUrl.contains("linked") || strUrl.contains("yammer"))) {
				e.printStackTrace();
				strLstExpectionList.add(strUrl);
				System.out.println(e + " URL " + strUrl);
			}
		}
		return iRespCode;
	}

	/**
	 * Function to select a value in a dropdown
	 *
	 * @param strLabel    Name of the dropdown
	 * @param strValue    Value to be selected in the dropdown
	 * @param strPageName Name of the page
	 */
	/**
	 * public void selectDropdown(String strLabel, String strValue, String
	 * strPageName)
	 *
	 * @return
	 */
	public void selectDropdown(String strLabel, String strValue, String strPageName) {
		By locator;

		locator = new Common(strLabel).dropdown;
		driverUtil.waitUntilStalenessOfElement(locator, 2, strPageName);
		if (objectExists(locator, "isDisplayed", timeOutInSeconds, strLabel, "Dropdown button", strPageName, false)) {
			pagescroll(locator, strPageName);
			driverUtil.waitUntilStalenessOfElement(locator, 2, strPageName);
			selectDropdownByvalue(locator, strLabel, strValue, strPageName, true);
		} else {
			ALMFunctions.ThrowException("Verify dropdown button is clicked",
					strLabel + " dropdown button should be clicked",
					strLabel + " dropdown button is not available to click", true);
		}
	}

	/**
	 * Function to select a value in a dropdown in Tab
	 *
	 * @param strLabel    Name of the dropdown
	 * @param strValue    Value to be selected in the dropdown
	 * @param strPageName Name of the page
	 */
	/**
	 * public void selectDropdownInTab(String strLabel, String strValue, String
	 * strPageName)
	 *
	 * @return
	 */
	public void selectDropdownInTab(String strLabel, String strValue, String strPageName) {
		By locator;

		locator = new Common(strLabel).button;
		driverUtil.waitUntilStalenessOfElement(locator, 2, strPageName);
		if (objectExists(locator, "isDisplayed", timeOutInSeconds, strLabel, "Tab Dropdown button", strPageName,
				false)) {
			clickByJS(locator, lngPagetimeOutInSeconds, strLabel, "Tab Dropdown button", strPageName, true);
			driverUtil.waitUntilStalenessOfElement(new Common(strValue).listViewValue, 1, strPageName);
			pagescroll(new Common(strValue).listViewValue, strPageName);
			click(new Common(strValue).listViewValue, lngPagetimeOutInSeconds, strLabel, strValue, strPageName, true);
		} else {
			ALMFunctions.ThrowException("Verify dropdown button is clicked",
					strLabel + " dropdown button should be clicked",
					strLabel + " dropdown button is not available to click", true);
		}
// waitUntilPageReadyStateCompleteLightning(lngPagetimeOutInSeconds, "Event
// Detail");

	}

	/**
	 * Function to enter a value in a textarea
	 *
	 * @param strLabel    Name of the textarea
	 * @param strValue    Value to be entered in the textarea
	 * @param strPageName Name of the page
	 */
	/**
	 * public void enterTextarea(String strLabel, String strValue, String
	 * strPageName)
	 *
	 * @return
	 */
	public void enterTextarea(String strLabel, String strValue, String strPageName) {
		By locator;
		locator = new Common(strLabel).textarea;
		if (objectExists(locator, "isDisplayed", timeOutInSeconds, strLabel, "TextArea", strPageName, false)) {
			sendkeys(locator, lngPagetimeOutInSeconds, strValue, strLabel, "TextArea", strPageName, true);
			pagescroll(locator, strPageName);
			sendkeys(locator, lngPagetimeOutInSeconds, Keys.TAB, strLabel, strPageName, false);
		} else {
			ALMFunctions.ThrowException("Verify Value is enterted in Textarea",
					strLabel + " textarea should be availble to enter",
					strLabel + " textarea is not available to enter", true);
		}
	}

	/**
	 * Method to click Menu
	 *
	 * @param strValue,    value to click link
	 * @param strPageName, Page Name in which the control is available
	 * @return No return value
	 */

	/**
	 * public void clickMenu(String strMainMenu, String strSubMenu, String
	 * strPageName)
	 *
	 * @return
	 */
	public void clickMenu(String strMainMenu, String strSubMenu, String strPageName) {
		By navigation = null;
		By subNavigation = null;
		Common nav1 = new Common(strMainMenu);
		navigation = nav1.headerMenu;
		Common nav2 = new Common(strSubMenu);
		subNavigation = nav2.subMenuDD;

		if (objectExists(navigation, "isDisplayed", lngMinTimeOutInSeconds, strMainMenu, "Main Menu", strPageName,
				false)) {
			driverUtil.waitUntilStalenessOfElement(navigation, strPageName);
			pagescroll(navigation, strPageName);
			click(navigation, lngMinTimeOutInSeconds, strMainMenu, "Main Menu", strPageName, true);
		} else {
			ALMFunctions.ThrowException(strMainMenu,
					"Main Menu - " + strMainMenu + " should be displayed in " + strPageName,
					"Error - Main Menu - " + strMainMenu + " is not available in " + strPageName, true);
		}
		if (objectExists(subNavigation, "isEnabled", lngMinTimeOutInSeconds, strMainMenu, "Sub Menu", strPageName,
				false)) {
			driverUtil.waitUntilStalenessOfElement(subNavigation, strPageName);
			pagescroll(subNavigation, strPageName);
			click(subNavigation, lngMinTimeOutInSeconds, strSubMenu, "Sub Menu", strPageName, true);
		} else {
			ALMFunctions.ThrowException(strSubMenu,
					"Sub Menu - " + strSubMenu + " should be displayed in " + strPageName,
					"Error - Sub Menu - " + strSubMenu + " is not available in " + strPageName, true);
		}
	}

	/**
	 * Method to click button
	 *
	 * @param strButtonLabel, value to click button
	 * @param strPageName,    Page Name in which the control is available
	 * @return No return value
	 */
	/**
	 * public void clickButton(String strButtonLabel, String strPageName)
	 *
	 * @return
	 */
	public void clickButton(String strButtonLabel, String strPageName)

	{
		By locator;

		locator = new Common(strButtonLabel).button;

		if (objectExists(locator, "isDisplayed", timeOutInSeconds, strButtonLabel, "Button", strPageName, false)) {

			driverUtil.waitUntilStalenessOfElement(locator, 2, strPageName);

			pagescroll(locator, strPageName);
			clickByJS(locator, lngPagetimeOutInSeconds, strButtonLabel, "Button", strPageName, true);
		} else {
			ALMFunctions.ThrowException("Verify Button is clicked", strButtonLabel + " button should be clicked",
					strButtonLabel + " button is not available to click", true);
		}

	}

	/**
	 * Method to click button -> navigation steps passing through dataTable
	 *
	 * @param strButtonLabel, value to click button
	 * @param strPageName,    Page Name in which the control is available
	 * @return No return value
	 */
	/**
	 * public void navigationSteps(String strPageName)
	 *
	 * @return
	 */
	public void navigationSteps(String strPageName)

	{
		String[] strNavigationLinks = dataTable.getData("General_Data", "Navigation_Steps").split("->");
		By locator;
		for (String strNavigationLink : strNavigationLinks) {
			locator = new Common(strNavigationLink).button;
			if (objectExists(locator, "isDisplayed", timeOutInSeconds, strNavigationLink, "Button", strPageName,
					false)) {
				driverUtil.waitUntilStalenessOfElement(locator, 2, strPageName);
				pagescroll(locator, strPageName);
				clickByJS(locator, lngPagetimeOutInSeconds, strNavigationLink, "Button", strPageName, true);
			} else {
				ALMFunctions.ThrowException("Verify Button is clicked", strNavigationLink + " button should be clicked",
						strNavigationLink + " button is not available to click", true);
			}
		}

	}

	/**
	 * public void clickDownload()
	 *
	 * @return
	 */
	public void clickDownload() {
		new Common("Select all");
		if (objectExists(new Common("Select all").resourcesBtn, "isDisplayed", lngMinTimeOutInSeconds, "Button", "Tab",
				driver.getTitle(), false)) {
			clickByJS(new Common("Select all").resourcesBtn, lngMinTimeOutInSeconds, "Button", "Select All", "Button",
					true);
			clickByJS(new Common("Download").resourcesBtn, lngMinTimeOutInSeconds, "Button", "Select All", "Button",
					true);
		}

		List<WebElement> downloadBtns = driver.findElements(Common.btnDownload);
		if (objectExists(Common.btnDownload, "isDisplayed", lngMinTimeOutInSeconds, "Button", "Tab", driver.getTitle(),
				false)) {
			for (WebElement downloadBtn : downloadBtns) {
				clickByJS(downloadBtn, lngMinTimeOutInSeconds, "Button", "Download Button", "Button", true);
			}
		}
	}

	/**
	 * Method to click Tab
	 *
	 * @param strValue,    value to click link
	 * @param strPageName, Page Name in which the control is available
	 * @return No return value
	 */

	/**
	 * public void clickTab(String strTab, String strPageName)
	 *
	 * @return
	 */
	public void clickTab(String strTab, String strPageName) {
		Common objTab = new Common(strTab);
		if (objectExists(objTab.tab, "isDisplayed", lngMinTimeOutInSeconds, strTab, "Tab", strPageName, false)) {
// driverUtil.waitUntilStalenessOfElement(objTab.tab, strPageName);
			driverUtil.waitUntilStalenessOfElement(objTab.tab, strPageName);
			pagescroll(objTab.tab, strPageName);
			clickByJS(objTab.tab, lngMinTimeOutInSeconds, strTab, "Tab", strPageName, true);
// driverUtil.waitUntilElementInVisible(Common.loadingSpinner, "Loading
// spinner", "Spinner", "Case");
		} else {
			ALMFunctions.ThrowException("Verify Tab is clicked",
					"Tab - " + strTab + " should be displayed in " + strPageName,
					"Error - Tab - " + strTab + " is not available in " + strPageName, true);
		}
	}

	/**
	 * Method to click Inline Edit button
	 *
	 * @param strButtonLabel, value to click Inline Edit button
	 * @param strPageName,    Page Name in which the control is available
	 * @return No return value
	 */
	/**
	 * public void clickInlineEditButton(String strButtonLabel, String strPageName)
	 *
	 * @return
	 */
	public void clickInlineEditButton(String strButtonLabel, String strPageName)

	{
		driverUtil.waitUntilStalenessOfElement(new Common(strButtonLabel).button, 2, strPageName);
		if (objectExists(new Common(strButtonLabel).inlineEditbutton, "isDisplayed", timeOutInSeconds, strButtonLabel,
				"Button", strPageName, false)) {
			/*
			 * driverUtil.waitUntilStalenessOfElement(new Common(strButtonLabel).button,
			 * lngMinTimeOutInSeconds, strPageName);
			 */
			pagescroll(new Common(strButtonLabel).inlineEditbutton, strPageName);
			clickByJS(new Common(strButtonLabel).inlineEditbutton, lngPagetimeOutInSeconds, strButtonLabel,
					"Inline Edit Button", strPageName, true);
// driverUtil.waitUntilElementInVisible(Common.loadingSpinner, "Loading
// spinner", "Spinner", "Case");
		} else {
			ALMFunctions.ThrowException("Verify Inline Edit Button is clicked",
					strButtonLabel + " Inline Edit button should be clicked",
					strButtonLabel + " Inline Edit button is not available to click", true);
		}

	}

	/**
	 * Method to enter value and Select it in Lookup
	 *
	 * @param strLabel Name of the combobox
	 * @param strValue Value to be entered in the combobox
	 * @return No return value
	 */
	/**
	 * public void enterComboBox(String strLabel, String strValueToBeSelected,
	 * String strPageName)
	 *
	 * @return
	 */
	public void enterComboBox(String strLabel, String strValueToBeSelected, String strPageName) {
		By locator;

		locator = new Common(strLabel).textbox;

		if (objectExists(locator, "isDisplayed", timeOutInSeconds, strLabel, "Combobox", strPageName, false)) {
			pagescroll(locator, strPageName);
// sendkeys(locator, lngPagetimeOutInSeconds, strValueToBeSelected, strLabel,
// "Combobox", strPageName, true);
			if (strLabel.contains("Physician")) {
				String[] strPhysician = strValueToBeSelected.split(" ");
				String strValueToBeEntered = strPhysician[0];
				sendkeys(locator, lngPagetimeOutInSeconds, strValueToBeEntered, strLabel, "Combobox", strPageName,
						true);
			} else {
				sendkeys(locator, lngPagetimeOutInSeconds, strValueToBeSelected, strLabel, "Combobox", strPageName,
						true);
			}
			sendkeys(locator, lngMinTimeOutInSeconds, Keys.ENTER, "textbox", strPageName, false);
// driverUtil.waitUntilElementInVisible(Common.loadingSpinner, "Loading
// spinner", "Spinner", "Case");
			driverUtil.waitUntilStalenessOfElement(new Common(strValueToBeSelected).comboboxValue, 3, strPageName);
			click(new Common(strValueToBeSelected).comboboxValue, lngPagetimeOutInSeconds, strValueToBeSelected,
					"Combobox Value", strPageName, true);
		} else {
			ALMFunctions.ThrowException("Verify ComboBox is clicked", strLabel + " ComboBox should be clicked",
					strLabel + " ComboBox is not available to click", true);
		}
	}

	/**
	 * Method to verify multiple field and its values
	 *
	 * @param strValues,   expected values with split of '!'
	 * @param strPageName, Page Name in which the control is available
	 * @return No return value
	 */

	/**
	 * public void verifyMultipleFieldValues(String strKeyword, String strValues,
	 * String strPageName)
	 *
	 * @return
	 */
	public void verifyMultipleFieldValues(String strKeyword, String strValues, String strPageName) {

		setKeywordCount(strKeyword);

		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
// driverUtil.waitUntilElementInVisible(Common.loadingSpinner, "Loading
// spinner", "Spinner", "Case");
		String[] strInputValues = strValues.split("!!");
		List<Boolean> blnFlag = new ArrayList<Boolean>();
		String strDescription = "Verify the multiple field values:";
		String strExpected = "Expected: <br> <br>";
		String strActual = "Actual : <br> <br>";
		for (int i = 0; i < strInputValues.length; i++) {
			By locator = null;
			String strInputs = strInputValues[i];
			String[] strSplitInputs = strInputs.split("!");
			String strElementType = strSplitInputs[0];
			strElementType = strElementType.toLowerCase();
			String[] strElementLabelAndValue = strSplitInputs[1].split("=");
			String strLabelName = strElementLabelAndValue[0];
			String strLabelValue = strElementLabelAndValue[1];
			strExpected = strExpected + strLabelName + " value should be displayed as : " + strLabelValue + "<br> <br>";

			String strActualValue = null;
			driverUtil.waitUntilStalenessOfElement(locator, 3, strPageName);
			switch (strElementType) {
			case "readonly":
				locator = new Common(strLabelName).readOnlyFields;
				if (driverUtil.waitUntilElementLocated(locator, lngMinTimeOutInSeconds, strLabelName, "Textbox",
						strPageName, false)) {

					pagescroll(locator, strPageName);
					strActualValue = driver.findElement(locator).getText().trim();
					if (strActualValue.trim().equalsIgnoreCase(strLabelValue.trim())) {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue
								+ "<br> <br>";
					} else {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue
								+ "<br> <br>";
						blnFlag.add(false);
					}
				} else {
					ALMFunctions.ThrowException(strLabelName,
							strLabelName + " label should be displayed in the " + strPageName + " page",
							"No Such " + strLabelName + " label is found in the " + strPageName + " page", true);
				}
				break;
			case "textbox":
				locator = new Common(strLabelName).textbox;

				if (driverUtil.waitUntilElementLocated(locator, lngMinTimeOutInSeconds, strLabelName, "Textbox",
						strPageName, false)) {

					pagescroll(locator, strPageName);
					driverUtil.waitUntilStalenessOfElement(locator, strPageName);
					strActualValue = driver.findElement(locator).getText().trim();
					if (strActualValue.isEmpty()) {
						strActualValue = driver.findElement(locator).getAttribute("value");
					}
					if (strActualValue.trim().equalsIgnoreCase(strLabelValue.trim())) {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue
								+ "<br> <br>";
					} else {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue
								+ "<br> <br>";
						blnFlag.add(false);
					}
				} else {
					ALMFunctions.ThrowException(strLabelName,
							strLabelName + " label should be displayed in the " + strPageName + " page",
							"No Such " + strLabelName + " label is found in the " + strPageName + " page", true);
				}
				break;
			case "dropdown":
				locator = new Common(strLabelName).dropdownValue;

				if (driverUtil.waitUntilElementLocated(locator, lngMinTimeOutInSeconds, strLabelName, "Textbox",
						strPageName, false)) {

					pagescroll(locator, strPageName);

					strActualValue = driver.findElement(locator).getText().trim();

					if (strActualValue.trim().equalsIgnoreCase(strLabelValue.trim())) {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue + " in "
								+ strLabelName + " dropdown" + "<br> <br>";
					} else {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue + " in "
								+ strLabelName + " dropdown" + "<br> <br>";
						blnFlag.add(false);
					}
				} else {
					ALMFunctions.ThrowException(strLabelName,
							strLabelName + " label should be displayed in the " + strPageName + " page",
							"No Such " + strLabelName + " label is found in the " + strPageName + " page", true);
				}
				break;

			case "combobox":
				locator = new Common(strLabelName).combobox;
				if (driverUtil.waitUntilElementLocated(locator, lngMinTimeOutInSeconds, strLabelName, "Textbox",
						strPageName, false)) {
					driverUtil.waitUntilStalenessOfElement(locator, strPageName);
					pagescroll(locator, strPageName);
					strActualValue = driver.findElement(locator).getAttribute("data-value").trim();
					if (strActualValue.trim().equalsIgnoreCase(strLabelValue.trim())) {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue
								+ "<br> <br>";
					} else {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue
								+ "<br> <br>";
						blnFlag.add(false);
					}
				} else {
					ALMFunctions.ThrowException(strLabelName,
							strLabelName + " label should be displayed in the " + strPageName + " page",
							"No Such " + strLabelName + " label is found in the " + strPageName + " page", true);
				}
				break;

			case "highlightitem":
				locator = new Common(strLabelName).highlightsItem;
				if (driverUtil.waitUntilElementLocated(locator, lngMinTimeOutInSeconds, strLabelName, "Textbox",
						strPageName, false)) {
					driverUtil.waitUntilStalenessOfElement(locator, strPageName);
					pagescroll(locator, strPageName);
					strActualValue = driver.findElement(locator).getText().trim();
					driver.findElement(By.xpath("//*[text()='Account']//ancestor::records-highlights-details-item//a"))
							.getText().trim();
					if (strActualValue.trim().equalsIgnoreCase(strLabelValue.trim())) {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue
								+ "<br> <br>";
					} else {
						strActual = strActual + strLabelName + " value is displayed as : " + strActualValue
								+ "<br> <br>";
						blnFlag.add(false);
					}
				} else {
					ALMFunctions.ThrowException(strLabelName,
							strLabelName + " label should be displayed in the " + strPageName + " page",
							"No Such " + strLabelName + " label is found in the " + strPageName + " page", true);
				}
				break;
			default:
				ALMFunctions.ThrowException("Test Data",
						"Only Pre-Defined Fields Type must be provided in the test data sheet",
						"Error - Unhandled Field Type " + strElementType, false);
				break;
			}

		}

		if (!blnFlag.contains(false)) {
// ALMFunctions.UpdateReportLogAndALMForPassStatus(strDescription, strExpected,
// strActual, true);
			report.updateTestLogs(strDescription, strExpected, strActual, Status.PASS);
// .UpdateReportLogsDSAndALMForPassStatus(strDescription, strExpected,
// strActual, true);
		} else {
			report.updateTestLogs(strDescription, strExpected, strActual, Status.FAIL);
// ALMFunctions.UpdateReportLogsDSAndALMForFailStatus(strDescription,
// strExpected, strActual, true);
		}

	}

	/**
	 * Method to verify multiple object state like editable or non editable fields
	 *
	 * @param strValue,    expected value and its state given with split of '!'
	 * @param strPageName, Page Name in which the control is available
	 * @return No return value
	 */
	/**
	 * public void verifyMultipleObjectState(String strKeyword, String strValues,
	 * String strPageName)
	 *
	 * @return
	 */
	public void verifyMultipleObjectState(String strKeyword, String strValues, String strPageName) {
		setKeywordCount(strKeyword);
		String[] strInputValues = strValues.split("!!");
		List<Boolean> blnFlag = new ArrayList<Boolean>();
		String strDescription = "Verify the multiple field objectstate:";
		String strExpected = "Expected: <br> <br>";
		String strActual = "Actual : <br> <br>";
		for (int intInputValue = 0; intInputValue < strInputValues.length; intInputValue++) {
			By locator = null;
			String strInputs = strInputValues[intInputValue];
			String[] strSplitInputs = strInputs.split("!");
			String strElementType = strSplitInputs[0];
			strElementType = strElementType.toLowerCase();
			String strLabelName = strSplitInputs[1];
			strExpected = strExpected + "'" + strLabelName + "' field should be available <br> <br>";
			switch (strElementType) {
			case "textbox":
				locator = new Common(strLabelName).textbox;
				if (objectExists(locator, "isDisplayed", timeOutInSeconds, strLabelName, strElementType, strPageName,
						false)) {
					pagescroll(locator, strPageName);
					strActual = strActual + strLabelName + " field is in : " + strElementType + " state <br> <br>";

				} else {
					blnFlag.add(false);
					strActual = strActual + strLabelName + " field is not available <br> <br>";
				}
				break;
			case "dropdown":
				locator = new Common(strLabelName).dropdown;
				if (objectExists(locator, "isDisplayed", timeOutInSeconds, strLabelName, strElementType, strPageName,
						false)) {
					pagescroll(locator, strPageName);
					strActual = strActual + strLabelName + " field is in : " + strElementType + " state <br> <br>";

				} else {
					blnFlag.add(false);
					strActual = strActual + strLabelName + " field is not available <br> <br>";
				}
				break;
			case "checkbox":
				locator = new Common(strLabelName).checkbox;

				if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, strLabelName, strElementType,
						strPageName, false)) {
					pagescroll(locator, strPageName);
					strActual = strActual + "'" + strLabelName + "' field is in : " + strElementType
							+ " state <br> <br>";

				} else {
					blnFlag.add(false);
					strActual = strActual + "'" + strLabelName + "' field is not available <br> <br>";
				}
				break;
			case "label":
				locator = new Common(strLabelName).label;

				if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, strLabelName, strElementType,
						strPageName, false)) {
					pagescroll(locator, strPageName);
					strActual = strActual + "'" + strLabelName + "' field is available <br> <br>";

				} else {
					blnFlag.add(false);
					strActual = strActual + "'" + strLabelName + "' field is not available <br> <br>";
				}
				break;
			case "defaultvalue":
				locator = new Common(strLabelName).message;

				if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, strLabelName, strElementType,
						strPageName, false)) {
					pagescroll(locator, strPageName);
					strActual = strActual + "'" + strLabelName + "' field is available <br> <br>";

				} else {
					blnFlag.add(false);
					strActual = strActual + "'" + strLabelName + "' field is not available <br> <br>";
				}
				break;
			case "task":
				locator = new Common(strLabelName).message;

				if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, strLabelName, strElementType,
						strPageName, false)) {
					pagescroll(locator, strPageName);
					strActual = strActual + "'" + strLabelName + "' task is available <br> <br>";

				} else {
					blnFlag.add(false);
					strActual = strActual + "'" + strLabelName + "' task is not available <br> <br>";
				}
				break;
			case "defaultdropdownvalue":
				String[] strValue = strLabelName.split("\\|");
				strLabelName = strValue[0];
				String strLabelValue = strValue[1];
				locator = new Common(strLabelName, strLabelValue).defaultDropdownValue;

				if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, strLabelName, strElementType,
						strPageName, false)) {
					pagescroll(locator, strPageName);
					strActual = strActual + "'" + strLabelName + "' field's default Value is displayed as '"
							+ strLabelValue + "' <br> <br>";

				} else {
					blnFlag.add(false);
					strActual = strActual + "'" + strLabelName + "' field's default Value is not displayed as '"
							+ strLabelValue + "' <br> <br>";
				}
				break;
			case "button":
				locator = new Common(strLabelName).button;
				if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, strLabelName, strElementType,
						strPageName)) {
// if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds,
// strLabelName, strElementType, strPageName,false)) {
					pagescroll(locator, strPageName);
					strActual = strActual + "'" + strLabelName + "' button is available <br> <br>";

				} else {
					blnFlag.add(false);
					strActual = strActual + "'" + strLabelName + "' button is not available <br> <br>";
				}
				break;
			default:
				ALMFunctions.ThrowException("Test Data",
						"Only Pre-Defined Fields Type must be provided in the test data sheet",
						"Error - Unhandled Field Type " + strElementType, false);
				break;
			}

		}

		if (!blnFlag.contains(false)) {
// report.updateTestLog(strDescription, strExpected, strActual, Status.PASS);
			report.updateTestLogs(strDescription, strExpected, strActual, Status.PASS);
		} else {
// report.updateTestLog(strDescription, strExpected, strActual, Status.FAIL);
			report.updateTestLogs(strDescription, strExpected, strActual, Status.FAIL);
		}

	}

	/**
	 * Method to Click Check box
	 *
	 * @param locator,      locator of checkbox
	 * @param strFieldName, value to click check box
	 * @param strPageName,  Page Name in which the control is available
	 * @return No return value
	 */

	/**
	 * public void clickCheckBox(String strFieldName, String strPageName)
	 *
	 * @return
	 */
	public void clickCheckBox(String strFieldName, String strPageName) {
		By locator = new Common(strFieldName).textbox;
		if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, strFieldName, "check box", strPageName,
				false)) {
			pagescroll(locator, strPageName);
			boolean blnChecked = driver.findElement(locator).isSelected();
			if (blnChecked) {
				report.updateTestLog(strFieldName, "Checkbox should be able to check",
						strFieldName + " checkbox is already checked", Status.DONE);
			} else {
				clickByJS(locator, timeOutInSeconds, strFieldName, "check box", strPageName, true);
			}
		} else {
			ALMFunctions.ThrowException("Checkbox", strFieldName + " should be displayed in " + strPageName,
					"Error - " + strFieldName + " is not available for editing in " + strPageName, true);
		}

	}

	/**
	 * public void verifyAllLinkUnderHeaderSection(String strHeaderName)
	 *
	 * @return
	 */
	public void verifyAllLinkUnderHeaderSection(String strHeaderName) {
		String strPageName = driver.getTitle();
		By locator = new Common(strHeaderName).headerElement;
		if (objectExists(locator, "isDisplayed", lngMinTimeOutInSeconds, strHeaderName, "Element Existence",
				strPageName, false)) {
			pagescroll(locator, strPageName);
			driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Header Element Exists",
					strHeaderName + " Header should be display in the " + strPageName,
					strHeaderName + " Header is displayed in the " + strPageName, true);

		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Header Element Exists",
					strHeaderName + " should be display in the " + strPageName,
					strHeaderName + " is not displayed in the " + strPageName, true);

		}
		By cardComponentHeader = new Common(strHeaderName).learnMoreLink;
		String dataGtmValue = null;
		List<WebElement> cardComponents = driver.findElements(cardComponentHeader);
		for (WebElement cardComponent : cardComponents) {
			driverUtil.waitUntilElementVisible(cardComponent, lngMinTimeOutInSeconds, strHeaderName, dataGtmValue,
					strPageName);
			if (objectExists(cardComponent, "isDisplayed", lngMinTimeOutInSeconds, strHeaderName, "Element Existence",
					strPageName, false)) {

				if (cardComponents.indexOf(cardComponent) == 0) {
					pagescroll(cardComponent, strPageName);
					driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
					ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Links present under Header Section",
							"All Links should be display in the " + strPageName + " Page under Header Section- "
									+ strHeaderName,
							"All Links is display in the " + strPageName + " Page under Header Section- "
									+ strHeaderName,
							true);
				}
				pagescroll(cardComponent, strPageName);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
				String hrefLink = cardComponent.getAttribute("href");
				dataGtmValue = getEventDescriptionIfPresent(cardComponent);
				testRedirection(hrefLink);
				report.updateTestLog("Verify Link under Present Header Element Exists",
						dataGtmValue + " link should be display in the " + strPageName
								+ " present Under Header Section ",
						dataGtmValue + " link is displayed in the " + strPageName + " present Under Header Section ",
						Status.DONE);

			} else {
				report.updateTestLog("Verify Link under Present Header Element Exists",
						dataGtmValue + " link should be display in the " + strPageName
								+ " present Under Header Section " + strHeaderName,
						dataGtmValue + " link is not displayed in the " + strPageName + " present Under Header Section "
								+ strHeaderName,
						Status.FAIL);

			}
		}

	}

// Method to extract eventDescription from the data-gtm attribute
	/**
	 * public static String getEventDescriptionIfPresent(WebElement cardComponent)
	 *
	 * @return
	 */
	public static String getEventDescriptionIfPresent(WebElement cardComponent) {

// Check if the 'data-gtm' attribute is present
		if (cardComponent != null && cardComponent.getAttribute("data-gtm") != null) {
// Get the value of the data-gtm attribute
			String dataGtmValue = cardComponent.getAttribute("data-gtm");
			if (dataGtmValue.equalsIgnoreCase("") | dataGtmValue.isEmpty()) {
				String strEventName = cardComponent.getText();
				return strEventName;
			} else {
// Extract the eventDescription from the data-gtm JSON string
				return extractEventDescription(dataGtmValue);
			}

		}
		return null;
	}

// Helper method to extract eventDescription from the data-gtm attribute
	/**
	 * private static String extractEventDescription(String dataGtmValue)
	 *
	 * @return
	 */
	private static String extractEventDescription(String dataGtmValue) {
// Define the prefix for eventDescription key
		String prefix = "\"eventDescription\":\"";

// Find the position of the eventDescription key and extract the value
		int startIndex = dataGtmValue.indexOf(prefix);
		if (startIndex == -1) {
			return null; // eventDescription is not present
		}

		startIndex += prefix.length(); // move to the start of the value

// Find the end of the value (next quote)
		int endIndex = dataGtmValue.indexOf("\"", startIndex);

// Extract and return the value between the quotes
		return dataGtmValue.substring(startIndex, endIndex);
	}

	/**
	 * Function to test Redirection links in particular webpage
	 *
	 * @param timeOutInSeconds The number of seconds to wait while checking for the
	 *                         alert
	 * @param strWindowName    Name of the window/tab to be switched
	 */
	/**
	 * public void testRedirection(String urlToTest)
	 *
	 * @return
	 */
	public void testRedirection(String urlToTest) {
		try {
// Create a URL object from the URL string
			URL url = new URL(urlToTest);

// Open a connection to the URL
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("GET");
			connection.setInstanceFollowRedirects(false); // Disable automatic redirection handling
// Set additional headers if needed (e.g., User-Agent, Authorization, etc.)
			connection.setRequestProperty("User-Agent", "Mozilla/5.0");
// Get the response code to check for redirection
			int responseCode = connection.getResponseCode();
// String expectedUrl = driver.getCurrentUrl();
// Check if the response code is a 3xx code (Redirection)
			if (responseCode >= 300 && responseCode < 400) {
// String redirectedUrl = connection.getHeaderField("Location");

				report.updateTestLog("Verify Links in WebPage",
						" URL:<a href = " + urlToTest + ">" + urlToTest + "</a>" + "Links Should be 3XX",
						"Response Code is: " + responseCode, Status.DONE);

			}
// Check if the response code is a 2xx code (Success)
			else if (responseCode >= 200 && responseCode < 300) {
				report.updateTestLog("Verify Links in WebPage",
						" URL:<a href = " + urlToTest + " >" + urlToTest + " </a>" + " Link Should be 2XX",
						"Actual Response Code is: " + responseCode, Status.DONE);
			}
// Check if the response code is a 4xx code (Client Error)
			else if (responseCode >= 400 && responseCode < 500) {
				if (responseCode == 406) {
					report.updateTestLog("Verify Links in WebPage",
							" URL:<a href = " + urlToTest + ">" + urlToTest + "</a>" + " Link is 4XX",
							"Client error occurred. Response Code: " + responseCode, Status.DONE);
				} else {

					report.updateTestLog("Verify Links in WebPage",
							" URL:<a href = " + urlToTest + ">" + urlToTest + "</a>" + " Link is 4XX",
							"Client error occurred. Response Code: " + responseCode, Status.DEBUG);
				}
				System.out.println("Client error occurred. Response Code: " + responseCode);

			}
// Check if the response code is a 5xx code (Server Error)
			else if (responseCode >= 500 && responseCode < 600) {
				System.out.println("Server error occurred. Response Code: " + responseCode);
				driver.navigate().refresh();

				report.updateTestLog("Verify Links in WebPage",
						" URL:<a href = " + urlToTest + ">" + urlToTest + "</a>" + " Link is 5XX",
						"Server error occurred. Response Code: " + responseCode, Status.DEBUG);
			}
// Handle other response codes (unlikely, but included for completeness)
			else {
				driver.navigate().refresh();
				System.out.println("Unexpected response code. Response Code: " + responseCode);
				report.updateTestLog("Verify Links in WebPage", "UnExpected Reponse Code is Present",
						" Response Code is: " + responseCode, Status.DEBUG);
			}

		} catch (IOException e) {
			System.out.println("An error occurred during the redirection test: " + e.getMessage());
		}
	}

// Added below method as part of clms
	/**
	 * Function to new window by closing existing window/tab.
	 *
	 * @param timeOutInSeconds The number of seconds to wait while checking for the
	 *                         alert
	 * @param strWindowName    Name of the window/tab to be switched
	 */

	/**
	 * public void switchToNewWindowANDCloseCurrentWindow(long
	 * lngPagetimeOutInSeconds, String strWindowHandle,
	 *
	 * @return
	 */
	public void switchToNewWindowANDCloseCurrentWindow(long lngPagetimeOutInSeconds, String strWindowHandle,
			String strWindowName) {
		if (driverUtil.waitUntilWindowCountAvailable(2, strWindowName, lngPagetimeOutInSeconds)) {
			Set<String> handles = driver.getWindowHandles();
			for (String windowHandle : handles) {
				if (!windowHandle.equals(strWindowHandle)) {
					driver.close();
					driver.switchTo().window(windowHandle);
					driver.manage().window().maximize();
				}
			}
		}
	}

	/**
	 * Method to check the File Exists
	 *
	 * @param strPageName, Page Name in which the control is available
	 * @return No return value
	 */

	/**
	 * public static boolean FileExists(String strFilePath) throws
	 * InterruptedException
	 *
	 * @return
	 */
	public static boolean FileExists(String strFilePath) throws InterruptedException {
		File file = new File(strFilePath);
		boolean blnFound = false;
		int counter = 0;
		if (!file.exists()) {
			while (counter <= 10) {

				if (file.exists()) {
					blnFound = true;
					break;
				}
				counter++;
			}
		} else {
			blnFound = true;
		}
		return blnFound;
	}

	/**
	 * Method to verify multiple buttons are available in a page
	 *
	 * @param strButtons,  name of the buttons should be provided with "!" separator
	 * @param strPageName, Page Name in which the control is available
	 * @return No return value
	 */
	/**
	 * public void verifyMultipleButtonExist(String strKeyword, String strButtons,
	 * String strPageName)
	 *
	 * @return
	 */
	public void verifyMultipleButtonExist(String strKeyword, String strButtons, String strPageName) {
		setKeywordCount(strKeyword);
		String[] strInputParameter = strButtons.split("!");
		driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds, strPageName);
		boolean flag = true;
		List<String> buttonExist = new ArrayList<>();
		List<String> buttonNotExist = new ArrayList<>();
		for (String strInput : strInputParameter) {
			By objButton = new Common(strInput).button;
			if (objectExists(objButton, "isDisplayed", timeOutInSeconds, strInput, "Button", strPageName, false)) {
				pagescroll(objButton, strPageName);
				buttonExist.add(strInput);

			} else {
				buttonNotExist.add(strInput);
				flag = false;

			}
		}
		if (flag) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Button Exist ",
					String.join(", ", strInputParameter) + ":" + " Button should be displayed as expected in "
							+ strPageName + " page",
					String.join(", ", buttonExist) + " Button is displayed in " + strPageName + " page", true);

		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Button Exist ",
					String.join(", ", strInputParameter) + ":" + " Button should be displayed as expected in "
							+ strPageName + " page",
					String.join(", ", buttonNotExist) + " Button is not displayed in " + strPageName + " page", true);
		}
	}

	/**
	 *
	 * Method to select a radio button
	 *
	 *
	 *
	 * @param strFieldName, The name of the field
	 *
	 * @param strValue,     value to be selected in the field
	 *
	 * @param strPageName,  Page Name in which the control is available
	 *
	 * @return No return value
	 *
	 */

	/**
	 * public void selectRadioButton(String strValueToSelect, String strPageName)
	 *
	 * @return
	 */
	public void selectRadioButton(String strValueToSelect, String strPageName) {

		By locator = new Common(strValueToSelect).radiobutton;
		if (objectExists(locator, "isDisplayed", timeOutInSeconds, strValueToSelect, "Radio Button", strPageName,
				false)) {

			driverUtil.waitUntilStalenessOfElement(locator, 2, strPageName);
			pagescroll(locator, strPageName);
			click(locator, lngPagetimeOutInSeconds, strValueToSelect, "Radio Button", strPageName, true);
// driverUtil.waitUntilElementInVisible(Common.loadingSpinner, "Loading
// spinner", "Spinner", strPageName);
		} else {
			ALMFunctions.ThrowException("Verify Radio Button is selected",
					strValueToSelect + "Radio Button should be selected",
					strValueToSelect + " Radio Button is not available to selected", true);
		}

	}

	/**
	 * Method to return the date in particular format and time zone
	 *
	 * @param strTimeZone, which timezone date to be returned
	 * @return TimeZone
	 */
	/**
	 * public String getTime(String strTimeZone)
	 *
	 * @return
	 */
	public String getTime(String strTimeZone) {

		Date currentDate = new Date();
		SimpleDateFormat dateFormat = new SimpleDateFormat("MMMM d, yyyy");
		TimeZone timeZone = TimeZone.getTimeZone(strTimeZone);
		dateFormat.setTimeZone(timeZone);
		String strCurrentDateAndTime = dateFormat.format(currentDate);
		return strCurrentDateAndTime;
	}

	/**
	 * Method to return the date in particular format and time zone
	 *
	 * @param strTimeZone, which timezone date to be returned
	 * @return TimeZone
	 */
	/**
	 * public static String getFormattedDate(String timeZone, String dateFormat,
	 * String strCondition)
	 *
	 * @return
	 */
	public static String getFormattedDate(String timeZone, String dateFormat, String strCondition) {
// Get the current date and time in the specified time zone
		ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(timeZone));
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
		String strDate = null;
		if (strCondition.contains("+")) {
			strCondition = strCondition.replace("+", "");
			ZonedDateTime futureDate = zonedDateTime.plusDays(Integer.valueOf(strCondition));
			strDate = futureDate.format(formatter);
		} else if (strCondition.contains("-")) {
			strCondition = strCondition.replace("+", "");
			ZonedDateTime futureDate = zonedDateTime.minusDays(Integer.valueOf(strCondition));
			strDate = futureDate.format(formatter);
		} else {
			strDate = zonedDateTime.format(formatter);
		}

		return strDate;

	}

	/**
	 * Method to switch to different frames in application
	 *
	 * @param locator,       Locator of the Frame
	 * @param strFrameValue, input frame values to switch different frames
	 * @param strPageName,   Page Name in which the control is available
	 * @return No return value
	 */

	/**
	 * public void frameSwitchAndSwitchBack(By locator, String StrFrameValue, String
	 * strPageName)
	 *
	 * @return
	 */
	public void frameSwitchAndSwitchBack(By locator, String StrFrameValue, String strPageName) {
		switch (StrFrameValue.toLowerCase()) {
		case "documentframe":
			try {
				driver.switchTo().defaultContent();

				if (objectExists(locator, "isEnabled", lngMinTimeOutInSeconds, StrFrameValue, "Frame", strPageName,
						false)) {
					driverUtil.waitUntilStalenessOfElement(locator, staleTimeOut, strPageName);
// driverUtil.waitUntilStalenessOfElement(locator, strPageName);
					WebElement elmFrame = driver.findElement(locator);
					driver.switchTo().frame(elmFrame);
// report.updateTestLog("Switch to Frame", "Should be switched to Document
// Frame", "Switched to Document Frame", Status.DONE);

				}

			} catch (Exception e) {
				ALMFunctions.ThrowException("Error - Frame", "Iframe should be available in the " + strPageName,
						"Iframe is not available in the " + strPageName, true);
			}
			break;

		case "default":
			driver.switchTo().defaultContent();
// report.updateTestLog("Switch to Frame", "Should be switched to Default
// Frame", "Switched to Default Frame", Status.DONE);
			break;

		default:
			ALMFunctions.ThrowException("Test Data",
					"Only Pre-Defined Fields Type must be provided in the test data sheet",
					"Error - Unhandled Field Type " + StrFrameValue, false);
			break;
		}
	}

	/**
	 * public void selectHCP()
	 *
	 * @return
	 */
	public void selectHCP() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strClickBtn = dataTable.getData("FunctionalRegression", "ClickLocators");
		String strMemberBtn = dataTable.getData("FunctionalRegression", "PreCondition");
		WebElement eleMemberBtn = driver.findElement(By.xpath(strMemberBtn));
		WebElement eleLink = driver.findElement(By.xpath(strClickBtn));
		String strPageName = driver.getTitle();

		if (objectExists(eleLink, "isDisplayed", lngMinTimeOutInSeconds, strPageName, "Element Existence", strPageName,
				false)) {
			click(eleLink, lngMinTimeOutInSeconds, "Member Button", strMemberBtn, strPageName, true);
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Link in webPage",
					eleLink.getText() + " should be display in the " + strPageName,
					eleLink.getText() + " is not displayed in the " + strPageName, true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Link in webPage",
					eleLink.getText() + " should be display in the " + strPageName,
					eleLink.getText() + " is not displayed in the " + strPageName, true);
		}
		if (objectExists(eleMemberBtn, "isDisplayed", lngMinTimeOutInSeconds, strPageName, "Element Existence",
				strPageName, false)) {
			click(eleMemberBtn, lngMinTimeOutInSeconds, "Member Button", eleMemberBtn.getText(), strPageName, true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Link in webPage",
					eleMemberBtn.getText() + " should be display in the " + strPageName,
					eleMemberBtn.getText() + " is not displayed in the " + strPageName, true);
		}
	}

	/**
	 * public void checkPdfFileDownload()
	 *
	 * @return
	 */
	public void checkPdfFileDownload() {
		// By locator should be used in all methods instead of webelement
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPdfFileLocator = dataTable.getData("FunctionalRegression", "PdfFileStaticLocator");

		//By Locator should used 
		WebElement pdfFile = driver.findElement(By.xpath(strPdfFileLocator));
		pdfFileCheck(pdfFile);
	}

	/**
	 * public void pdfFileCheck(WebElement elePdfLoc)
	 *
	 * @return
	 */
	public void pdfFileCheck(WebElement elePdfLoc) {
		String strAttachmentName = getPdfFilename(elePdfLoc);
		driverUtil.waitUntilStalenessOfElement(elePdfLoc, driver.getTitle());
		pagescroll(elePdfLoc, strAttachmentName);
		if (objectExists(elePdfLoc, "isDisplayed", lngMinTimeOutInSeconds, "pdf_file", "PDF", driver.getTitle(),
				false)) {
			clickByJS(elePdfLoc, lngMinTimeOutInSeconds, "pdf File", "Multiple File Download", driver.getTitle(), true);

			try {
				Thread.sleep(3000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			// use Global setting properties
			String downloadPath = System.getProperty("user.dir") + "\\externalFiles";
			File file = new File(downloadPath);
			try {
				checkFileDownloaded(file, downloadPath, strAttachmentName);
			} catch (Exception e) {
				e.printStackTrace();
			}
			deleteAllFilesInFolder(downloadPath);

		}
	}

	/**
	 * public String getPdfFilename(WebElement linkElement)
	 *
	 * @return
	 */
	public String getPdfFilename(WebElement linkElement) {
// Extract the href attribute
		String href = linkElement.getAttribute("href");

// Extract the filename from the href
		return href.substring(href.lastIndexOf('/') + 1);
	}

	/**
	 * public void deleteAllFilesInFolder(String folderPath)
	 *
	 * @return
	 */
	public void deleteAllFilesInFolder(String folderPath) {
// Create a File object for the folder
		File folder = new File(folderPath);

// Get all files in the folder
		File[] files = folder.listFiles();

		if (files != null) {
			for (File file : files) {
// Delete each file
				if (file.isFile()) {
					boolean deleted = file.delete();
					if (deleted) {
						System.out.println("Deleted file: " + file.getName());
					} else {
						System.out.println("Failed to delete file: " + file.getName());
					}
				}
			}
		} else {
			System.out.println("The specified folder does not exist or is not a directory.");
		}
	}

	/**
	 * public void checkMultiplePdfFileDownload()
	 *
	 * @return
	 */
	public void checkMultiplePdfFileDownload() {
		String strPdfFileLocator = dataTable.getData("FunctionalRegression", "PdfFileStaticLocator");
		List<WebElement> pdfFiles = driver.findElements(By.xpath(strPdfFileLocator));
		for (WebElement pdfFile : pdfFiles) {
			pdfFileCheck(pdfFile);
		}
	}

	/**
	 * public void checkRegisterLinks()
	 *
	 * @return
	 */

	/**
	 * Iterates through all register links on the page, handles overlay if present,
	 * clicks each link, validates the resulting page, and navigates back if needed.
	 */
	public void checkRegisterLinks() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strEleToLink = dataTable.getData("FunctionalRegression", "LinkStaticLocator");
		String strOverlayLocator = dataTable.getData("FunctionalRegression", "RemoveOverlay");

		List<WebElement> eleRegisterLinks = driver.findElements(By.xpath(strEleToLink));
		int totalLinks = eleRegisterLinks.size();

		for (int i = 0; i < totalLinks; i++) {
// Re-locate the element to avoid stale reference
			List<WebElement> currentLinks = driver.findElements(By.xpath(strEleToLink));
			if (i >= currentLinks.size())
				break; // Safety check
			WebElement eleRegister = currentLinks.get(i);

// Handle overlay if present
			if (!strOverlayLocator.isEmpty()) {
				try {
					WebElement eleOverlay = driver.findElement(By.cssSelector(strOverlayLocator));
					if (objectExists(eleOverlay, "isDisplayed", lngMinTimeOutInSeconds, strOverlayLocator,
							"Element Existence", driver.getTitle(), false)) {
						JavascriptExecutor jsExecutor = (JavascriptExecutor) driver.getWebDriver();
						jsExecutor.executeScript(
								"document.querySelector('" + strOverlayLocator + "').style.display='none';");
					}
				} catch (Exception e) {
// Log or ignore if overlay not found
				}
			}

// Click the register link
			clickByJS(eleRegister, lngMinTimeOutInSeconds, eleRegister.getText(), "Register Link", driver.getTitle(),
					true);

// Validate the resulting page
			validateComponentExists();
			String strExpectedTitle = dataTable.getData("FunctionalRegression", "HomePageTitle");
			String strActualTitle = driver.getTitle();
			if (!strExpectedTitle.equalsIgnoreCase(strActualTitle)) {
				driver.navigate().back();
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}
		}
	}
	/**
	 * public void checkRegisterLinks()
	 *
	 * @return
	 */

	/**
	 * Iterates through all register links on the page, handles overlay if present,
	 * clicks each link, validates the resulting page, and navigates back if needed.
	 */
	public void checkRegisterLinksLoc() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strEleToLink = dataTable.getData("FunctionalRegression", "LinkStaticLocator");
		String strOverlayLocator = dataTable.getData("FunctionalRegression", "RemoveOverlay");

		List<WebElement> eleRegisterLinks = driver.findElements(new Common(strEleToLink).registerLinks);
		int totalLinks = eleRegisterLinks.size();

		for (int i = 0; i < totalLinks; i++) {
// Re-locate the element to avoid stale reference
			List<WebElement> currentLinks = driver.findElements(new Common(strEleToLink).registerLinks);
			if (i >= currentLinks.size())
				break; // Safety check
			WebElement eleRegister = currentLinks.get(i);

// Handle overlay if present
			if (!strOverlayLocator.isEmpty()) {
				try {
					WebElement eleOverlay = driver.findElement(By.cssSelector(strOverlayLocator));
					if (objectExists(eleOverlay, "isDisplayed", lngMinTimeOutInSeconds, strOverlayLocator,
							"Element Existence", driver.getTitle(), false)) {
						JavascriptExecutor jsExecutor = (JavascriptExecutor) driver.getWebDriver();
						jsExecutor.executeScript(
								"document.querySelector('" + strOverlayLocator + "').style.display='none';");
					}
				} catch (Exception e) {
// Log or ignore if overlay not found
				}
			}

// Click the register link
			clickByJS(eleRegister, lngMinTimeOutInSeconds, eleRegister.getText(), "Register Link", driver.getTitle(),
					true);

// Validate the resulting page
			validateComponentExists();
			String strExpectedTitle = dataTable.getData("FunctionalRegression", "HomePageTitle");
			String strActualTitle = driver.getTitle();
			if (!strExpectedTitle.equalsIgnoreCase(strActualTitle)) {
				driver.navigate().back();
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
			}
		}
	}
	/**
	 * public void clickSubMenu()
	 *
	 * @return
	 */
	public void clickSubMenu() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String[] getMenuLocators = dataTable.getData("FunctionalRegression", "SubMenuLocators").split(";");
		By menu = By.xpath(getMenuLocators[0]);
		By subMenu = By.xpath(getMenuLocators[1]);
		String strPageName = driver.getTitle();
		
		if (objectExists(menu, "isDisplayed", lngMinTimeOutInSeconds, "Menu", "Element Existence", driver.getTitle(),
				false)) {
			
			if (dataTable.getData("FunctionalRegression", "TC_ID").contains("Vemlidy")
					|| dataTable.getData("FunctionalRegression", "TC_ID").contains("Vekluryhcp")) {
				mouseOver(menu, lngMinTimeOutInSeconds, "Menu", "Header Section", strPageName);
			} else {
				mouseOverandClick(menu, lngMinTimeOutInSeconds, "Menu", "Header Section", strPageName, true);
			}

			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Menu Element Exists",
					" Menu should be display in the " + strPageName,
					 "Menu is displayed in the " + strPageName, true);
			
			if (objectExists(subMenu, "isDisplayed", lngMinTimeOutInSeconds, "Menu", "Element Existence",
					driver.getTitle(), false)) {
				driverUtil.waitUntilStalenessOfElement(subMenu, strPageName);
				click(subMenu, lngMinTimeOutInSeconds, "subMenu", "SubMenu", strPageName, true);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
				//remove Thread.sleep
				try {
					Thread.sleep(3000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Sub Menu Element Exists",
						 "Sub Menu Content should be display in the " + strPageName,
						 "Sub Menu Content is displayed in the " + strPageName, true);

			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Menu Element Exists",
						"Sub Menu Content should be display in the " + strPageName,
						 "Sub Menu Content is not displayed in the " + strPageName, true);
			}
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Menu Element Exists",
					 " Menu should be display in the " + strPageName,
					 "Menu is not displayed in the " + strPageName, true);
		}
	}

	/**
	 * public void clickSubMenu()
	 *
	 * @return
	 */
	public void clickSubMenuLoc() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String[] getMenuLocators = dataTable.getData("FunctionalRegression", "SubMenuLocators").split(";");
		String menu = getMenuLocators[0];
		String subMenu = getMenuLocators[1];
		String strPageName = driver.getTitle();
		
		if (objectExists(new Common(menu).menu, "isDisplayed", lngMinTimeOutInSeconds, "Menu", "Element Existence", driver.getTitle(),
				false)) {
			
			if (dataTable.getData("FunctionalRegression", "TC_ID").contains("Vemlidy")
					|| dataTable.getData("FunctionalRegression", "TC_ID").contains("Vekluryhcp")) {
				mouseOver(new Common(menu).menu, lngMinTimeOutInSeconds, "Menu", "Header Section", strPageName);
			} else {
				mouseOverandClick(new Common(menu).menu, lngMinTimeOutInSeconds, "Menu", "Header Section", strPageName, true);
			}

			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Menu Element Exists",
					" Menu should be display in the " + strPageName,
					 "Menu is displayed in the " + strPageName, true);
			
			if (objectExists(new Common(subMenu).subMenu, "isDisplayed", lngMinTimeOutInSeconds, "Menu", "Element Existence",
					driver.getTitle(), false)) {
				driverUtil.waitUntilStalenessOfElement(new Common(subMenu).subMenu, strPageName);
				click(new Common(subMenu).subMenu, lngMinTimeOutInSeconds, "subMenu", "SubMenu", strPageName, true);
				driverUtil.waitUntilPageReadyStateComplete(lngMinTimeOutInSeconds);
				//remove Thread.sleep
				try {
					Thread.sleep(3000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Sub Menu Element Exists",
						 "Sub Menu Content should be display in the " + strPageName,
						 "Sub Menu Content is displayed in the " + strPageName, true);
				
				
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Menu Element Exists",
						"Sub Menu Content should be display in the " + strPageName,
						 "Sub Menu Content is not displayed in the " + strPageName, true);
			}
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Menu Element Exists",
					 " Menu should be display in the " + strPageName,
					 "Menu is not displayed in the " + strPageName, true);
		}
	}
	/**
	 * public void validateComponentExists()
	 *
	 * @return
	 */
	public void validateComponentExists() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String[] strArrCompontentToChecks = dataTable.getData("FunctionalRegression", "VerifyComponentPresent")
				.split(";");
// Lists to track checked elements and failed elements
		List<String> lstElementsChecked = lstCheckPass.get();
		List<String> lstElementsFailed = lstCheckFail.get();
		String strElementName = null;
		if (!dataTable.getData("FunctionalRegression", "VerifyComponentPresent").isEmpty()) {
			for (String strCompontentToCheck : strArrCompontentToChecks) {

				try {
					By componetentExists = new Common_SmokeTestcases(strCompontentToCheck).componetentExists;
					pagescroll(componetentExists, driver.getTitle());
					WebElement lastCheckedElement = driver.findElement(componetentExists);
					strElementName =getText(componetentExists, lngMinTimeOutInSeconds, "Component Exixts", driver.getTitle());
					if (strElementName.isEmpty() || lastCheckedElement.getTagName().contains("select")) {
						strElementName = lastCheckedElement.getAttribute("data-sc-field-name");
					} // Get the visible name of the element
					if (objectExists(componetentExists, "isDisplayed", lngMinTimeOutInSeconds, strElementName,
							"Element Existence", driver.getCurrentUrl(), false)) {
						lstElementsChecked.add(strElementName); // Add to the list of successfully checked elements
					}
				} catch (NoSuchElementException e) {
					lstElementsFailed.add(strElementName); // Add the component to the failed list if not found
				}
			}
// Compare expected and actual elements found on the webpage and log results
			if (lstElementsChecked.size() == strArrCompontentToChecks.length) {
				report.updateTestLog("Verify Given Component in Data Sheet Exists",
						"Given elements should be visible on the webpage: " + lstElementsChecked,
						"Given elements are visible on the webpage: " + lstElementsChecked, Status.PASS);
			} else {
				report.updateTestLog("Verify Given Component in Data Sheet Exists",
						"Given elements should be visible on the webpage: " + lstElementsFailed,
						"Given elements are not visible on the webpage: " + lstElementsFailed, Status.FAIL);
			}
			lstCheckPass.remove();
			lstCheckFail.remove();

		}
	}

	/**
	 * public void verifyConfirmationPopup()
	 *
	 * @return
	 */
	public void verifyConfirmationPopup() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName = driver.getTitle();
		String[] testData = dataTable.getData("FunctionalRegression", "ConfirmationPopupLocators").split(";");
		String strConfirmationBtn = testData[0];
		String strAccpetBtn = testData[1];
		String strCancelBtn = testData[2];
		click(By.xpath(strConfirmationBtn), lngMinTimeOutInSeconds, strCancelBtn, "Icon", strPageName, true);

		verifyDeclineButton(strCancelBtn);
		click(By.xpath(strConfirmationBtn), lngMinTimeOutInSeconds, "Link to center site", "Link", strPageName, true);
		verifyAcceptButton(strAccpetBtn);
	}
	/**
	 * public void verifyConfirmationPopup()
	 *
	 * @return
	 */
	public void verifyConfirmationPopupLoc() {
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());
		String strPageName = driver.getTitle();
		String[] testData = dataTable.getData("FunctionalRegression", "ConfirmationPopupLocators").split(";");
		String strConfirmationBtn = testData[0];
		String strAccpetBtn = testData[1];
		String strCancelBtn = testData[2];
		click(new Common_SmokeTestcases(strConfirmationBtn).confirmationPopup, lngMinTimeOutInSeconds, strConfirmationBtn, "Site Leaving Link", strPageName, true);

		verifyDeclineButton(strCancelBtn);
		click(new Common_SmokeTestcases(strConfirmationBtn).confirmationPopup, lngMinTimeOutInSeconds, strConfirmationBtn, "Site Leaving Link", strPageName, true);
		verifyAcceptButton(strAccpetBtn);
	}
	/**
	 * Method to navigate through menu and verify the page has loaded by comparing
	 * expected and actual titles.
	 *
	 * @param sheetName       The name of the Excel sheet containing the data.
	 * @param firstMenuColumn The column name representing the primary menu in
	 *                        Excel.
	 * @param titleColumn     The column name representing the expected page title
	 *                        in Excel.
	 * @param blnSecondMenu   if false, the code checks for sub-menus under main
	 *                        menu
	 */
	/**
	 * public void navigateAndVerifyMenu(String sheetName, String firstMenuColumn,
	 * String titleColumn,
	 *
	 * @return
	 */
	public void navigateAndVerifyMenu(String sheetName, String firstMenuColumn, String titleColumn,
			boolean blnSecondMenu) {
// Retrieve the title data for the corresponding test case from the Excel sheet
		String expectedTitles = dataTable.getData(sheetName, titleColumn);

// Validate the retrieved title data
		if (expectedTitles == null || expectedTitles.isEmpty()) {
			throw new IllegalArgumentException("No title data found for the corresponding test case row.");
		}

// Split the expected title values using the delimiter "~"
		String[] arrExpectedTitle = expectedTitles.split("~");

// Retrieve the submenu data for the corresponding test case from the Excel
// sheet, if required
		String strSecondMenus = null;
		String[] arrSecondMenu = null;
		if (blnSecondMenu) {
			strSecondMenus = dataTable.getData(sheetName, firstMenuColumn);

// Validate the retrieved submenu data
			if (strSecondMenus == null || strSecondMenus.isEmpty()) {
				throw new IllegalArgumentException("No submenu data found for the corresponding test case row.");
			}

// Split the submenu values using the delimiter "~"
			arrSecondMenu = strSecondMenus.split("~");

// Validate if the number of submenus matches the number of expected titles
			if (arrSecondMenu.length != arrExpectedTitle.length) {
				throw new IllegalArgumentException("Mismatch between the number of submenus and expected titles.");
			}
		}

// Iterate through each expected title
		for (int i = 0; i < arrExpectedTitle.length; i++) {
			String strPageUrl = driver.getCurrentUrl();
			String secondMenu = (arrSecondMenu != null && arrSecondMenu.length > i) ? arrSecondMenu[i].trim() : null;
			String strExpectedTitle = arrExpectedTitle[i].trim();

			driverUtil.waitUntilAjaxLoadingComplete(lngMinTimeOutInSeconds, "");

			try {
// Attempt to click on the first navigation menu
				clickByJS(new Common(firstMenuColumn).navigationMenu, lngMinTimeOutInSeconds, firstMenuColumn,
						"Navigation Menu", strPageUrl, true);
			} catch (Exception e) {
// Log the exception and proceed without halting execution
				System.err.println("Failed to click on the first menu: " + e.getMessage());
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Failed to click on the first menu: " + firstMenuColumn,
						"Error Message: " + e.getMessage(), "Continuing execution despite the error.", false);
				continue; // Skip to the next iteration
			}

// Skip the second menu step if the firstMenuColumn is "Support & Resources" or
// "Caregiver Support"
			if (secondMenu != null) {
				switch (secondMenu) {
				case "Caregiver role":
					clickByJS(new Common(secondMenu, "1").caregiverRoleNavigationMenu, lngMinTimeOutInSeconds,
							"Caregiver role and support", "Navigation Menu", strPageUrl, true);
					break;
				case "Caregiver role and support":
				case "Managing side effects":
					clickByJS(new Common(secondMenu, "2").caregiverRoleNavigationMenu, lngMinTimeOutInSeconds,
							secondMenu, "Navigation Menu", strPageUrl, true);
					break;
				default:
					if (objectExists(new Common("navbar-nav", secondMenu).genericTextUsingAncestorID, "isDisplayed",
							lngMinTimeOutInSeconds, secondMenu, "Menu", strPageUrl, false)) {
						clickByJS(new Common("navbar-nav", secondMenu).genericTextUsingAncestorID,
								lngMinTimeOutInSeconds, secondMenu, "Navigation Menu", strPageUrl, true);
						break;
					} else if (!objectExists(new Common("level-2", secondMenu).genericTextUsingAncestorClass,
							"isDisplayed", lngMinTimeOutInSeconds, secondMenu, "Menu", strPageUrl, false)) {
						List<WebElement> subHeadersList = getListWebElements(
								new Common("menu-level-2-link").genericElementWithClass);
						for (int j = 0; j < 3; j++) {
							driverUtil.waitUntilAjaxLoadingComplete(staleTimeOut, driver.getCurrentUrl());
							clickByJS(subHeadersList.get(j), lngMinTimeOutInSeconds, subHeadersList.get(j).getText(),
									"Navigation Menu", strPageUrl, true);
							if (objectExists(new Common("level-2", secondMenu).genericTextUsingAncestorClass,
									"isDisplayed", lngMinTimeOutInSeconds, secondMenu, "Menu", strPageUrl, false)) {
								clickByJS(new Common("level-2", secondMenu).genericTextUsingAncestorClass,
										lngMinTimeOutInSeconds, secondMenu, "Navigation Menu", strPageUrl, true);
								break;

							}
						}
					} else {
						clickByJS(new Common("level-2", secondMenu).genericTextUsingAncestorClass,
								lngMinTimeOutInSeconds, secondMenu, "Navigation Menu", strPageUrl, true);
					}
				}
			}

			refreshPageIfNotLoaded(CtrlTimeOut);

// Retrieve the current page title
			String strActualTitle = driver.getTitle();

// Verify the page title
			if (strActualTitle.equals(strExpectedTitle)) {
				ALMFunctions.UpdateReportLogAndALMForPassStatus(
						"'" + strPageUrl
								+ "' navigation menu must be clicked and the expected page title must be verified",
						"Expected title: " + strExpectedTitle,
						"Actual title matches the expected title: " + strActualTitle, true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus(
						"'" + strPageUrl
								+ "' navigation menu must be clicked and the expected page title must be verified",
						"Expected title: " + strExpectedTitle,
						"Actual title does not match the expected title. Actual title: " + strActualTitle, true);
			}
		}
	}

	/**
	 * Waits for the page to fully load within the specified timeout duration. If
	 * the page is not fully loaded within the given time, the method refreshes the
	 * current page.
	 *
	 * @param timeOutInSeconds The maximum time to wait for the page to load, in
	 *                         seconds.
	 */
	/**
	 * public void refreshPageIfNotLoaded(long timeOutInSeconds)
	 *
	 * @return
	 */
	public void refreshPageIfNotLoaded(long timeOutInSeconds) {
		int intRefreshCount = 0;
		long lngStartTime;

		while (intRefreshCount < 2) {
			lngStartTime = System.currentTimeMillis();
			try {
// Polling to check if a specific element is present
				while ((System.currentTimeMillis() - lngStartTime) < timeOutInSeconds * 1000) {
					if (driverUtil.waitUntilAjaxLoadingComplete(staleTimeOut, driver.getCurrentUrl())) {
						return; // Page loaded successfully
					}
					waitFor(500); // Wait for 500ms before re-checking
				}

// If timeout expires and page is still not loaded, refresh the page
				driver.navigate().refresh();
				intRefreshCount++; // Increment refresh count
			} catch (Exception e) {
				e.printStackTrace(); // Log the exception
				break; // Break the loop in case of error
			}
		}
	}

	/**
	 * Verifies the existence of the "Approved Uses" and "Important Safety
	 * Information" tray on the specified page and logs the results.
	 */
	/**
	 * public void verifyReadButton()
	 *
	 * @return
	 */
	public void verifyReadButton() {
		String strPageName = driver.getCurrentUrl();
		click(new Common("read-btn", "Read").genericTextUsingAncestorClass, lngMinTimeOutInSeconds, "ISI Tray read",
				"Button", strPageName, true);
		if (objectExists(new Common("Approved").simpleLinkedText, "isDisplayed", loadingWindowTimeOut, "Approved Uses",
				"Text", strPageName, false)
				&& objectExists(new Common("ISI-tray", "Safety").genericLinkedTextUsingAncestorID, "isDisplayed",
						loadingWindowTimeOut, "Safety Information", "Text", strPageName, false)) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus(
					"Click on the 'Read' tab from the ISI Tray displayed on the bottom of the screen",
					"Users should view approved uses and safety information on the right-side pane popup.",
					"User is able to view valid results", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus(
					"Click on the 'Read' tab from the ISI Tray displayed on the bottom of the screen",
					"Users should view approved uses and safety information on the right-side pane popup.",
					"User is unable to view valid results", true);
		}
		click(new Common("ISI-header", "Close").genericTextUsingAncestorClass, lngMinTimeOutInSeconds, "ISI Tray Close",
				"Button", strPageName, true);
	}

	/**
	 * Handles video playback within a frame and closes it afterward.
	 *
	 * @param videoIdentifier A string that identifies the video element (e.g., text
	 *                        like "Learn about what was important").
	 * @param videoName       Name of the video (used for logging and
	 *                        identification).
	 * @param pageName        Name of the page where the video is located (used for
	 *                        logging).
	 */
	/**
	 * public void handleVideoPlayAndClose(String videoIdentifier, String videoName,
	 * String pageName)
	 *
	 * @return
	 */
	public void handleVideoPlayAndClose(String videoIdentifier, String videoName, String pageName) {
		try {
			clickByJS(new Common(videoIdentifier).genericPlayVideo, lngMinTimeOutInSeconds, videoName, "Video Button",
					pageName, true);
			driverUtil.waitUntilElementVisible(new Common("videoVimeo").genericIFrame, lngMinTimeOutInSeconds);
			driver.switchTo().frame(getSingleWebElement(new Common("videoVimeo").genericIFrame));
			videoPlay(videoName);
			driver.switchTo().defaultContent();
			click(Common.videoClose_btn, lngMinTimeOutInSeconds, videoName + " Video Close", "Button", pageName, true);
		} catch (Exception e) {
			throw new RuntimeException(String.format("Error handling video playback for '%s' on page '%s': %s",
					videoName, pageName, e.getMessage()), e);
		}
	}

// Class-level lock to synchronize file claim operations among threads.
	private static final Object downloadLock = new Object();

	/**
	 * Handles file download verification by initiating the download, verifying its
	 * existence with a random name, and deleting the file.
	 *
	 * @param locator  The element locator for the download trigger.
	 * @param fileName The expected base file name.
	 * @param fileType The file type/extension (e.g., "pdf").
	 * @param pageName The name of the page for logging context.
	 */
	public void handleFileDownload(By locator, String fileName, String fileType, String pageName) {
		if (objectExists(locator, "isEnabled", loadingWindowTimeOut, fileName, fileType, pageName, true)) {
			try {
				// Define the download folder path
				String strDownloadPath = System.getProperty("user.dir") + "/externalFiles";
				File downloadDir = new File(strDownloadPath);

				// Capture the list of files present before the download
				Set<String> filesBefore = new HashSet<>(Arrays.asList(downloadDir.list()));

				// Initiate the file download via JavaScript click
				clickByJS(locator, lngMinTimeOutInSeconds, fileName, fileType.toUpperCase(), pageName, true);

				// Wait until a new file appears in the folder and claim it uniquely.
				File downloadedFile = waitForAndClaimDownloadedFile(downloadDir, filesBefore, fileType);

				// Verify download success
				if (downloadedFile == null || !downloadedFile.exists()) {
					throw new RuntimeException(
							String.format("Downloaded file '%s' not found in path '%s'", fileName, strDownloadPath));
				}

				// Perform file verification
				checkFileDownloaded(downloadDir, strDownloadPath, downloadedFile.getName());

				// Cleanup: Delete the downloaded file after verification
				if (!downloadedFile.delete()) {
					System.err.println("Warning: Unable to delete the downloaded file: " + downloadedFile.getName());
				}
			} catch (Exception e) {
				throw new RuntimeException(String.format("Error while verifying file download '%s' on page '%s': %s",
						fileName, pageName, e.getMessage()), e);
			}
		}
	}

	/**
	 * Waits for a new file to appear in the downloadDir that was not present in
	 * filesBefore. It then attempts to claim the file so that no other thread can
	 * process the same file.
	 *
	 * @param downloadDir The directory where files are downloaded.
	 * @param filesBefore The snapshot of file names before the download started.
	 * @param fileType    The expected file extension (without dot).
	 * @return The claimed File object when found; otherwise null.
	 * @throws InterruptedException If thread sleep is interrupted.
	 */
	private File waitForAndClaimDownloadedFile(File downloadDir, Set<String> filesBefore, String fileType)
			throws InterruptedException {
		File claimedFile = null;
		int attempts = 0;
		while (attempts < 30 && claimedFile == null) {
			claimedFile = claimDownloadedFile(downloadDir, filesBefore, fileType);
			if (claimedFile != null) {
				break;
			}
			// Wait 1 second before trying again
			Thread.sleep(1000);
			attempts++;
		}
		return claimedFile;
	}

	/**
	 * Checks the downloadDir for any new file (i.e., not in filesBefore) with the
	 * given fileType. If found, uses a synchronized block along with a file rename
	 * to "claim" the file uniquely.
	 *
	 * @param downloadDir The download directory.
	 * @param filesBefore The original set of file names before the download.
	 * @param fileType    The file extension to check (without dot).
	 * @return A File object that has been claimed; or null if none found.
	 */
	private File claimDownloadedFile(File downloadDir, Set<String> filesBefore, String fileType) {
		String[] filesAfter = downloadDir.list();
		if (filesAfter != null) {
			for (String currentFileName : filesAfter) {
				// If the file is new and matches the desired file type (case-insensitive)
				if (!filesBefore.contains(currentFileName)
						&& currentFileName.toLowerCase().endsWith("." + fileType.toLowerCase())) {

					File fileToClaim = new File(downloadDir, currentFileName);
					// Generate a unique name for the claimed file.
					String newFileName = currentFileName + "_claimed_" + Thread.currentThread().getId() + "_"
							+ System.currentTimeMillis();
					File claimedFile = new File(downloadDir, newFileName);
					// Synchronized block ensures that two threads don’t claim the same file
					// concurrently.
					synchronized (downloadLock) {
						// Double-check that the file still exists before claiming it
						if (fileToClaim.exists() && fileToClaim.renameTo(claimedFile)) {
							return claimedFile;
						}
					}
				}
			}
		}
		return null;
	}

	/**
	 * public void verifyHeaderAndImage(String subMenu, String header, String
	 * imageAlternateText)
	 *
	 * @return
	 */
	public void verifyHeaderAndImage(String subMenu, String header, String imageAlternateText) {
		clickByJS(new Common("inpage-nav-list", subMenu).genericTextUsingClass, lngMinTimeOutInSeconds, subMenu,
				"Sub-Menu", driver.getCurrentUrl(), true);
		if (objectExists(new Common(header).simpleH2Header, "isDisplayed", loadingWindowTimeOut, header, "Header",
				driver.getCurrentUrl(), false)
				&& objectExists(new Common(imageAlternateText).imageWithAlternateText, "isDisplayed",
						loadingWindowTimeOut, imageAlternateText, "Image", driver.getCurrentUrl(), false)) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Click on " + subMenu + " submenu and validate the section",
					"User should be able to validate All the Reference, Charts, Graphs, Tables, contents Should be visible in this section",
					"User is able to view valid results", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Click on " + subMenu + " submenu and validate the section",
					"User should be able to validate All the Reference, Charts, Graphs, Tables, contents Should be visible in this section",
					"User is unable to view valid results", true);
		}
	}

	/**
	 * public void navigateToALLPage()
	 *
	 * @return
	 */
	public void navigateToALLPage() {
		if (objectExists(new Common("message", "ALL").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "ALL", "Tab", "Tecartus HCP", false)) {
			pagescroll(new Common("message", "ALL").genericTextUsingAncestorClass, "Tecartus HCP");
			ALMFunctions.UpdateReportLogsDSAndALMForPassStatus("ALL Tab", "ALL Tab must be present",
					"ALL Tab is present", true);
			clickByJS(new Common("message", "ALL").genericTextUsingAncestorClass, lngMinTimeOutInSeconds, "ALL", "Tab",
					"Tecartus HCP", true);
		}
	}

	/**
	 * public void verifyMoreTab()
	 *
	 * @return
	 */
	public void verifyMoreTab() {
		if (objectExists(new Common("isi-view-text", "View more").genericTextUsingAncestorID, "isDisplayed",
				lngMinTimeOutInSeconds, "View more", "Link", "Tecartus HCP", true)) {
			clickByJS(new Common("isi-view-text", "View more").genericTextUsingAncestorID, lngMinTimeOutInSeconds,
					"View more", "Link", "Tecartus HCP", true);

			if (objectExists(new Common("safety-section", "IMPORTANT SAFETY INFORMATION").genericTextUsingAncestorID,
					"isDisplayed", lngMinTimeOutInSeconds, "IMPORTANT SAFETY INFORMATION", "Header", "Tecartus HCP",
					false)) {
				pagescroll(new Common("safety-section", "IMPORTANT SAFETY INFORMATION").genericTextUsingAncestorID,
						"Tecartus HCP");
				ALMFunctions.UpdateReportLogAndALMForPassStatus(
						"Click on the 'More' tab from the ISI Tray which is displayed on the bottom of the screen",
						"Users should be able to view about the Important safety information.",
						"Important safety information is visible", true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus(
						"Click on the 'More' tab from the ISI Tray which is displayed on the bottom of the screen",
						"Users should be able to view about the Important safety information.",
						"Important safety information is visible", true);

			}
		}
	}

//	/**
//	 * public void verifyPrescribingInformationPDFOnTecartusHCP()
//	 *
//	 * @return
//	 */
//	public void verifyPrescribingInformationPDFOnTecartusHCP() {
//		if (objectExists(new Common("utilityNav", "Prescribing Information").genericLinkedTextUsingAncestorID,
//				"isDisplayed", lngMinTimeOutInSeconds, "Prescribing Information", "Link", "Tecartus HCP", false)) {
//			pagescroll(new Common("utilityNav", "Prescribing Information").genericLinkedTextUsingAncestorID,
//					"Tecartus HCP");
//			handleFileDownload(new Common("utilityNav", "Prescribing Information").genericLinkedTextUsingAncestorID,
//					"tecartus-pi", "pdf", "Tecartus HCP");
//		}
//	}

	/**
	 * public void verifyHeadersOnYescartaHCP()
	 *
	 * @return
	 */
	public void verifyHeadersOnYescartaHCP() {
		verifyPrescribingInformationPDFOnYescartaHCP();
		clickAndVerifyLinkOnChildPage(new Common("navbar-nav", "REMS").genericTextUsingAncestorID, "REMS",
				new Common("What Is the YESCARTA and TECARTUS REMS?").simpleH2Header,
				"What Is the YESCARTA and TECARTUS REMS?");
		clickAndVerifyLinkOnChildPage(new Common("navbar-nav", "For Patients").genericTextUsingAncestorID,
				"For Patients", new Common("Why YESCARTA?").simpleH2Header, "Why YESCARTA?");
		clickAndVerifyLinkOnChildPage(new Common("navbar-nav", "Kite Konnect").genericTextUsingAncestorID,
				"Kite Konnect", new Common("Your link to cell therapy support").simpleH2Header,
				"Your link to cell therapy support");
		clickAndVerifyLink(new Common("navbar-nav", "Treatment Centers").genericTextUsingAncestorID,
				"Treatment Centers", "YESCARTA Authorized Treatment Center");

		if (objectExists(new Common("navbar-nav", "Yescartahcp Logo").imageWithAlternateTextUsingAncestorID,
				"isDisplayed", loadingWindowTimeOut, "YescartaHCP", "Logo", "Yescarta HCP", false)) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify header logo", "Header logo should be present",
					"header logo is present", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify header logo", "Header logo should be present",
					"header logo is not present", true);
		}

		clickAndVerifyLink(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, "EFFICACY",
				"YESCARTA efficacy and safety");

		mouseOver(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, loadingWindowTimeOut, "Efficacy",
				"Menu", "Yescarta HCP");
		clickAndVerifyLink(new Common("navbar-nav", "2L R/R lbcl").genericTextUsingAncestorID, "2L R/R lbcl",
				"Choose YESCARTA");
		mouseOver(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, loadingWindowTimeOut, "Efficacy",
				"Menu", "Yescarta HCP");
		clickAndVerifyLink(new Common("navbar-nav", "3L R/R lbcl").genericTextUsingAncestorID, "3L R/R lbcl",
				"YESCARTA: The FIRST CAR T");

		clickAndVerifyLink(new Common("navbar-nav", "SAFETY").genericTextUsingAncestorID, "SAFETY",
				"YESCARTA demonstrated");
		clickAndVerifyLink(new Common("navbar-nav", "REAL-WORLD").genericTextUsingAncestorID, "REAL-WORLD",
				"YESCARTA in the real world");
		clickAndVerifyLink(new Common("navbar-nav", "PATIENT ID").genericTextUsingAncestorID, "PATIENT ID",
				"CAR T therapy");
		clickAndVerifyLink(new Common("navbar-nav", "MANUFACTURING").genericTextUsingAncestorID, "MANUFACTURING",
				"KITE: Redefining CAR T");
		clickAndVerifyLink(new Common("navbar-nav", "URGENCY TO TREAT").genericTextUsingAncestorID, "URGENCY TO TREAT",
				"Consider YESCARTA");
		clickAndVerifyLink(new Common("navbar-nav", "RESOURCES").genericTextUsingAncestorID, "RESOURCES",
				"KITE KONNECT");
	}

	/**
	 * public void verifyFooterOnYescartaHCP()
	 *
	 * @return
	 */
	public void verifyFooterOnYescartaHCP() {
		if (objectExists(new Common("Footer Logo").imageWithAlternateText, "isDisplayed", loadingWindowTimeOut,
				"YescartaHCP Footer", "Logo", "Yescarta HCP", false)) {
			pagescroll(new Common("Footer Logo").imageWithAlternateText, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify footer logo", "Footer logo should be present",
					"Footer logo is present", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Footer logo", "Footer logo should be present",
					"Footer logo is not present", true);
		}

		clickAndVerifyfooter("Home", "Choose");
		clickAndVerifyfooter("Terms and Conditions", "Terms of Use");
		clickAndVerifyfooter("Privacy Policy", "Privacy Policy");
		clickAndVerifyfooter("Consumer Health Data Privacy Policy", "Consumer Health Data Privacy Policy");
		clickAndVerifyfooter("Contact Us", "Contact Us");
		clickAndVerifyfooter("Site Map", "Site Map");

		if (objectExists(new Common("footer", "This website is intended").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Text", "Footer", "Yescarta HCP", false)) {
			pagescroll(new Common("footer", "This website is intended").genericTextUsingAncestorClass, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify footer Text", "Footer text should be present",
					"Footer text is present", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify Footer text", "Footer text should be present",
					"Footer text is not present", true);
		}
	}

	/**
	 * public void verifyBodyOnYescartaHCPHomePage()
	 *
	 * @return
	 */
	public void verifyBodyOnYescartaHCPHomePage() {
		if (objectExists(new Common("hero", "SURVIVAL.").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "SURVIVAL", "Hero Banner Title", "Yescarta HCP", false)
				&& objectExists(new Common("hero", "DELIVERED.").genericTextUsingAncestorClass, "isDisplayed",
						lngMinTimeOutInSeconds, "DELIVERED", "Hero Banner Title", "Yescarta HCP", false)) {
			pagescroll(new Common("hero", "SURVIVAL.").genericTextUsingAncestorClass, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify hero banner", "Hero banner should load properly",
					"Hero banner is loaded completely", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify hero banner", "Hero banner should load properly",
					"Hero banner is not loaded", true);
		}

		clickAndVerifyLink(new Common("card-deck", "Explore Efficacy data").genericTextUsingAncestorClass,
				"Explore Efficacy data", "Choose YESCARTA");
		returnToYescartaHCPHomePage();
		clickAndVerifyLink(new Common("card-deck", "View safety data").genericTextUsingAncestorClass,
				"View safety data", "YESCARTA demonstrated");
		returnToYescartaHCPHomePage();
		clickAndVerifyLink(new Common("card-deck", "Find a Treatment center").genericTextUsingAncestorClass,
				"Find a Treatment center", "YESCARTA Authorized Treatment Center");
		clickAndVerifyLink(new Common("isi-indication", "More").genericTextUsingAncestorClass, "More",
				"IMPORTANT SAFETY INFORMATION");
	}

	/**
	 * public void verifyEfficacyHomePage()
	 *
	 * @return
	 */
	public void verifyEfficacyHomePage() {
		mouseOver(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, loadingWindowTimeOut, "Efficacy",
				"Menu", "Yescarta HCP");
		clickAndVerifyLink(new Common("navbar-nav", "2L R/R lbcl").genericTextUsingAncestorID, "2L R/R lbcl",
				"Choose YESCARTA");
		mouseOver(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, loadingWindowTimeOut, "Efficacy",
				"Menu", "Yescarta HCP");
		clickAndVerifyLink(new Common("navbar-nav", "3L R/R lbcl").genericTextUsingAncestorID, "3L R/R lbcl",
				"YESCARTA: The FIRST CAR T");
		clickAndVerifyLink(new Common("navbar-nav", "REAL-WORLD").genericTextUsingAncestorID, "REAL-WORLD",
				"YESCARTA in the real world");
		clickAndVerifyLink(new Common("isi-indication", "MORE").genericTextUsingAncestorClass, "MORE",
				"IMPORTANT SAFETY INFORMATION");
	}

	/**
	 * public void verify2LPage()
	 *
	 * @return
	 */
	public void verify2LPage() {
		mouseOver(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, loadingWindowTimeOut, "Efficacy",
				"Menu", "Yescarta HCP");
		clickAndVerifyLink(new Common("navbar-nav", "2L R/R lbcl").genericTextUsingAncestorID, "2L R/R lbcl",
				"Choose YESCARTA");

		if (objectExists(new Common("myTab", "EFS").genericLinkedTextUsingAncestorID, "isDisplayed",
				loadingWindowTimeOut, "EFS", "Menu", "Yescarta HCP", false)) {
			clickByJS(new Common("myTab", "EFS").genericLinkedTextUsingAncestorID, lngMinTimeOutInSeconds, "EFS",
					"Menu", "Yescarta HCP", true);

			if (objectExists(
					new Common("myTabContent", "18 month event-free survival").imageWithAlternateTextUsingAncestorID,
					"isDisplayed", loadingWindowTimeOut, "EFS vs Standard Therapy", "Graph", "Yescarta HCP", true)
					&& objectExists(new Common("EFS vs standard therapy").spanText, "isDisplayed", loadingWindowTimeOut,
							"EFS vs Standard Therapy", "Header", "Yescarta HCP", true)) {
				pagescroll(new Common("myTabContent",
						"18 month event-free survival").imageWithAlternateTextUsingAncestorID, "Yescarta HCP");
				ALMFunctions.UpdateReportLogAndALMForPassStatus(
						"Click on EFS tab and verify the Image and content on the page",
						"Images and content should not be broken on the page",
						"Images and content are not be broken on the page", true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus(
						"Click on EFS tab and verify the Image and content on the page",
						"Images and content should not be broken on the page", "Images and content are not present",
						true);
			}
		}
		clickAndVerifyLink(new Common("myTab", "Overall Survival").genericTextUsingAncestorID, "Overall Survival",
				"SUPERIOR overall survival");
		clickAndVerifyLink(new Common("myTab", "Response").genericTextUsingAncestorID, "Response",
				"objective response rate");
		clickAndVerifyLink(new Common("myTab", "Quality of Life").genericTextUsingAncestorID, "Quality of Life",
				"Health-related quality of life");
		clickAndVerifyLink(new Common("myTab", "Trial Design").genericTextUsingAncestorID, "Trial Design",
				"ZUMA-7 evaluated difficult-to-treat adult patients");

		clickAndVerifyLink(new Common("card-deck", "View safety data").genericTextUsingAncestorClass,
				"View safety data", "YESCARTA demonstrated");
		mouseOver(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, loadingWindowTimeOut, "Efficacy",
				"Menu", "Yescarta HCP");
		clickAndVerifyLink(new Common("navbar-nav", "2L R/R lbcl").genericTextUsingAncestorID, "2L R/R lbcl",
				"Choose YESCARTA");
		clickAndVerifyLink(new Common("card-deck", "Find a Treatment center").genericTextUsingAncestorClass,
				"Find a Treatment center", "YESCARTA Authorized Treatment Center");
	}

	/**
	 * public void verify3LPage()
	 *
	 * @return
	 */
	public void verify3LPage() {
		mouseOver(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, loadingWindowTimeOut, "Efficacy",
				"Menu", "Yescarta HCP");
		clickAndVerifyLink(new Common("navbar-nav", "3L R/R lbcl").genericTextUsingAncestorID, "3L R/R lbcl",
				"YESCARTA: The FIRST CAR T");
		if (objectExists(
				new Common("myTabContent",
						"YESCARTA complete remission rates chart").imageWithAlternateTextUsingAncestorID,
				"isDisplayed", loadingWindowTimeOut, "YESCARTA complete remission rates", "Chart", "Yescarta HCP",
				false)) {
			pagescroll(
					new Common("myTabContent",
							"YESCARTA complete remission rates chart").imageWithAlternateTextUsingAncestorID,
					"Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify images and content on the page ",
					"Images and content should not be broken on the page", "Images and content are not broken", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify images and content on the page ",
					"Images and content should not be broken on the page", "Images and content are not present", true);
		}
		clickAndVerifyLink(new Common("myTab", "Response").genericTextUsingAncestorID, "Response",
				"Patients infused with YESCARTA");
		clickAndVerifyLink(new Common("myTab", "Overall Survival").genericTextUsingAncestorID, "Overall Survival",
				"5 and alive with YESCARTA: 43% of patients alive");
		if (objectExists(new Common("tab2", "").imageWithAlternateTextUsingAncestorID, "isDisplayed",
				loadingWindowTimeOut, "KM Estimate of Overall Survival", "Chart", "Yescarta HCP", false)) {
			pagescroll(new Common("tab2", "").imageWithAlternateTextUsingAncestorID, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify images and content on the page ",
					"Images and content should not be broken on the page", "Images and content are not broken", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify images and content on the page ",
					"Images and content should not be broken on the page", "Images and content are not present", true);
		}
		clickAndVerifyLink(new Common("myTab", "Trial Design").genericTextUsingAncestorID, "Trial Design",
				"The ZUMA-1 population");
		clickAndVerifyLink(new Common("card-deck", "View safety data").genericTextUsingAncestorClass,
				"View safety data", "YESCARTA demonstrated");
		mouseOver(new Common("navbar-nav", "EFFICACY").genericTextUsingAncestorID, loadingWindowTimeOut, "Efficacy",
				"Menu", "Yescarta HCP");
		clickAndVerifyLink(new Common("navbar-nav", "3L R/R lbcl").genericTextUsingAncestorID, "3L R/R lbcl",
				"YESCARTA: The FIRST CAR T");
		clickAndVerifyLink(new Common("card-deck", "Find a Treatment center").genericTextUsingAncestorClass,
				"Find a Treatment center", "YESCARTA Authorized Treatment Center");
	}

	/**
	 * public void verifySafetySite()
	 *
	 * @return
	 */
	public void verifySafetySite() {
		clickAndVerifyLink(new Common("navbar-nav", "SAFETY").genericTextUsingAncestorID, "SAFETY",
				"YESCARTA demonstrated");
		clickAndVerifyLink(new Common("myTab", "2L and 3L Pivotal Trials").genericTextUsingAncestorID,
				"2L and 3L Pivotal Trials", "In 2L, most cases of CRS");
		clickAndVerifyLink(new Common("myTab", "Safety Management Studies in 3L").genericTextUsingAncestorID,
				"Safety Management Studies in 3L", "Similar efficacy results were observed in the clinical trial");
		clickAndVerifyLink(new Common("navbar-nav", "PATIENT ID").genericTextUsingAncestorID, "PATIENT ID",
				"CAR T therapy");
		clickAndVerifyLink(new Common("navbar-nav", "REAL-WORLD").genericTextUsingAncestorID, "REAL-WORLD",
				"YESCARTA in the real world");
	}

	/**
	 * public void verifyPatientIDSite()
	 *
	 * @return
	 */
	public void verifyPatientIDSite() {
		clickAndVerifyLink(new Common("navbar-nav", "PATIENT ID").genericTextUsingAncestorID, "PATIENT ID",
				"CAR T therapy");
		clickAndVerifyLink(new Common("myTab", "Paul, a 56-year-old patient").genericTextUsingAncestorID, "Paul Tab",
				"CLINICAL FITNESS CONSIDERATIONS");
		verifyAccordionOnYescartaHCP("Sharon: female, 57 years old", "collapse1", "Treatment History");
		verifyAccordionOnYescartaHCP("Gary: male, 50 years old", "collapse2", "Treatment History");
		clickAndVerifyLink(new Common("tab2-tab", "Leslie, a 70-year-old patient with poor").genericTextUsingAncestorID,
				"Leslie Tab", "Stage IV LBCL with bone marrow involvement");
		verifyAccordionOnYescartaHCP("Sharon: female, 57 years old", "collapse1", "Treatment History");
		verifyAccordionOnYescartaHCP("Gary: male, 50 years old", "collapse2", "Treatment History");
	}

	/**
	 * public void verifyManufacturingAndProcessPage()
	 *
	 * @return
	 */
	public void verifyManufacturingAndProcessPage() {
		clickAndVerifyLink(new Common("navbar-nav", "MANUFACTURING").genericTextUsingAncestorID, "MANUFACTURING",
				"KITE: Redefining CAR T");
		if (objectExists(new Common("Proactive patient identification:").simpleGenericText, "isDisplayed",
				loadingWindowTimeOut, "Proactive patient identification:", "Header", driver.getCurrentUrl(), true)) {
			pagescroll(new Common("Proactive patient identification:").simpleGenericText, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify before treatment canter section",
					"Section should be there", "Section is present", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify before treatment canter section",
					"Section should be there", "Section is not present", true);
		}
		if (objectExists(new Common("Leukapheresis:").simpleGenericText, "isDisplayed", loadingWindowTimeOut,
				"Leukapheresis:", "Header", driver.getCurrentUrl(), true)) {
			pagescroll(new Common("Leukapheresis:").simpleGenericText, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify during treatment canter section",
					"Section should be there", "Section is present", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify before treatment canter section",
					"Section should be there", "Section is not present", true);
		}
		if (objectExists(new Common("Ongoing care and follow-up:").simpleGenericText, "isDisplayed",
				loadingWindowTimeOut, "Ongoing care and follow-up:", "Header", driver.getCurrentUrl(), true)) {
			pagescroll(new Common("Ongoing care and follow-up:").simpleGenericText, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify after treatment canter section",
					"Section should be there", "Section is present", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify before treatment canter section",
					"Section should be there", "Section is not present", true);
		}
		clickAndVerifyLink(new Common("card-deck", "Find a Treatment center").genericTextUsingAncestorClass,
				"Find a Treatment center", "YESCARTA Authorized Treatment Center");
		clickAndVerifyLink(new Common("navbar-nav", "MANUFACTURING").genericTextUsingAncestorID, "MANUFACTURING",
				"KITE: Redefining CAR T");
		clickAndVerifyLink(new Common("card-deck", "Explore efficacy data").genericTextUsingAncestorClass,
				"Explore Efficacy data", "Choose YESCARTA");
	}

	/**
	 * public void verifyUrgencyToTreatPage()
	 *
	 * @return
	 */
	public void verifyUrgencyToTreatPage() {
		clickAndVerifyLink(new Common("navbar-nav", "URGENCY TO TREAT").genericTextUsingAncestorID, "URGENCY TO TREAT",
				"Consider YESCARTA");
		clickAndVerifyLink(new Common("myTab", "UnmetNeed").genericTextUsingAncestorID, "Unmet Need",
				"CAR T cell therapy could play a significant role");
		clickAndVerifyLink(new Common("myTab", "Importance of Early Initiation").genericTextUsingAncestorID,
				"Importance of Early Initiation", "CAR T therapy should be used at the earliest opportunity");
	}

	/**
	 * public void verifyResourcePage()
	 *
	 * @return
	 */
	public void verifyResourcePage() {
		clickAndVerifyLink(new Common("navbar-nav", "RESOURCES").genericTextUsingAncestorID, "RESOURCES",
				"KITE KONNECT");
		clickAndVerifyLink(new Common("card-deck", "Find a treatment center").genericTextUsingAncestorClass,
				"Find a treatment center", "Refer your patient");
		clickAndVerifyLink(new Common("navbar-nav", "RESOURCES").genericTextUsingAncestorID, "RESOURCES",
				"KITE KONNECT");
		clickAndVerifyLinkOnChildPage(
				new Common("card-deck", "Enroll a YESCARTA patient").genericTextUsingAncestorClass,
				"Enroll a YESCARTA patient", new Common("centerPanel", "").imageWithAlternateTextUsingAncestorID,
				"KiteKonnect Logo");
		clickAndVerifyLinkOnChildPage(
				new Common("coverage-banner", "View coverage in your area").genericTextUsingAncestorClass,
				"View coverage in your area", new Common("INDICATIONS").spanText, "INDICATIONS");
		handleFileDownload(new Common("download-icon-dsk").imageWithAlternateText, "download", "pdf", "Yescarta HCP");
		clickAndVerifyLinkOnChildPage(new Common("external-url-icn-dsk").imageWithAlternateText, "Patient Materials",
				new Common("YESCARTA® is a ").spanText, "YESCARTA®");
	}

	/**
	 * public void verifyTreatmentPage()
	 *
	 * @return
	 */
	public void verifyTreatmentPage() {
		clickAndVerifyLink(new Common("navbar-nav", "Treatment Centers").genericTextUsingAncestorID,
				"Treatment Centers", "YESCARTA Authorized Treatment Center");
		if (objectExists(new Common("mapboxgl-canvas").genericElementWithClass, "isDisplayed", loadingWindowTimeOut,
				"Treatment Centers", "Map", "Yescarta HCP", false)) {
			pagescroll(new Common("mapboxgl-canvas").genericElementWithClass, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify ATC map box section",
					"ATC map Box should load correctly", "ATC map Box is loaded", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify ATC map box section",
					"ATC map Box should load correctly", "ATC map Box is not loaded", true);
		}
		if (objectExists(new Common("locator-mini-map").genericElementWithID, "isDisplayed", loadingWindowTimeOut,
				"Treatment Centers", "Mini-Map", "Yescarta HCP", false)) {
			pagescroll(new Common("locator-mini-map").genericElementWithID, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify mini map on map box", "Mini map should be there",
					"Mini map is present", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify mini map on map box", "Mini map should be there",
					"Mini map is not present", true);
		}

		clickAndVerifyLink(new Common("st_lukes_health_system").genericElementWithID, "St. Lukes Health System Pin",
				"General information");
		clickByJS(new Common("locator-print").genericElementWithID, lngMinTimeOutInSeconds, "Print Information",
				"Accordion", "Yescarta HCP", true);
	}

	/**
	 * public void verifyAccordionOnYescartaHCP(String strAccordionTitle, String
	 * strTabID, String strAccordionHeader)
	 *
	 * @return
	 */
	public void verifyAccordionOnYescartaHCP(String strAccordionTitle, String strTabID, String strAccordionHeader) {
		if (objectExists(new Common("additional-profiles", strAccordionTitle).genericTextUsingAncestorID, "isDisplayed",
				loadingWindowTimeOut, strAccordionTitle, "Accordion", "Yescarta HCP", false)) {
			pagescroll(new Common("additional-profiles", strAccordionTitle).genericTextUsingAncestorID, "Yescarta HCP");
			clickByJS(new Common("additional-profiles", strAccordionTitle).genericTextUsingAncestorID,
					lngMinTimeOutInSeconds, strAccordionTitle, "Accordion", "Yescarta HCP", true);

			if (objectExists(new Common(strTabID, strAccordionHeader).genericTextUsingAncestorID, "isDisplayed",
					loadingWindowTimeOut, strAccordionHeader, "Header", driver.getCurrentUrl(), true)
					&& objectExists(new Common(strTabID, "").imageWithAlternateTextUsingAncestorID, "isDisplayed",
							loadingWindowTimeOut, strAccordionHeader, "Image", driver.getCurrentUrl(), true)) {
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Expand '" + strAccordionTitle + "' Accordion",
						"Images and content should not be broken on the page",
						"Images and content are present in the tab", true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Expand '" + strAccordionTitle + "' Accordion",
						"Images and content should not be broken on the page",
						"Images and content are not present in the tab", true);
			}

			clickByJS(new Common("additional-profiles", strAccordionTitle).genericTextUsingAncestorID,
					lngMinTimeOutInSeconds, strAccordionTitle, "Accordion", "Yescarta HCP", false);
		}
	}

	/**
	 * public void returnToYescartaHCPHomePage()
	 *
	 * @return
	 */
	public void returnToYescartaHCPHomePage() {
		if (objectExists(new Common("navbar-nav", "Yescartahcp Logo").imageWithAlternateTextUsingAncestorID,
				"isDisplayed", loadingWindowTimeOut, "YescartaHCP", "Logo", "Yescarta HCP", false)) {
			clickByJS(new Common("navbar-nav", "Yescartahcp Logo").imageWithAlternateTextUsingAncestorID,
					lngMinTimeOutInSeconds, "YescartaHCP", "Logo", "Yescarta HCP", true);
		}
	}

	/**
	 * public void clickAndVerifyLinkOnChildPage(By by, String strLinkName, By
	 * elementToVerify, String strElementName)
	 *
	 * @return
	 */
	public void clickAndVerifyLinkOnChildPage(By by, String strLinkName, By elementToVerify, String strElementName) {

		if (objectExists(by, "isDisplayed", lngMinTimeOutInSeconds, strLinkName, "Menu", driver.getTitle(), false)) {
			pagescroll(by, "Yescarta HCP");
			clickByJS(by, lngMinTimeOutInSeconds, strLinkName, "Navigation Menu", driver.getTitle(), true);
		}

		switchToChildWindow();
		allowAllCookies();

		if (objectExists(elementToVerify, "isDisplayed", loadingWindowTimeOut, strElementName, "Header",
				driver.getTitle(), false)) {
			pagescroll(elementToVerify, driver.getTitle());
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Click on '" + strLinkName + "'",
					"'" + strLinkName + "' Section should be displayed", "User is able to view valid results", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Click on '" + strLinkName + "'",
					"'" + strLinkName + "' Section should be displayed", "User is unable to view valid results", true);
		}

		switchToChildWindowAndCloseCurrent();
	}

	/**
	 * public void clickAndVerifyfooter(String strFooter, String strHeader)
	 *
	 * @return
	 */
	public void clickAndVerifyfooter(String strFooter, String strHeader) {
		if (objectExists(new Common("footer", strFooter).genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, strFooter, "Footer", "Yescarta HCP", false)) {
			pagescroll(new Common("footer", strFooter).genericTextUsingAncestorClass, "Yescarta HCP");
			clickByJS(new Common("footer", strFooter).genericTextUsingAncestorClass, lngMinTimeOutInSeconds, strFooter,
					"Footer", "Yescarta HCP", true);
		}

		switchToChildWindow();
		allowAllCookies();

		if (objectExists(new Common(strHeader).spanText, "isDisplayed", loadingWindowTimeOut, strHeader, "Header",
				"Yescarta HCP", false)) {
			pagescroll(new Common(strHeader).spanText, "Yescarta HCP");
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Click on '" + strFooter + "' Footer",
					"'" + strFooter + "' Section should be displayed", "'" + strFooter + "' Section sis displayed",
					true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Click on '" + strFooter + "' Footer",
					"'" + strFooter + "' Section should be displayed", "'" + strFooter + "' Section is not displayed",
					true);
		}

		switchToChildWindowAndCloseCurrent();
	}

	/**
	 * Clicks on a given link and verifies if the corresponding section header is
	 * displayed.
	 *
	 * @param by          Locator for the link element.
	 * @param strLinkText Text of the link to be clicked.
	 * @param strHeader   Expected header text of the section that should be
	 *                    displayed.
	 */
	public void clickAndVerifyLink(By by, String strLinkText, String strHeader) {
		String strPageURL = driver.getCurrentUrl();

		if (objectExists(by, "isDisplayed", lngMinTimeOutInSeconds, strLinkText, "Link", strPageURL, true)) {
			pagescroll(by, strPageURL);
			waitFor(1500);
			clickByJS(by, lngMinTimeOutInSeconds, strLinkText, "Link", strPageURL, false);

			driverUtil.waitUntilAjaxLoadingComplete(loadingWindowTimeOut, strPageURL);

			// Check if the header exists in different possible locations
			if (objectExists(new Common(strHeader).spanText, "isDisplayed", staleTimeOut, strHeader, "Header",
					strPageURL, false)
					|| objectExists(new Common("myTabContent", strHeader).genericTextUsingAncestorID, "isDisplayed",
							staleTimeOut, strHeader, "Header", strPageURL, false)
					|| objectExists(new Common("frame_20", strHeader).genericTextUsingAncestorID, "isDisplayed",
							staleTimeOut, strHeader, "Header", strPageURL, false)) {

				ALMFunctions.UpdateReportLogAndALMForPassStatus("Click on '" + strLinkText + "' link",
						"'" + strHeader + "' Section should be displayed", "'" + strHeader + "' Section is displayed",
						true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Click on '" + strLinkText + "' link",
						"'" + strHeader + "' Section should be displayed",
						"'" + strHeader + "' Section is not displayed", true);
			}
		}
	}

	/**
	 * public void verifyPrescribingInformationPDFOnYescartaHCP()
	 *
	 * @return
	 */
	public void verifyPrescribingInformationPDFOnYescartaHCP() {
		if (objectExists(new Common("navbar-nav", "Prescribing Information").genericLinkedTextUsingAncestorID,
				"isDisplayed", lngMinTimeOutInSeconds, "Prescribing Information", "Link", "Yescarta HCP", false)) {
			pagescroll(new Common("navbar-nav", "Prescribing Information").genericLinkedTextUsingAncestorID,
					"Yescarta HCP");
			handleFileDownload(new Common("navbar-nav", "Prescribing Information").genericLinkedTextUsingAncestorID,
					"yescarta-pi", "pdf", "Yescarta HCP");
		}
	}

	/**
	 * public void letsChatCartNavigationAndFunctionality()
	 *
	 * @return
	 */
	public void letsChatCartNavigationAndFunctionality() {
		if (objectExists(new Common("What are you looking for?").textarea, "isDisplayed", lngMinTimeOutInSeconds,
				"Search", "Box", "Lets Chat Cart", false)) {
			sendkeys(new Common("What are you looking for?").textarea, lngPagetimeOutInSeconds, "Who is", "Keyword",
					"Textbox", "Lets Chat Cart", true);

			if (objectExists(new Common("suggested-list", "Who is").genericTextUsingAncestorClass, "isDisplayed",
					lngMinTimeOutInSeconds, "Who is", "Result", "Lets Chat Cart", false)) {
				ALMFunctions.UpdateReportLogAndALMForPassStatus("Click on 'Search Box'",
						"User Should be able to search Relevant topic.", "User is able to view valid results", true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Click on 'Search Box'",
						"User Should be able to search Relevant topic.", "User is unable to view valid results", true);
			}
		}

		clickAndVerifyLink(new Common("card__title", "What is").genericTextUsingAncestorClass, "What is CAR T?",
				"What is CAR");
		clickAndVerifyLink(new Common("jump-link", "Who is CAR").genericTextUsingAncestorClass, "Who is CAR T for?",
				"Who is CAR");
		clickAndVerifyLink(new Common("jump-link", "What can I expect?").genericTextUsingAncestorClass,
				"What can I expect?", "What can I expect with CAR");
		clickAndVerifyLink(new Common("navbarSupportedContent", "For caregivers").genericTextUsingAncestorID,
				"For caregivers", "For caregivers");
		clickAndVerifyLink(new Common("navbarSupportedContent", "Find support").genericTextUsingAncestorID,
				"Find support", "Find support");
		clickAndVerifyLink(new Common("navbarSupportedContent", "Find support").genericTextUsingAncestorID,
				"Find support", "Find support");
	}

	/**
	 * Clicks on a given link, confirms navigation by interacting with a
	 * confirmation button, switches to the child window, allows cookies, and
	 * verifies the presence of an expected element.
	 *
	 * @param by              Locator for the link element.
	 * @param strLinkName     Text of the link to be clicked.
	 * @param elementToVerify Locator of the element expected to be present in the
	 *                        child page.
	 * @param strElementName  Name of the expected element for verification.
	 */
	public void clickAndVerifyLinkOnChildPageAfterConfirming(By by, String strLinkName, By elementToVerify,
			String strElementName) {
		String pageTitle = driver.getTitle();

		if (objectExists(by, "isDisplayed", lngMinTimeOutInSeconds, strLinkName, "Menu", pageTitle, true)) {
			pagescroll(by, pageTitle);
			clickByJS(by, lngMinTimeOutInSeconds, strLinkName, "Navigation Menu", pageTitle, true);

			// Verify presence of confirmation button and proceed
			if (objectExists(new Common("continueBtn").genericTextElementWithClass, "isDisplayed",
					lngMinTimeOutInSeconds, "Leave this site", "Button", pageTitle, true)) {
				clickByJS(new Common("continueBtn").genericTextElementWithClass, lngMinTimeOutInSeconds,
						"Leave this site", "Button", pageTitle, true);

				switchToChildWindow();
				allowAllCookies();

				// Verify element presence in the child window
				if (objectExists(elementToVerify, "isDisplayed", loadingWindowTimeOut, strElementName, "Header",
						driver.getTitle(), false)) {
					pagescroll(elementToVerify, driver.getTitle());
					ALMFunctions.UpdateReportLogAndALMForPassStatus("Click on '" + strLinkName + "'",
							"'" + strLinkName + "' Section should be displayed", "User is able to view valid results",
							true);
				} else {
					ALMFunctions.UpdateReportLogAndALMForFailStatus("Click on '" + strLinkName + "'",
							"'" + strLinkName + "' Section should be displayed", "User is unable to view valid results",
							true);
				}
				switchToChildWindowAndCloseCurrent();
			}
		}
	}

	/**
	 * public void validateElement(By by, String strElementName, String
	 * strElementType, String strPageName)
	 *
	 * @return
	 */
	public void validateElement(By by, String strElementName, String strElementType, String strPageName) {
		if (objectExists(by, "isDisplayed", loadingWindowTimeOut, strElementName, strElementType, strPageName, false)) {
			pagescroll(by, strPageName);
			ALMFunctions.UpdateReportLogAndALMForPassStatus(
					"Validate " + strElementName + " " + strElementType + " on " + strPageName,
					strElementName + " " + strElementType + " should be visible on " + strPageName,
					strElementName + " " + strElementType + " is visible", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Validate " + strElementName + " on " + strPageName,
					strElementName + " " + strElementType + " should be visible on " + strPageName,
					strElementName + " " + strElementType + " is not visible", true);
		}
	}

	/**
	 * public void verifyPDFAndReadMorePane()
	 *
	 * @return
	 */
	public void verifyPDFAndReadMorePane() {
		handleFileDownload(new Common("Prescribing Information").linkedText, "yescarta-pi", "pdf", "Efficacy");

		clickByJS(new Common("Expand to read more").genericElementWithTitle, lngMinTimeOutInSeconds, "More", "Button",
				"Efficacy", true);
		if (objectExists(new Common("IMPORTANT SAFETY INFORMATION").simpleH2Header, "isDisplayed", loadingWindowTimeOut,
				"IMPORTANT SAFETY INFORMATION", "Header", driver.getCurrentUrl(), false)) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus(
					"Click on the 'More' tab from the ISI Tray which is displayed on the bottom of the screen",
					"Users should be able to view about the approved user and critical safety information on the right side pane popup",
					"User is able to view valid results", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus(
					"Click on the 'More' tab from the ISI Tray which is displayed on the bottom of the screen",
					"Users should be able to view about the approved user and critical safety information on the right side pane popup",
					"User is unable to view valid results", true);
		}
	}

	/**
	 * This method clicks on a linked text to open a pane, verifies if the pane is
	 * displayed, and then clicks on the close button to close the pane.
	 *
	 * @param linkedText The text of the link to be clicked initially.
	 * @param trayID     The ID of the tray or pane where the linked information is
	 *                   displayed.
	 * @param header     The header text expected within the tray or pane.
	 * @param tray       The description of the tray or pane to be verified.
	 */
	public void clickAndVerifyPane(String linkedText, String trayID, String header, String siteName) {
// Click on the initial link text
		clickByJS(new Common(linkedText).simpleLinkedText, lngMinTimeOutInSeconds, linkedText, "Linked text", siteName,
				true);

// Check if the corresponding pane is displayed
		if (objectExists(new Common(trayID, header).genericTextUsingAncestorID, "isDisplayed", loadingWindowTimeOut,
				linkedText, header, header + " pane", false)) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Click on '" + linkedText + "'",
					"Right side pane pop up with information of '" + linkedText + "' should be displayed",
					"User is able to view valid results", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Click on '" + linkedText + "'",
					"Right side pane pop up with information of '" + linkedText + "' should be displayed",
					"User is unable to view valid results", true);
		}

// Click on the close button with the final link text
		clickByJS(new Common(trayID, "Close").genericLinkedTextUsingAncestorID, lngMinTimeOutInSeconds, "Close",
				"Button", header + " pane", true);
	}

	/**
	 * Navigates and verifies all navigation menus in Yescarta HCP site.
	 *
	 * @return No return value
	 */
	public void accessNavigationMenuOnYescartaHCP() {
		handleFileDownload(new Common("Prescribing Information").linkedText, "yescarta-pi", "pdf", "Efficacy");
// navigateAndVerifyMenu("General_Data", "Select Indication", "ElementToCheck", true);
	}

	/**
	 * Navigates and verifies all necessary elements on the 'Receiving Yescarta'
	 * site
	 *
	 * @return No return value
	 */
	public void PIandNavigationVerification(String siteName) {
// Navigate to 'Receiving Yescarta' site and verify its elements
		verifyReceivingSite(siteName);

// Verify 'ISI-tray' is displayed
		verifyReadButton();
	}

	/**
	 * public static String subtractDaysExcludingWeekends(String strDateFormat,
	 * String strDateString, int days)
	 *
	 * @return
	 */
	public static String subtractDaysExcludingWeekends(String strDateFormat, String strDateString, int days) {
		LocalDate date;
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(strDateFormat);
			date = LocalDate.parse(strDateString, formatter);
		} catch (DateTimeParseException e) {
			throw new IllegalArgumentException("Invalid date format. Please use " + strDateFormat);
		}
		int intDaysSubtracted = 0;
		while (intDaysSubtracted < days) {
			date = date.minusDays(1);
			if (date.getDayOfWeek() != DayOfWeek.SATURDAY && date.getDayOfWeek() != DayOfWeek.SUNDAY) {
				intDaysSubtracted++;
			}
		}
		return date.format(DateTimeFormatter.ofPattern(strDateFormat));
	}

	/**
	 * Verifies whether the specified components exist on the webpage and are
	 * visible.
	 * 
	 * @param componentColumnName Name of the column in the data sheet containing
	 *                            component XPaths
	 */
	public void verifyComponentExists(String componentColumnName) {
		// Set the keyword count for the current test method
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());

		// Retrieve components from the specified column in the data sheet
		String data = dataTable.getData("FunctionalRegression", componentColumnName);
		
		//handle try catch block
		String[] arrComponents = data.split(";");
		List<String> lstElementsChecked = lstCheckPass.get();
		List<String> lstElementsFailed = lstCheckFail.get();

		// Check if components exist and are visible on the current webpage
		for (String strComponentXPath : arrComponents) {
			try {
				WebElement element = getSingleWebElement(By.xpath(strComponentXPath));
				if (element != null) {
					String strElementName = element.getText();

					if (objectExists(element, "isDisplayed", lngMinTimeOutInSeconds, strElementName,
							"Element Existence", driver.getCurrentUrl(), false)) {
						pagescroll(element, driver.getCurrentUrl());
						lstElementsChecked.add(strElementName);
					} else {
						lstElementsFailed.add(strElementName);
					}
				} else {
					System.out.println("Warning: Element not found for XPath: " + strComponentXPath);
					lstElementsFailed.add(strComponentXPath);
				}
			} catch (NoSuchElementException e) {
				System.out.println("Exception caught: NoSuchElementException for XPath: " + strComponentXPath);
				lstElementsFailed.add(strComponentXPath);
			}
		}

		// Compare expected and actual elements found on the webpage and log results
		if (lstElementsChecked.size() == arrComponents.length) {
			report.updateTestLog("Verify Given Component Exists",
					"Given elements should be visible on the webpage: " + lstElementsChecked,
					"Given elements are visible on the webpage: " + lstElementsChecked, Status.PASS);
		} else {
			report.updateTestLog("Verify Given Component Exists",
					"Given elements should be visible on the webpage: " + lstElementsFailed,
					"Given elements are not visible on the webpage: " + lstElementsFailed, Status.FAIL);
		}

		lstCheckPass.remove();
		lstCheckFail.remove();
	}

	/**
	 * Verifies the functionality of the global search, including the visibility of
	 * the search field, the ability to perform keyword search, sort results by
	 * options, view result counts, and pagination handling.
	 */
	/**
	 * public void globalSearchFunctionality()
	 *
	 * @return
	 */
	public void globalSearchFunctionality() {
// Set the keyword count for the current test method
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());

// Click the global search icon using JavaScript
		if (objectExists(new Common("btn-search-icon").button, "isDisplayed", lngMinTimeOutInSeconds, "Global Search",
				"Button", driver.getCurrentUrl(), false)) {
			click(new Common("btn-search-icon").button, lngMinTimeOutInSeconds, "Global Search", "Button",
					driver.getCurrentUrl(), true);
// Verify if the global search field with a close button is displayed
			if (objectExists(new Common("close-search-window").genericElementWithID, "isDisplayed",
					lngMinTimeOutInSeconds, "Close Search", "Window", driver.getCurrentUrl(), false)) {
				report.updateTestLog("Verify user is able to view global search icon and click on the search",
						"Search field should be displayed with close x button on the search",
						"User is able to view valid results", Status.PASS);
			} else {
				report.updateTestLog("Verify user is able to view global search icon and click on the search",
						"Search field should be displayed with close x button on the search",
						"User is unable to view valid results", Status.FAIL);
			}

// Perform a keyword search
			sendkeys(new Common("Type a keyword").textarea, lngPagetimeOutInSeconds, "Science" + Keys.RETURN, "Keyword",
					"Textbox", driver.getCurrentUrl(), true);
		}

// Verify the existence of tabs
		verifyComponentExists("Component");

// Locate the sort dropdown and select "Relevance" and "Newest" options
		driverUtil.waitUntilElementVisible(new Common("results-count").genericElementWithClass, lngMinTimeOutInSeconds);
		if (objectExists(new Common("flatSortingList", "Relevance").genericTextUsingAncestorID, "isDisplayed",
				lngMinTimeOutInSeconds, "Relevance", "sorting", driver.getCurrentUrl(), true)) {
			report.updateTestLog("Verify user is able to select Sort By: Relevance",
					"User should be able to view the Sort by and should allow to select relevance option and search result should display related to selection",
					"User is able to Sort By: Relevance", Status.PASS);
		} else {
			report.updateTestLog("Verify user is able to select Sort By: Relevance",
					"User should be able to view the Sort by and should allow to select relevance option and search result should display related to selection",
					"User is unable to Sort By: Relevance", Status.FAIL);
		}
		selectDropdownByvalue(new Common("flatSortingList").genericElementWithID, "Sort", "Newest",
				driver.getCurrentUrl(), true);
		driverUtil.waitUntilElementVisible(new Common("results-count").genericElementWithClass, lngMinTimeOutInSeconds);
		if (objectExists(new Common("flatSortingList", "Newest").genericTextUsingAncestorID, "isDisplayed",
				lngMinTimeOutInSeconds, "Newest", "sorting", driver.getCurrentUrl(), true)) {
			report.updateTestLog("Verify user is able to select Sort By: Newest",
					"User should be able to view the Sort by and should allow to select Newest option and search result should display related to selection",
					"User is able to Sort By: Newest", Status.PASS);
		} else {
			report.updateTestLog("Verify user is able to select Sort By: Newest",
					"User should be able to view the Sort by and should allow to select Newest option and search result should display related to selection",
					"User is unable to Sort By: Newest", Status.FAIL);
		}

// Retrieve search results count and related text results-count
		driverUtil.waitUntilElementVisible(new Common("results-count").genericElementWithClass, lngMinTimeOutInSeconds);
		String strFullResultText = getText(new Common("results-count").genericElementWithClass, lngMinTimeOutInSeconds,
				"Results count", driver.getCurrentUrl());
// Extract the number (e.g., 2149)
		String strSearchCount = strFullResultText.split(" ")[0]; // Splitting by space and getting the first part

// Extract the search term (e.g., Science)
		String strSearchText = "";
		if (strFullResultText.contains("'")) {
			int intStartIndex = strFullResultText.indexOf("'") + 1;
			int intEndIndex = strFullResultText.lastIndexOf("'");
			if (intStartIndex < intEndIndex) {
				strSearchText = strFullResultText.substring(intStartIndex, intEndIndex);
			}
		}

// Verify search result count and related text visibility
		if (objectExists(new Common("results-count").genericElementWithClass, "isDisplayed", lngMinTimeOutInSeconds,
				"Search Results", "Count", driver.getCurrentUrl(), false)) {
			report.updateTestLog(
					"Verify user is able to view " + strSearchCount + " Search results for " + strSearchText,
					"User should be able to view the search results with the number of search results in the format "
							+ strSearchCount + " Search results for " + strSearchText,
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to view " + strSearchCount + " Search results for " + strSearchText,
					"User should be able to view the search results with the number of search results in the format "
							+ strSearchCount + " Search results for " + strSearchText,
					"User is unable to view valid results", Status.FAIL);
		}

// Retrieve pagination elements and concatenate pagination details

		if (objectExists(new Common("page-selector-list").genericElementWithClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Pagination", "Link", driver.getCurrentUrl(), false)) {
			String strPaginationValues = getText(new Common("page-selector-list").genericElementWithClass,
					lngMinTimeOutInSeconds, "Sort", driver.getCurrentUrl());
			pagescroll(new Common("page-selector-list").genericElementWithClass, driver.getCurrentUrl());
			report.updateTestLog(
					"Verify user is able to view the search results pagination at the bottom of the search results for more pages",
					"User should be able to view the search results pagination: '" + strPaginationValues + "'",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to view the search results pagination at the bottom of the search results for more pages",
					"User should be able to view the search results pagination", "User is unable to view valid results",
					Status.FAIL);
		}
	}

	/**
	 * Generic method to retrieve the text alignment property of a web element.
	 *
	 * @param elementLocator - By locator for the web element
	 * @return - The text alignment value (e.g., "center", "left", "right")
	 */
	/**
	 * public String getTextAlign(By elementLocator)
	 *
	 * @return
	 */
	public String getTextAlign(By elementLocator) {
		try {
// Find the web element using the provided locator
			WebElement element = driver.findElement(elementLocator);

// Retrieve the CSS value for text alignment
			return element.getCssValue("text-align");
		} catch (Exception e) {
// Handle the exception and return a default or error value
			System.out.println("Error retrieving text-align property: " + e.getMessage());
			return "Error"; // You can use a more descriptive fallback value
		}
	}

	/**
	 * Generic method to retrieve a list of WebElements based on a locator.
	 *
	 * @param elementLocator - By locator used to locate the WebElements
	 * @return - List of WebElements matching the locator
	 */
	/**
	 * public List<WebElement> getListWebElements(By elementLocator)
	 *
	 * @return
	 */
	public List<WebElement> getListWebElements(By elementLocator) {
		try {
// Find the list of WebElements using the provided locator
			return driver.findElements(elementLocator);
		} catch (Exception e) {
// Handle the exception gracefully and return an empty list
			System.out.println("Error retrieving elements: " + e.getMessage());
			return new ArrayList<>(); // Returns an empty list in case of an error
		}
	}

	/**
	 * Generic method to retrieve a single WebElement based on a locator.
	 *
	 * @param elementLocator - By locator used to locate the WebElement
	 * @return - The located WebElement, or null if not found or an exception occurs
	 */
	public WebElement getSingleWebElement(By elementLocator) {
		try {
// Find the WebElement using the provided locator
			return driver.findElement(elementLocator);
		} catch (Exception e) {
// Handle the exception gracefully and return null
			System.out.println("Error retrieving element: " + e.getMessage());
			return null; // Return null in case of an error
		}
	}

	/**
	 * Verifies the functionality of the "News Press Release" page. This method sets
	 * the keyword count for the current test method and performs a series of
	 * actions to validate the navigation, visibility, and alignment of elements on
	 * the News Press Release page.
	 */
	/**
	 * public void newsPressRelease()
	 *
	 * @return
	 */
	public void newsPressRelease() {
// Set the keyword count for the current test method
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());

// Navigate to "Media" menu if it exists
		driverUtil.waitUntilElementVisible(new Common("field-link", "Media").genericTextUsingAncestorClass,
				lngMinTimeOutInSeconds, "Media", "Menu", driver.getCurrentUrl(), false);
		if (objectExists(new Common("field-link", "Media").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Media", "Menu", driver.getCurrentUrl(), true)) {
			click(new Common("field-link", "Media").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "Media",
					"Menu", driver.getCurrentUrl(), true);
// Navigate to "View All News Releases" if it exists
			if (objectExists(new Common("View All News Releases").simpleLinkedText, "isDisplayed",
					lngMinTimeOutInSeconds, "View All News Releases", "Link", driver.getCurrentUrl(), true)) {
				pagescroll(new Common("View All News Releases").simpleLinkedText, driver.getCurrentUrl());
				report.updateTestLog(
						"Verify user is able view the 'view all new Release' button displayed at the bottom of page and click on it",
						"News Release Landing page should be displayed", "User is able to view valid results",
						Status.PASS);
				driverUtil.waitUntilElementVisible(new Common("View All News Releases").simpleLinkedText,
						lngMinTimeOutInSeconds);
				click(new Common("View All News Releases").simpleLinkedText, lngPagetimeOutInSeconds,
						"View All News Releases", "Link", driver.getCurrentUrl(), true);
			} else {
				report.updateTestLog(
						"Verify user is able view the 'view all new Release' button displayed at the bottom of page and click on it",
						"News Release Landing page should be displayed", "User is unable to view valid results",
						Status.FAIL);
			}
		}

// Verify breadcrumbs and heading alignment
		if (getBreadcrumbTextsFormatted().equalsIgnoreCase("-News Releases") && "center".equalsIgnoreCase(
				getTextAlign(new Common("banner-text", "News Releases").genericTextUsingAncestorClass))) {
			report.updateTestLog("Verify breadcrumbs and heading alignment",
					"Breadcrumb must be visible and heading must be aligned to the center",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify breadcrumbs and heading alignment",
					"Breadcrumb must be visible and heading must be aligned to the center",
					"User is unable to view valid results " + getBreadcrumbTextsFormatted(), Status.FAIL);
		}

// Verify dropdowns and search box
		if (objectExists(new Common("gl-dropdown-label", "Categories").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Categories", "Dropdown", driver.getCurrentUrl(), false)
				&& objectExists(new Common("gl-dropdown-label", "Years").genericTextUsingAncestorClass, "isDisplayed",
						lngMinTimeOutInSeconds, "Years", "Dropdown", driver.getCurrentUrl(), false)
				&& objectExists(new Common("Search").textarea, "isDisplayed", lngMinTimeOutInSeconds, "Searchbox",
						"Input", driver.getCurrentUrl(), false)) {
			report.updateTestLog("Verify the two dropdown boxes and one search box displayed",
					"User should be able to view the two dropdown boxes and one search box",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify the two dropdown boxes and one search box displayed",
					"User should be able to view the two dropdown boxes and one search box",
					"User is unable to view valid results", Status.FAIL);
		}

// Filter by year
		click(new Common("gl-dropdown-label", "Years").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "Years",
				"Filter", driver.getCurrentUrl(), true);
		click(new Common("facet-value", "2025").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "2025",
				"Filter", driver.getCurrentUrl(), true);
		click(new Common("facet-value", "2024").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "2024",
				"Filter", driver.getCurrentUrl(), true);

		driverUtil.waitUntilElementVisible(new Common("gl-f-tags").genericElementWithClass, lngMinTimeOutInSeconds,
				"Filters applied", "Row", driver.getCurrentUrl(), false);

// Retrieve and log applied filters
		waitFor(5000);
		String strYearFilters = "";
		for (WebElement yearFilterElement : getListWebElements(new Common("gl-f-tags").genericElementWithClass)) {
			strYearFilters += yearFilterElement.getText() + " ";
		}
// Trim any extra space at the end
		strYearFilters = strYearFilters.trim();

		if (strYearFilters.equals("2025 2024")) {
			report.updateTestLog(
					"Verify user is able to filter the 'news press release landing' page based on the year by using Filter by single/multiple years",
					"User should be allowed to filter the 'news press release landing' based on the year by using Filter by single/multiple years",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to filter the 'news press release landing' page based on the year by using Filter by single/multiple years",
					"User should be allowed to filter the 'news press release landing' based on the year by using Filter by single/multiple years",
					"User is unable to view valid results. The filters are: " + strYearFilters, Status.FAIL);
		}

// Click filters
		click(new Common("gl-dropdown-label", "Categories").genericTextUsingAncestorClass, lngPagetimeOutInSeconds,
				"Years", "Filter", driver.getCurrentUrl(), true);
		click(new Common("facet-value", "Corporate News").genericTextUsingAncestorClass, lngPagetimeOutInSeconds,
				"Corporate News", "Filter", driver.getCurrentUrl(), true);
		click(new Common("facet-value", "Cell Therapy").genericTextUsingAncestorClass, lngPagetimeOutInSeconds,
				"Cell Therapy", "Filter", driver.getCurrentUrl(), true);
		waitFor(3000);
// Wait for the filters to be applied
		driverUtil.waitUntilElementVisible(new Common("gl-f-tags").genericElementWithClass, lngMinTimeOutInSeconds,
				"Filters applied", "Row", driver.getCurrentUrl(), false);

		if (objectExists(new Common("gl-f-tags").genericElementWithClass, "isDisplayed", lngMinTimeOutInSeconds, "All",
				"Filters", driver.getCurrentUrl(), true)) {
// Safely build concatenated string with re-fetched elements
			String strAllFilters = "";
			for (WebElement eleFilter : getListWebElements(new Common("gl-f-tags").genericElementWithClass)) {
				strAllFilters += eleFilter.getText() + " ";
			}

// Trim the result to remove trailing spaces
			strAllFilters = strAllFilters.trim();
			pagescroll(getListWebElements(new Common("gl-content-icon").genericElementWithClass).get(1),
					driver.getCurrentUrl());
			if (strAllFilters.equals("Cell Therapy Corporate News 2025 2024")) {
				report.updateTestLog(
						"Verify user is able to filter the 'news press release landing' page based on the category using Filter by single/multiple selections",
						"User should be allowed to filter the 'news press release landing' based on the year using Filter by category and should view the search results",
						"User is able to view valid results", Status.PASS);
			} else {
				report.updateTestLog(
						"Verify user is able to filter the 'news press release landing' page based on the category using Filter by single/multiple selections",
						"User should be allowed to filter the 'news press release landing' based on the year using Filter by category and should view the search results",
						"User is unable to view valid results. The filters are: " + strAllFilters, Status.FAIL);
			}
			clickByJS(new Common("gl-clear-all-filter-id", "Clear All").genericTextUsingAncestorID,
					lngPagetimeOutInSeconds, "Clear All", "Filter", driver.getCurrentUrl(), true);

			if (!objectExists(new Common("gl-f-tags").genericElementWithClass, "isDisplayed", lngMinTimeOutInSeconds,
					"Filters applied", "Row", driver.getCurrentUrl(), false)) {
				report.updateTestLog(
						"Verify the user is able to view the Clear All option and clear filters and search options",
						"User should be able to view the Clear All option and clicking on it clears filters and search options",
						"User is able to view valid results", Status.PASS);
			} else {
				report.updateTestLog(
						"Verify the user is able to view the Clear All option and clear filters and search options",
						"User should be able to view the Clear All option and clicking on it clears filters and search options",
						"User is unable to view valid results. The filters are: " + strAllFilters, Status.FAIL);
			}
		}

// Perform search and verify search results
		sendkeys(new Common("Search").textarea, lngPagetimeOutInSeconds, "Science" + Keys.RETURN, "Search", "Textbox",
				driver.getCurrentUrl(), true);
		String strSearchText = "";
		if (objectExists(new Common("search-text-placeholder").genericElementWithClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Search applied", "Header", driver.getCurrentUrl(), false)) {
			strSearchText = getSingleWebElement(new Common("search-text-placeholder").genericElementWithClass)
					.getText();
		}
		pagescroll(getListWebElements(new Common("gl-content-icon").genericElementWithClass).get(1),
				driver.getCurrentUrl());
		if (strSearchText.equals("Science")) {
			report.updateTestLog(
					"Verify user is able to perform search functions and view the search results in News Press Release landing page",
					"User should be able to perform search functions and view the search results in News Press Release landing page without any error message",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to perform search functions and view the search results in News Press Release landing page",
					"User should be able to perform search functions and view the search results in News Press Release landing page without any error message",
					"User is unable to view valid results.", Status.FAIL);
		}

// Check date format of results
		List<WebElement> lstResultDates = getListWebElements(
				new Common("gl-notes-date field-news-date").genericElementWithClass);

// Verify the format and order of the dates
		pagescroll(lstResultDates.get(0), driver.getCurrentUrl());
		int intLatestDate = 0;
		boolean blnIsOrdered = true;
		boolean blnIsValidDate = true;

		System.out.println("Entering the loop");
		for (int i = 0; i < lstResultDates.size() - 1; i++) { // Go up to the second-to-last element
			WebElement eleDate = lstResultDates.get(i);
			String strDateText = eleDate.getText().trim();
			System.out.println("Current date text: " + strDateText);

			try {
// Parse the date using the provided formatter
				int intCurrentDate = Integer.parseInt(strDateText.split(",")[1].trim());

// Verify the order (latest to oldest)
				if (intCurrentDate < intLatestDate) {
					blnIsOrdered = false; // Dates are not ordered correctly
					break; // Exit the loop early if order is incorrect
				}
				intLatestDate = intCurrentDate;
			} catch (Exception e) {
				blnIsValidDate = false; // Date format is incorrect
				System.out.println("Exception caught: " + e.getMessage());
				break; // Exit the loop early if format is invalid
			}

			if (blnIsOrdered && blnIsValidDate) {
				report.updateTestLog(
						"Verify the user is able to view latest test results at the top with month date and year displayed (MMM DD, YYYY format)",
						"User should be able to view latest test results at the top with month date and year displayed",
						"User is able to view valid results", Status.PASS);
			} else {
				report.updateTestLog(
						"Verify the user is able to view latest test results at the top with month date and year displayed (MMM DD, YYYY format)",
						"User should be able to view latest test results at the top with month date and year displayed",
						"User is unable to view valid results", Status.FAIL);
			}

// Your dropdown, pagination, and other logic remains unchanged
		}
		boolean blnCompanyStatementsCard = objectExists(new Common("Company Statements").simpleH3Header, "isDisplayed",
				lngMinTimeOutInSeconds, "Company Statements", "Header", driver.getCurrentUrl(), false);
		boolean blnMediaContacts = objectExists(new Common("Media Contacts").simpleH3Header, "isDisplayed",
				lngMinTimeOutInSeconds, "Media Contacts", "Header", driver.getCurrentUrl(), false);

		String strHeaderName = dataTable.getData("Components_to_Verify", "Component");
		WebElement eleResultHeader = getListWebElements(By.xpath(strHeaderName)).get(1);
		String strResultHeaderText = eleResultHeader.getText();
		clickByJS(eleResultHeader, lngPagetimeOutInSeconds, "Media", "Menu", driver.getCurrentUrl(), true);

		if (objectExists(new Common("row gl-white", strResultHeaderText).genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Result", "Header", driver.getCurrentUrl(), false)) {
			report.updateTestLog(
					"Verify user is able to click on the link in the search result to view the search result page in the same page",
					"Should open the link without any error message with breadcrumb at the top of the page",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to click on the link in the search result to view the search result page in the same page",
					"Should open the link without any error message with breadcrumb at the top of the page",
					"User is unable to view valid results", Status.FAIL);
		}
		if (blnCompanyStatementsCard && blnMediaContacts) {
			report.updateTestLog(
					"Verify the 2 cards are available in News release Landing page: 'Media contacts' and 'Company Statements'",
					"User should be able to view the 2 cards available in News release Landing page: 'Media contacts' and 'Company Statements'",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify the 2 cards are available in News release Landing page: 'Media contacts' and 'Company Statements'",
					"User should be able to view the 2 cards available in News release Landing page: 'Media contacts' and 'Company Statements'",
					"User is unable to view valid results", Status.FAIL);
		}
	}

	/**
	 * Verifies the functionality of the "Company Statements" page. This includes
	 * navigation to the "Media" page, checking the "Company Statements" card,
	 * verifying the alignment of breadcrumbs and heading, performing search
	 * functions, filtering results by year, and validating the applied filters.
	 */
	/**
	 * public void verifyCompanyStatement()
	 *
	 * @return
	 */
	public void verifyCompanyStatement() {
// Set the keyword count for the current test method
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());

// Check if the "Media" link exists and is displayed
		if (objectExists(new Common("field-link", "Media").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Media", "Menu", driver.getCurrentUrl(), false)) {
// Click on the "Media" link using JavaScript
			click(new Common("field-link", "Media").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "Media",
					"Menu", driver.getCurrentUrl(), true);
		}

// Locate the "Company Statements" card
		driverUtil.waitUntilElementVisible(
				getSingleWebElement(new Common("g-info-item", "Company Statements").genericTextUsingAncestorClass),
				lngMinTimeOutInSeconds, "Company Statements", "Card", driver.getCurrentUrl(), false);

// If the card is displayed, click on it
		if (objectExists(
				getSingleWebElement(new Common("g-info-item", "Company Statements").genericTextUsingAncestorClass),
				"isDisplayed", lngMinTimeOutInSeconds, "Company Statements", "Header", driver.getCurrentUrl(), false)) {
			getSingleWebElement(new Common("g-info-item", "Company Statements").genericTextUsingAncestorClass).click();
		}

// Verify breadcrumbs and heading alignment
		String strTextAlign = getSingleWebElement(
				new Common("banner-text", "Company Statements").genericTextUsingAncestorClass)
				.getCssValue("text-align");
		if (getBreadcrumbTextsFormatted().equals("-Company-Company Statements") && "center".equals(strTextAlign)) {
			report.updateTestLog("Verify breadcrumbs and heading alignment",
					"Breadcrumb must be visible and heading must be aligned to the center",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify breadcrumbs and heading alignment",
					"Breadcrumb must be visible and heading must be aligned to the center",
					"User is unable to view valid results " + getBreadcrumbTextsFormatted(), Status.FAIL);
		}

// Perform search functionality and verify results
		sendkeys(new Common("Search").textarea, lngPagetimeOutInSeconds, "HIV" + Keys.RETURN, "Search", "Textbox",
				driver.getCurrentUrl(), true);
		waitFor(3000);
		String strSearchText = "";
		if (objectExists(new Common("search-text-placeholder").genericElementWithClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Search applied", "Header", driver.getCurrentUrl(), false)) {
			strSearchText = getSingleWebElement(new Common("search-text-placeholder").genericElementWithClass)
					.getText();
		}
		if (strSearchText.trim().equals("HIV")) {
			report.updateTestLog(
					"Verify user is able to perform search functions and view the search results in News Press Release landing page",
					"User should be able to perform search functions and view the search results in News Press Release landing page without any error message",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to perform search functions and view the search results in News Press Release landing page",
					"User should be able to perform search functions and view the search results in News Press Release landing page without any error message",
					"User is unable to view valid results.", Status.FAIL);
		}

// Filter by year
		clickByJS(new Common("gl-dropdown-label", "Filter by year").genericTextUsingAncestorClass,
				lngPagetimeOutInSeconds, "Years", "Filter", driver.getCurrentUrl(), true);
		clickByJS(new Common("facet-value", "2025").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "2025",
				"Filter", driver.getCurrentUrl(), true);
		clickByJS(new Common("facet-value", "2024").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "2024",
				"Filter", driver.getCurrentUrl(), true);
		driverUtil.waitUntilElementVisible(new Common("gl-f-tags").genericElementWithClass, lngMinTimeOutInSeconds,
				"Filters applied", "Row", driver.getCurrentUrl(), false);
		waitFor(4000);
		String strYearFilter = "";
		for (WebElement eleYearFilter : getListWebElements(new Common("gl-f-tags").genericElementWithClass)) {
			strYearFilter += eleYearFilter.getText() + " ";
		}
// Trim any extra space at the end
		strYearFilter = strYearFilter.trim();

		if (strYearFilter.equals("2025 2024")) {
			report.updateTestLog(
					"Verify user is able to filter the 'news press release landing' page based on the year by using Filter by single/multiple years",
					"User should be allowed to filter the 'news press release landing' based on the year by using Filter by single/multiple years",
					"User is able to view valid results. The filters are: " + strYearFilter, Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to filter the 'news press release landing' page based on the year by using Filter by single/multiple years",
					"User should be allowed to filter the 'news press release landing' based on the year by using Filter by single/multiple years",
					"User is unable to view valid results. The filters are: " + strYearFilter, Status.FAIL);
		}
		waitFor(5000);
		WebElement firstDate = getListWebElements(new Common("company-date").genericTextElementWithClass).get(0);
		pagescroll(firstDate, driver.getTitle());
		if (getListWebElements(new Common("company-date").genericTextElementWithClass).size() > 0) {
// Extract the year from the first result (adjust XPath to match the year
// element)
			String strDate = firstDate.getText();
			String year = strDate.split(",")[1];
			if (("2025").equalsIgnoreCase(year.trim())) {
				report.updateTestLog(
						"Verify the user is able to view latest year company statement results at the top when user selects multiple year option in Filter by year ",
						"User should be able to view latest year results at the top when user selects multiple year option in Filter by year ",
						"User is able to view valid results", Status.PASS);
			} else {
				report.updateTestLog(
						"Verify the user is able to view latest year company statement results at the top when user selects multiple year option in Filter by year ",
						"User should be able to view latest year results at the top when user selects multiple year option in Filter by year ",
						"User is unable to view valid results. The Year is : " + year, Status.FAIL);
			}
		}
		waitFor(10000);
		WebElement firstResult = getListWebElements(new Common("gl-content-icon").genericElementWithClass).get(1);
		String resultHeader = getText(firstResult, lngPagetimeOutInSeconds, "First Result Header",
				driver.getCurrentUrl());
		clickByJS(firstResult, lngPagetimeOutInSeconds, "Search result", "Date", driver.getCurrentUrl(), true);
		if (getText(new Common("field-company-title").genericElementWithClass, lngPagetimeOutInSeconds,
				"Result Page Header", driver.getCurrentUrl()).equals(resultHeader)) {
			report.updateTestLog(
					"Verify user is able to click on any of the company statement link to view the page of the search results",
					"Company statement pages on the search results should display with 'view more' option in bottom to view all the company statement links",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to click on any of the company statement link to view the page of the search results",
					"Company statement pages on the search results should display with 'view more' option in bottom to view all the company statement links",
					"User is unable to view valid results. Expected Header : " + resultHeader + "; Actual header : '"
							+ getText(new Common("field-company-title").genericElementWithClass,
									lngPagetimeOutInSeconds, "Result Page Header", driver.getCurrentUrl())
							+ "'",
					Status.FAIL);
		}
	}

	/**
	 * Verifies the functionality of the "Pipeline" section under the "Science"
	 * menu. This method ensures that navigation is correct, dropdowns and search
	 * functionality are working, the overview section is aligned properly, and the
	 * "Understanding Pipeline Terms" section is displayed.
	 */
	/**
	 * public void verifyPipeline()
	 *
	 * @return
	 */
	public void verifyPipeline() {
// Set the keyword count for the current test method
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());

// Check if the "Science" link exists and is displayed
		if (objectExists(new Common("clearfix", "Science").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Science", "Menu", driver.getCurrentUrl(), false)) {
// click on the "Science" link using JavaScript
			click(new Common("clearfix", "Science").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "Science",
					"Menu", driver.getCurrentUrl(), true);
		}

// Check if the "Pipeline" link exists and is displayed
		if (objectExists(new Common("level-menu-list", "Pipeline").genericLinkedTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Pipeline", "Menu", driver.getCurrentUrl(), false)) {
// Click on the "Pipeline" link using JavaScript
			click(new Common("level-menu-list", "Pipeline").genericLinkedTextUsingAncestorClass,
					lngPagetimeOutInSeconds, "Pipeline", "Menu", driver.getCurrentUrl(), true);
		}

// Verify dropdowns and search field in the Pipeline section
		boolean blnDropdownsAndSearchExist = objectExists(
				new Common("gl-dropdown-label", "Therapeutic Areas").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Therapeutic Areas", "Dropdown", driver.getCurrentUrl(), false)
				&& objectExists(new Common("gl-dropdown-label", "Trial Phase").genericTextUsingAncestorClass,
						"isDisplayed", lngMinTimeOutInSeconds, "Trial Phase", "Dropdown", driver.getCurrentUrl(), false)
				&& objectExists(new Common("Search our Pipeline").textarea, "isDisplayed", lngMinTimeOutInSeconds,
						"Search our Pipeline", "Input", driver.getCurrentUrl(), false);

		if (blnDropdownsAndSearchExist) {
			report.updateTestLog(
					"Verify user navigates to 'Pipeline' section from Science Menu to view the Pipeline page",
					"Pipeline page should be displayed with the following elements:"
							+ "1. Therapeutic Areas dropdown, 2. Trial Phase dropdown, 3. Search field",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user navigates to 'Pipeline' section from Science Menu to view the Pipeline page",
					"Pipeline page should be displayed with the following elements:"
							+ "1. Therapeutic Areas dropdown, 2. Trial Phase dropdown, 3. Search field",
					"User is unable to view valid results", Status.FAIL);
		}

// Perform search functionality
		sendkeys(new Common("Search our Pipeline").textarea, lngPagetimeOutInSeconds, "Oncology" + Keys.RETURN,
				"Search", "Textbox", driver.getCurrentUrl(), true);
		waitFor(3000);
		String strSearchText = "";
		if (objectExists(new Common("search-text-placeholder").genericElementWithClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Search applied", "Header", driver.getCurrentUrl(), false)) {
			strSearchText = getSingleWebElement(new Common("search-text-placeholder").genericElementWithClass)
					.getText();
		}

		if (strSearchText.equals("Oncology")) {
			report.updateTestLog(
					"Verify user is able to view search results based on the keyword search or Therapeutic area selection or Trial phase selection",
					"Search results should be displayed based on the selection of the filter values or search keywords",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify user is able to view search results based on the keyword search or Therapeutic area selection or Trial phase selection",
					"Search results should be displayed based on the selection of the filter values or search keywords",
					"User is unable to view valid results", Status.FAIL);
		}

// Verify breadcrumbs and heading alignment
		String strTextAlign = getSingleWebElement(new Common("banner-text", "PIPELINE").genericTextUsingAncestorClass)
				.getCssValue("text-align");
		if (getBreadcrumbTextsFormatted().equals("-Science-Pipeline") && "start".equalsIgnoreCase(strTextAlign)) {
			report.updateTestLog("Verify breadcrumbs and heading alignment",
					"Breadcrumb must be visible and heading must be aligned to the left",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify breadcrumbs and heading alignment",
					"Breadcrumb must be visible and heading must be aligned to the left " + strTextAlign,
					"User is unable to view valid results " + getBreadcrumbTextsFormatted(), Status.FAIL);
		}

// Verify Overview section alignment
		int intOverviewHeadingPosition = getSingleWebElement(
				new Common("container", "Overview").genericTextUsingAncestorClass).getLocation().getY();
		WebElement eleOverviewDescription = getSingleWebElement(
				new Common("container", "pioneering science").genericTextUsingAncestorClass);
		int intOverviewDescriptionPosition = eleOverviewDescription.getLocation().getY();
		String strOverviewTextAlign = getSingleWebElement(
				new Common("container", "Overview").genericTextUsingAncestorClass).getCssValue("text-align");

		pagescroll(new Common("container", "pioneering science").genericTextUsingAncestorClass, driver.getCurrentUrl());
		if (intOverviewDescriptionPosition > intOverviewHeadingPosition
				&& "center".equalsIgnoreCase(strOverviewTextAlign)) {
			report.updateTestLog("Verify Overview section alignment and content",
					"User should view the heading text in the center and description below it in the Overview section",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify Overview section alignment and content",
					"User should view the heading text in the center and description below it in the Overview section",
					"User is unable to view valid results " + strOverviewTextAlign, Status.FAIL);
		}

// Verify "Understanding Pipeline Terms" section
		pagescroll(new Common("container", "Pipeline Terms").genericTextUsingAncestorClass, driver.getCurrentUrl());
		if (objectExists(new Common("container", "Pipeline Terms").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Understanding Pipeline Terms", "Header", driver.getCurrentUrl(), false)) {
			report.updateTestLog("Verify 'Understanding Pipeline Terms' section displays in the Pipeline page",
					"'Understanding Pipeline Terms' section should be displayed in the Pipeline page",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify 'Understanding Pipeline Terms' section displays in the Pipeline page",
					"'Understanding Pipeline Terms' section should be displayed in the Pipeline page",
					"User is unable to view valid results", Status.FAIL);
		}
	}

	/**
	 * Verifies the functionality of the "Stories" page. This includes navigation,
	 * breadcrumb validation, banner alignment, search functionality, pagination,
	 * story cards verification, and category display checks.
	 *
	 * @throws ParseException if a date parsing error occurs
	 */
	/**
	 * public void verifyStoryPage() throws ParseException
	 *
	 * @return
	 */
	public void verifyStoryPage() throws ParseException {
// Set the keyword count for the current test method
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());

// Check if the "Stories" menu item exists and is displayed
		if (objectExists(new Common("navigation-title field-navigationtitle", "Stories").genericTextUsingAncestorClass,
				"isDisplayed", lngMinTimeOutInSeconds, "Stories", "Menu", driver.getCurrentUrl(), false)) {
			click(new Common("navigation-title field-navigationtitle", "Stories").genericTextUsingAncestorClass,
					lngPagetimeOutInSeconds, "Stories", "Menu", driver.getCurrentUrl(), true);
		}

// Verify breadcrumb alignment
		if (getBreadcrumbTextsFormatted().equalsIgnoreCase("-stories")) {
			report.updateTestLog(
					"Verify breadcrumb of stories appears in the stories landing page e.g., home icon> Stories",
					"User should be able to view the breadcrumb on the top left corner of the page",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify breadcrumb of stories appears in the stories landing page e.g., home icon> Stories",
					"User should be able to view the breadcrumb on the top left corner of the page",
					"User is unable to view valid results", Status.FAIL);
		}

// Verify Stories banner alignment
		String strStoriesAtGileadImgPath = dataTable.getData("Components_to_Verify", "Component");
		String strStoriesAtGileadImgAlign = getSingleWebElement(By.xpath(strStoriesAtGileadImgPath))
				.getCssValue("text-align");

		if ("Start".equalsIgnoreCase(strStoriesAtGileadImgAlign)) {
			report.updateTestLog("Verify Stories banner with center-aligned text and banner image",
					"Banner should be displayed in the center of the stories page",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify Stories banner with center-aligned text and banner image",
					"Banner should be displayed in the center of the stories page",
					"User is unable to view valid results " + strStoriesAtGileadImgAlign, Status.FAIL);
		}

// Verify search functionality and elements
		click(new Common("align-items-right", "").genericLinkedTextUsingAncestorClass, lngPagetimeOutInSeconds,
				"Search", "Button", driver.getCurrentUrl(), true);
		boolean blnSearchElementsExist = objectExists(
				new Common("gl-dropdown-label", "Filter By").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Filter By", "Dropdown", driver.getCurrentUrl(), false)
				&& objectExists(new Common("Search").textarea, "isDisplayed", lngMinTimeOutInSeconds, "Search", "Input",
						driver.getCurrentUrl(), false)
				&& objectExists(new Common("align-items-right", "").genericLinkedTextUsingAncestorClass, "isDisplayed",
						lngMinTimeOutInSeconds, "X", "Button", driver.getCurrentUrl(), false);

		if (blnSearchElementsExist) {
			report.updateTestLog("Verify user is able to view search icon and click on search icon",
					"User should be able to view 'Filter by' and search field with X button",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify user is able to view search icon and click on search icon",
					"User should be able to view 'Filter by' and search field with X button",
					"User is unable to view valid results", Status.FAIL);
		}

// Perform keyword search and verify results
		sendkeys(new Common("Search").textarea, lngPagetimeOutInSeconds, "Covid-19" + Keys.RETURN, "Search", "Input",
				driver.getCurrentUrl(), true);
		String strSearchText = "Covid-19";
		if (objectExists(new Common("search-text-placeholder").genericElementWithClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Search applied", "Header", driver.getCurrentUrl(), false)) {
			strSearchText = getSingleWebElement(new Common("search-text-placeholder").genericElementWithClass)
					.getText();
		}
		waitFor(3000);
		pagescroll(new Common("search-text-placeholder").genericElementWithClass, driver.getCurrentUrl());

		if (strSearchText.equals("Covid-19")) {
			report.updateTestLog("Verify user is able to view search results based on the keyword searched",
					"Search results should be displayed with the list of stories matching the search keyword and displays 'showing results for "
							+ strSearchText + "'",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify user is able to view search results based on the keyword searched",
					"Search results should be displayed with the list of stories matching the search keyword and displays 'showing results for "
							+ strSearchText + "'",
					"User is unable to view valid results", Status.FAIL);
		}

// Initialize variables
		pagescroll(getListWebElements(new Common("field-storydate").genericTextElementWithClass).get(0),
				driver.getCurrentUrl());
		int latestDate = 0;
		boolean blnIsOrdered = true;
		boolean blnIsValidDate = true;
		boolean blnPaginationValid = true;
		boolean blnAllCategoriesPresent = true;

		List<WebElement> lstStoryDates = getListWebElements(new Common("field-storydate").genericTextElementWithClass);

		for (int i = 0; i < 5; i++) { // Iterate up to the second-to-last element
			WebElement eleDate = lstStoryDates.get(i);
			String strDateText = eleDate.getText().trim();

			try {
// Parse the date using the provided formatter
				int dtCurrentDate = Integer.parseInt(strDateText.split(",")[1].trim());

// Verify the order (latest to oldest)
				if (dtCurrentDate < latestDate) {
					blnIsOrdered = false; // Dates are not ordered correctly
					break; // Exit the loop early if order is incorrect
				}
				latestDate = dtCurrentDate;
			} catch (Exception e) {
				blnIsValidDate = false; // Date format is incorrect
				break; // Exit the loop early if format is invalid
			}
		}

// Verify pagination
		String strPaginationPath = dataTable.getData("Components_to_Verify", "Component3");
		String strCompletePagination = "";

		for (WebElement elePageNumber : getListWebElements(By.xpath(strPaginationPath))) {
			strCompletePagination += " " + elePageNumber.getText();
		}

		pagescroll(By.xpath(strPaginationPath), driver.getCurrentUrl());
		if (!objectExists(By.xpath(strPaginationPath), "isDisplayed", lngMinTimeOutInSeconds, "Pagination", "Link",
				driver.getCurrentUrl(), false)) {
			blnPaginationValid = false; // Pagination is invalid
		}

// Verify categories and "View All" links
		String strCategories = dataTable.getData("Components_to_Verify", "Component2");
		String[] arrCategories = strCategories.split(";");

		for (String strCategory : arrCategories) {
			if (getSingleWebElement(new Common(strCategory).simpleH3Header) != null) {
				WebElement eleViewAllLink = getSingleWebElement(new Common(strCategory).simpleH3Header);
				if (eleViewAllLink == null || !eleViewAllLink.isDisplayed()) {
					blnAllCategoriesPresent = false;
				}
			} else {
				blnAllCategoriesPresent = false;
			}
		}

// Final Test Log Reporting
		if (blnIsOrdered && blnIsValidDate && blnPaginationValid) {
			report.updateTestLog("Verify latest stories section displays with pagination",
					"User should be able to view all stories with pagination '" + strCompletePagination
							+ "' and the latest stories at the top of the page",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify latest stories section displays with pagination",
					"User should be able to view all stories with pagination '" + strCompletePagination
							+ "' and the latest stories at the top of the page" + ", Is Ordered: " + blnIsOrdered
							+ ", Is Valid Date: " + blnIsValidDate + ", Is Pagination Valid: " + blnPaginationValid,
					"User is unable to view valid results", Status.FAIL);
		}

		if (blnAllCategoriesPresent) {
			report.updateTestLog(
					"Verify the below Stories category section displays with view all link" + "1. Scientific Innovation"
							+ "2. Access and Health Equity" + "3. People and Culture",
					"All the below 3 categories should be displayed with a list of stories to the category level"
							+ "1. Scientific Innovation" + "2. Access and Health Equity" + "3. People and Culture",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify the below Stories category section displays with view all link" + "1. Scientific Innovation"
							+ "2. Access and Health Equity" + "3. People and Culture",
					"All the below 3 categories should be displayed with a list of stories to the category level"
							+ "1. Scientific Innovation" + "2. Access and Health Equity" + "3. People and Culture",
					"User is unable to view valid results", Status.FAIL);
		}

		clickByJS(new Common("gl-clear-all-filter-id", "Clear All").genericTextUsingAncestorID, lngPagetimeOutInSeconds,
				"Clear all", "Button", driver.getCurrentUrl(), true);
		waitFor(5000);
		if (objectExists(new Common("scientific-innovation", "Advancing Oncology").genericTextUsingAncestorClass,
				"isDisplayed", lngMinTimeOutInSeconds, "Advancing Oncology", "Story", driver.getCurrentUrl(), false)) {
			clickByJS(new Common("scientific-innovation", "Advancing Oncology").genericTextUsingAncestorClass,
					lngPagetimeOutInSeconds, "Advancing Oncology", "Story", driver.getCurrentUrl(), true);

			WebElement iFrame = driver.findElement(new Common("gl-stories-banner").genericIFrameUsingAncestorClass);
			driver.switchTo().frame(iFrame);
			waitFor(3000);
			clickByJS(new Common("main-video").genericElementWithClass, lngMinTimeOutInSeconds,
					"Advancing Oncology video", "Button", driver.getCurrentUrl(), true);
			waitFor(3000);
			videoPlay("Gilead Oncology Research Scholars Program - YouTube");
			driver.switchTo().defaultContent();
		}
	}

	/**
	 * Verifies the functionality of the "Therapeutic Areas" (STA) section under the
	 * "Science" menu. This method includes navigation checks, submenu validation,
	 * breadcrumb verification, and the presence of subcategories like Virology,
	 * Oncology, and Inflammation.
	 */
	/**
	 * public void verifySTA()
	 *
	 * @return
	 */
	public void verifySTA() {
// Set the keyword count for the current test method
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());

// Navigate to "Science" menu
		if (objectExists(new Common("clearfix", "Science").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Science", "Menu", driver.getCurrentUrl(), false)) {
			click(new Common("clearfix", "Science").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "Science",
					"Menu", driver.getCurrentUrl(), true);
		}

// Navigate to "Therapeutic Areas"
		if (objectExists(new Common("level-menu-list", "Therapeutic Areas").genericLinkedTextUsingAncestorClass,
				"isDisplayed", lngMinTimeOutInSeconds, "Therapeutic Areas", "Menu", driver.getCurrentUrl(), false)) {
			click(new Common("level-menu-list", "Therapeutic Areas").genericLinkedTextUsingAncestorClass,
					lngPagetimeOutInSeconds, "Therapeutic Areas", "Menu", driver.getCurrentUrl(), true);
		}

// Verify "Therapeutic Areas" submenu
		boolean blnAllSubMenusExist = true;
		String[] arrSubMenus = dataTable.getData("Components_to_Verify", "Component").split(";");
		for (String strSubMenu : arrSubMenus) {
			driverUtil.waitUntilElementVisible(new Common("g-m-level-3", strSubMenu).genericLinkedTextUsingAncestorID,
					lngMinTimeOutInSeconds, strSubMenu, "Sub-Menu", driver.getCurrentUrl(), false);
			boolean blnSubMenuExists = objectExists(
					new Common("g-m-level-3", strSubMenu).genericLinkedTextUsingAncestorID, "isDisplayed",
					lngMinTimeOutInSeconds, strSubMenu, "Sub-Menu", driver.getCurrentUrl(), false);
			if (!blnSubMenuExists) {
				blnAllSubMenusExist = false;
				break;
			}
		}

// Log the results of "Therapeutic Areas" submenu verification
		if (blnAllSubMenusExist) {
			report.updateTestLog("Verify user navigates to 'Therapeutic Areas' link from Science",
					"Therapeutic Area menu should display submenus - Virology, Oncology, Inflammation",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify user navigates to 'Therapeutic Areas' link from Science",
					"Therapeutic Area menu should display submenus - Virology, Oncology, Inflammation",
					"User is unable to view valid results", Status.FAIL);
		}

// Click on "Therapeutic Areas" link
		click(new Common("g-m-level-3", "Therapeutic Areas").genericLinkedTextUsingAncestorID, lngPagetimeOutInSeconds,
				"Therapeutic Areas", "Sub Menu", driver.getCurrentUrl(), true);

// Verify breadcrumb on "Therapeutic Areas" page
		if (getBreadcrumbTextsFormatted().equalsIgnoreCase("-Science-Therapeutic Areas")) {
			report.updateTestLog("Verify click on Therapeutic Areas link navigates to Therapeutic Areas page",
					"Clicking on the link should display the Therapeutic Areas page",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify click on Therapeutic Areas link navigates to Therapeutic Areas page",
					"Clicking on the link should display the Therapeutic Areas page",
					"User is unable to view valid results. The actual breadcrumb text is: "
							+ getBreadcrumbTextsFormatted(),
					Status.FAIL);
		}

// Click on "Virology" card
		click(new Common("card-title", "Virology").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "Virology",
				"Card", driver.getCurrentUrl(), true);

// Verify "Virology" submenu
		boolean blnAllVirologySubMenusExist = true;
		String[] arrVirologySubMenus = dataTable.getData("Components_to_Verify", "Component3").split(";");
		for (String strVirologySubMenu : arrVirologySubMenus) {
			boolean blnVirologySubMenuExists = objectExists(
					new Common("slider-title", strVirologySubMenu).genericTextUsingAncestorClass, "isDisplayed",
					lngMinTimeOutInSeconds, strVirologySubMenu, "Sub-Menu", driver.getCurrentUrl(), false);
			pagescroll(new Common("slider-title", strVirologySubMenu).genericTextUsingAncestorClass, driver.getTitle());
			if (!blnVirologySubMenuExists) {
				blnAllVirologySubMenusExist = false;
				break;
			}
		}

// Log the results of "Virology" submenu verification
		pagescroll(new Common("card-title", arrSubMenus[1]).genericTextUsingAncestorClass, driver.getTitle());
		if (blnAllVirologySubMenusExist) {
			report.updateTestLog(
					"Verify Virology submenu navigates to HIV, Viral Hepatitis, Covid-19, and Emerging Virus",
					"All the below submenus should be displayed when navigating to Virology submenu: HIV, Viral Hepatitis, Covid-19, Emerging Virus",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify Virology submenu navigates to HIV, Viral Hepatitis, Covid-19, and Emerging Virus",
					"All the below submenus should be displayed when navigating to Virology submenu: HIV, Viral Hepatitis, Covid-19, Emerging Virus",
					"User is unable to view valid results", Status.FAIL);
		}

// Verify Oncology and Inflammation navigation
// Clicking on each card
		click(new Common("card-title", "Oncology").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "Oncology",
				"Card", driver.getCurrentUrl(), true);
		if (getBreadcrumbTextsFormatted().equalsIgnoreCase("-Science-Therapeutic Areas-Oncology")) {
			report.updateTestLog("Verify user is able to click on Oncology",
					"Oncology page should display with breadcrumb: Science > Therapeutic Areas > Oncology",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify user is able to click on Oncology",
					"Oncology page should display with breadcrumb: Science > Therapeutic Areas > Oncology",
					"User is unable to view valid results. Actual breadcrumbs: " + getBreadcrumbTextsFormatted(),
					Status.FAIL);
		}

// Repeat same for Inflammation
		click(new Common("card-title", "Inflammation").genericTextUsingAncestorClass, lngPagetimeOutInSeconds,
				"Inflammation", "Card", driver.getCurrentUrl(), true);
		if (getBreadcrumbTextsFormatted().equalsIgnoreCase("-Science-Therapeutic Areas-Inflammation")) {
			report.updateTestLog("Verify user is able to click on Inflammation",
					"Inflammation page should display with breadcrumb: Science > Therapeutic Areas > Inflammation",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify user is able to click on Inflammation",
					"Inflammation page should display with breadcrumb: Science > Therapeutic Areas > Inflammation",
					"User is unable to view valid results. Actual breadcrumbs: " + getBreadcrumbTextsFormatted(),
					Status.FAIL);
		}

// verify virology sub menu
		click(new Common("Science").genericElementWithTitle, lngPagetimeOutInSeconds, "Science", "Menu",
				driver.getCurrentUrl(), true);
		click(new Common("level-menu-list", "Therapeutic Areas").genericLinkedTextUsingAncestorClass,
				lngPagetimeOutInSeconds, "Therapeutic Areas", "Sub-Menu", driver.getCurrentUrl(), true);
		click(new Common("level-menu-list", "Virology").genericLinkedTextUsingAncestorClass, lngPagetimeOutInSeconds,
				"Virology", "Sub-Menu", driver.getCurrentUrl(), true);
		click(new Common("level-menu-list", "Viral Hepatitis").genericLinkedTextUsingAncestorClass,
				lngPagetimeOutInSeconds, "Viral Hepatitis", "Sub-Menu", driver.getCurrentUrl(), true);

		if (getBreadcrumbTextsFormatted().equals("-Science-Therapeutic Areas-Virology-Viral Hepatitis")) {
			report.updateTestLog(
					"Verify bottom of Therapeutic Areas page displays Virology, Oncology, and Inflammation cards",
					"The bottom of the Therapeutic Areas page should display the Virology, Oncology, and Inflammation cards",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify bottom of Therapeutic Areas page displays Virology, Oncology, and Inflammation cards",
					"The bottom of the Therapeutic Areas page should display the Virology, Oncology, and Inflammation cards",
					"User is unable to view valid results. Actual breadcrumbs are : " + getBreadcrumbTextsFormatted(),
					Status.FAIL);
		}

// Validate bottom page displays correctly Therapeutic Areas
		click(new Common("breadcrumbs", "Therapeutic Areas").genericLinkedTextUsingAncestorClass,
				lngPagetimeOutInSeconds, "Inflammation", "Card", driver.getCurrentUrl(), true);
		boolean blnCardsExist = true;
		pagescroll(new Common("card-title", arrSubMenus[1]).genericTextUsingAncestorClass, driver.getTitle());
		for (String strSubMenu : arrSubMenus) {
			boolean blnSubMenuExists = objectExists(new Common("card-title", strSubMenu).genericTextUsingAncestorClass,
					"isDisplayed", lngMinTimeOutInSeconds, strSubMenu, "Card", driver.getCurrentUrl(), false);
			if (!blnSubMenuExists) {
				blnCardsExist = false;
				break;
			}
		}

		if (blnCardsExist) {
			report.updateTestLog(
					"Verify bottom of Therapeutic Areas page displays Virology, Oncology, and Inflammation cards",
					"The bottom of the Therapeutic Areas page should display the Virology, Oncology, and Inflammation cards",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify bottom of Therapeutic Areas page displays Virology, Oncology, and Inflammation cards",
					"The bottom of the Therapeutic Areas page should display the Virology, Oncology, and Inflammation cards",
					"User is unable to view valid results", Status.FAIL);
		}
	}

	/**
	 * Verifies the functionality of the "Medicines" page under the menu. This
	 * method includes navigation checks, submenu validation, pop-up modal
	 * verification, and ensures the presence of critical elements such as Patient
	 * Safety, Medication Access, and Authorized Distributors.
	 */
	/**
	 * public void verifyMedicinesPage()
	 *
	 * @return
	 */
	public void verifyMedicinesPage() {
// Set the keyword count for the current test method
		setKeywordCount(Thread.currentThread().getStackTrace()[1].getMethodName());

// clickHamberMenu();

// Navigate to "Medicines" menu
		if (objectExists(new Common("clearfix", "Medicines").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Medicines", "Menu", driver.getCurrentUrl(), false)) {
			click(new Common("clearfix", "Medicines").genericTextUsingAncestorClass, lngPagetimeOutInSeconds,
					"Medicines", "Menu", driver.getCurrentUrl(), true);
		}

// Verify all "Medicines" submenu items
		boolean blnAllSubMenusExist = true;
		String[] arrSubMenus = dataTable.getData("Components_to_Verify", "Component").split(";");
		for (String strSubMenu : arrSubMenus) {
			driverUtil.waitUntilElementVisible(
					new Common("menu-left-border", strSubMenu).genericLinkedTextUsingAncestorClass,
					lngMinTimeOutInSeconds, strSubMenu, "Sub-Menu", driver.getCurrentUrl(), false);
			boolean blnSubMenuExists = objectExists(
					new Common("menu-left-border", strSubMenu).genericLinkedTextUsingAncestorClass, "isDisplayed",
					lngMinTimeOutInSeconds, strSubMenu, "Sub-Menu", driver.getCurrentUrl(), false);
			if (!blnSubMenuExists) {
				blnAllSubMenusExist = false;
				break;
			}
		}

// Log the results of "Medicines" submenu verification
		if (blnAllSubMenusExist) {
			report.updateTestLog("Click on Medicines menu",
					"Should display Medicines submenu with Patient Safety, Medication Access, and Authorized Distributors submenu links",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Click on Medicines menu",
					"Should display Medicines submenu with Patient Safety, Medication Access, and Authorized Distributors submenu links",
					"User is unable to view valid results", Status.FAIL);
		}

// Click on "Medicines" submenu link
		clickByJS(new Common("menu-left-border", "Medicines").genericLinkedTextUsingAncestorClass,
				lngPagetimeOutInSeconds, "Medicines", "Sub Menu", driver.getCurrentUrl(), true);
		driverUtil.waitUntilElementVisible(new Common("modal show", "The content").genericTextUsingAncestorClass,
				timeOutInSeconds);
// Verify the pop-up modal on the Medicines submenu page
		driverUtil.waitUntilElementVisible(new Common("modal show", "The content").genericTextUsingAncestorClass,
				timeOutInSeconds);
		boolean blnVerifyPopupOnMedicines = objectExists(
				new Common("modal show", "The content").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Page information", "Text", driver.getCurrentUrl(), false)
				&& objectExists(new Common("modal show", "Confirm").genericLinkedTextUsingAncestorClass, "isDisplayed",
						lngMinTimeOutInSeconds, "Confirm", "Button", driver.getCurrentUrl(), false)
				&& objectExists(new Common("modal show", "Back").genericLinkedTextUsingAncestorClass, "isDisplayed",
						lngMinTimeOutInSeconds, "Back to Gilead.com", "Button", driver.getCurrentUrl(), false)
				&& objectExists(new Common("modal show", "×").genericTextUsingAncestorClass, "isDisplayed",
						lngMinTimeOutInSeconds, "Close", "Button", driver.getCurrentUrl(), false);
// driverUtil.waitUntilPageReadyStateComplete(lngPagetimeOutInSeconds);
// Log results of the pop-up modal verification
		if (blnVerifyPopupOnMedicines) {
			report.updateTestLog("Click on Medicines submenu link",
					"System should display the pop-up with the following details:\n"
							+ "1. 'The content on this page contains information about Gilead products and is intended for audiences in the United States.'\n"
							+ "2. 'Confirm' button\n" + "3. 'Back to Gilead.com' button\n"
							+ "4. 'Close (X)' button in the top corner",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Click on Medicines submenu link",
					"System should display the pop-up with the following details:\n"
							+ "1. 'The content on this page contains information about Gilead products and is intended for audiences in the United States.'\n"
							+ "2. 'Confirm' button\n" + "3. 'Back to Gilead.com' button\n"
							+ "4. 'Close (X)' button in the top corner",
					"User is unable to view valid results", Status.FAIL);
		}
// Click "Confirm" on the pop-up
		click(new Common("modal show", "Confirm").genericLinkedTextUsingAncestorClass, lngPagetimeOutInSeconds,
				"Confirm", "Button", driver.getCurrentUrl(), true);

		waitFor(3000);
// Verify the presence of "Medicines" tabs
		boolean blnMedicinesTabsExist = true;
		String[] arrMedicineTabs = dataTable.getData("Components_to_Verify", "Component3").split(";");
		pagescroll(new Common("facet-value", arrMedicineTabs[1]).genericTextUsingAncestorClass, driver.getCurrentUrl());
		for (String strMedicineTab : arrMedicineTabs) {
			driverUtil.waitUntilElementVisible(new Common("facet-value", strMedicineTab).genericTextUsingAncestorClass,
					lngMinTimeOutInSeconds, strMedicineTab, "Sub-Menu", driver.getCurrentUrl(), false);
			boolean blnSubMenuExists = objectExists(
					new Common("facet-value", strMedicineTab).genericTextUsingAncestorClass, "isDisplayed",
					lngMinTimeOutInSeconds, strMedicineTab, "Sub-Menu", driver.getCurrentUrl(), false);
			if (!blnSubMenuExists) {
				blnMedicinesTabsExist = false;
				break;
			}
		}

		boolean blnVerifyMedicinesTabs = blnMedicinesTabsExist
				&& objectExists(new Common("Search medicines").textarea, "isDisplayed", lngMinTimeOutInSeconds,
						"Searchbox", "Input", driver.getCurrentUrl(), false)
				&& objectExists(new Common("g-alphabets").genericElementWithClass, "isDisplayed",
						lngMinTimeOutInSeconds, "Filter By Alphabets", "Bar", driver.getCurrentUrl(), false);

		waitFor(3000);
// Log results for medicines tabs verification
		if (blnVerifyMedicinesTabs) {
			report.updateTestLog(
					"Verify the medicines page displays multiple tabs like:\n"
							+ "1. All; Oncology; Virology; Inflammation; Others\n" + "2. Search Medicines\n"
							+ "3. Filter by ABCDEF…",
					"Medicines page should display multiple tabs including:\n"
							+ "1. All; Oncology; Virology; Inflammation; Others\n" + "2. Search Medicines\n"
							+ "3. Filter by ABCDEF…",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog(
					"Verify the medicines page displays multiple tabs like:\n"
							+ "1. All; Oncology; Virology; Inflammation; Others\n" + "2. Search Medicines\n"
							+ "3. Filter by ABCDEF…",
					"Medicines page should display multiple tabs including:\n"
							+ "1. All; Oncology; Virology; Inflammation; Others\n" + "2. Search Medicines\n"
							+ "3. Filter by ABCDEF…",
					"User is unable to view valid results", Status.FAIL);
		}

// Perform search and verify results
		sendkeys(new Common("Search medicines").textarea, lngPagetimeOutInSeconds, "Biktarvy" + Keys.RETURN, "Search",
				"Textbox", driver.getCurrentUrl(), true);
		waitFor(2000);
		pagescroll(new Common("MORE INFO ").linkedText, driver.getCurrentUrl());
		if (objectExists(new Common("field-description", "Biktarvy").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Biktarvy", "Header", driver.getCurrentUrl(), false)) {
			report.updateTestLog("Search any single/multiple keywords in the medicines page",
					"Search results should display based on the keyword e.g., Biktarvy",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Search any single/multiple keywords in the medicines page",
					"Search results should display based on the keyword e.g., Biktarvy",
					"User is unable to view valid results", Status.FAIL);
		}

// Apply filter by alphabet and verify results
		clickByJS(new Common("alphabets", "B").genericTextUsingAncestorClass, lngPagetimeOutInSeconds, "'B'", "Filter",
				driver.getCurrentUrl(), true);
		pagescroll(new Common("MORE INFO ").linkedText, driver.getCurrentUrl());
		if (objectExists(new Common("field-description", "Biktarvy").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Biktarvy", "Header", driver.getCurrentUrl(), false)) {
			report.updateTestLog("Select any Therapeutic areas medicines or click on filter by letters",
					"Selecting enabled letters should display related medicine details",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Select any Therapeutic areas medicines or click on filter by letters",
					"Selecting enabled letters should display related medicine details",
					"User is unable to view valid results", Status.FAIL);
		}

// Verify patient information and additional details
		pagescroll(new Common("MORE INFO ").linkedText, driver.getCurrentUrl());
		if (objectExists(new Common("gl-pdf-info", "Patient Information").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "Patient Information", "PDF", driver.getCurrentUrl(), false)
				&& objectExists(new Common("MORE INFO ").linkedText, "isDisplayed", lngMinTimeOutInSeconds, "More Info",
						"Link", driver.getCurrentUrl(), false)) {
			report.updateTestLog("Verify medicine name with icon displays with PDF and More Info details",
					"Medicine name with icon should display with a PDF link and More Info link to view the page",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify medicine name with icon displays with PDF and More Info details",
					"Medicine name with icon should display with a PDF link and More Info link to view the page",
					"User is unable to view valid results", Status.FAIL);
		}

// Verify "Show All" functionality
		clickByJS(new Common("removeClick").genericElementWithClass, lngPagetimeOutInSeconds, "'X'", "Button",
				driver.getCurrentUrl(), true);
		waitFor(3000);
		int intInitialVisibleCount = 0;
		for (WebElement eleCard : getListWebElements(new Common("field-title").genericElementWithClass)) {
			if (eleCard.isDisplayed()) {
				intInitialVisibleCount++;
			}
		}

		click(new Common("button-load-more").genericElementWithID, lngPagetimeOutInSeconds, "Show All", "Button",
				driver.getCurrentUrl(), true);
		waitFor(3000);
		int intFinalVisibleCount = 0;
		for (WebElement eleCard : getListWebElements(new Common("field-title").genericElementWithClass)) {
			if (eleCard.isDisplayed()) {
				intFinalVisibleCount++;
			}
		}

		if (intFinalVisibleCount > intInitialVisibleCount) {
			report.updateTestLog("Verify 'Show All' displays all the medicines",
					"'Show All' should display all the medicines on the medicines page",
					"User is able to view valid results", Status.PASS);
		} else {
			report.updateTestLog("Verify 'Show All' displays all the medicines",
					"'Show All' should display all the medicines on the medicines page. Initial count: "
							+ intInitialVisibleCount + ", Final count: " + intFinalVisibleCount,
					"User is unable to view valid results", Status.FAIL);
		}
	}

	/**
	 * Retrieves and formats breadcrumb texts by joining them with a hyphen.
	 *
	 * @return A formatted string of breadcrumb texts separated by hyphens.
	 */
	public String getBreadcrumbTextsFormatted() {
// Extract breadcrumb texts, trim whitespaces, and join them with a hyphen
		return getListWebElements(new Common("breadcrumbs-list", "").genericLinkedTextUsingAncestorClass).stream()
				.map(WebElement::getText).map(String::trim).collect(Collectors.joining("-"));
	}

	/**
	 * Compares the current URL of the web page with the expected URL and updates
	 * the report log accordingly.
	 *
	 * @param expectedURL       The expected URL of the page.
	 * @param expectedPageTitle The expected title of the page.
	 *
	 *                          This method waits for the AJAX loading to complete
	 *                          before verifying the page's URL. If the URL matches,
	 *                          it logs a pass status; otherwise, it logs a fail
	 *                          status.
	 */
	public void compareURL(String expectedURL, String expectedPageTitle) {
		driverUtil.waitUntilAjaxLoadingComplete(lngMinTimeOutInSeconds, expectedPageTitle);

		if (expectedURL.equals(driver.getCurrentUrl())) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify " + expectedPageTitle + " page is displayed",
					expectedPageTitle + " page should be displayed with expected URL", "Page is displayed", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Verify " + expectedPageTitle + " page is displayed",
					expectedPageTitle + " page should be displayed with expected URL", "Page is not displayed", true);
		}
	}

	public void validateYescartaMenu() {
		// make sure page is completely loaded
		refreshPageIfNotLoaded(CtrlTimeOut);
		if (objectExists(new Common("primaryNavigation").genericElementWithID, "isDisplayed", loadingWindowTimeOut,
				"Menu", "Button", "Yescarta.com", false)) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus(
					"Verify the main menu are visible"
							+ "1. Starting points; 2. About Yescarta ; 3. Support & Resources; 4. Caregiver Support",
					"All the menu should be visible and displayed", "All the menu are visible", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus(
					"Verify the main menu are visible"
							+ "1. Starting points; 2. About Yescarta ; 3. Support & Resources; 4. Caregiver Support",
					"All the menu should be visible and displayed", "All the menu are not displayed", true);
		}
	}

	/**
	 * Navigates and verifies all necessary elements on the 'At a Glance' site.
	 *
	 * @param siteName The name of the site (e.g., "Yescarta" or "Tecartus").
	 */
	public void verifyAtAGlanceSite(String siteName) {
		// Navigate to the 'At a Glance' section
		click(new Common("About " + siteName.toUpperCase()).navigationMenu, lngMinTimeOutInSeconds,
				"About " + siteName.toUpperCase(), "Navigation Menu", siteName, true);
		click(new Common("level-2", siteName.toUpperCase() + " at a glance").genericTextUsingAncestorClass,
				lngMinTimeOutInSeconds, siteName.toUpperCase() + " at a glance", "Navigation Menu", siteName, true);
		// make sure page is completely loaded
		refreshPageIfNotLoaded(CtrlTimeOut);
		compareURL("https://www." + siteName.toLowerCase() + ".com/" + siteName.toLowerCase() + "-at-a-glance",
				siteName + " at a Glance");
		// Verify elements on the page
		verifyReadButton();
		clickAndVerifyPane("Use the locator tool", "atc-locator", "locator tool", siteName);
	}

	/**
	 * Method to navigate and verify all necessary elements in 'Receiving Yescarta'
	 * Site
	 *
	 * @return No return value
	 */
	public void verifyReceivingSite(String siteName) {
		// clicking on 'Receiving' inside 'About Yescarta' navigation menu
		click(new Common("About " + siteName.toUpperCase()).navigationMenu, lngMinTimeOutInSeconds,
				"About " + siteName.toUpperCase(), "Navigation Menu", siteName, true);
		click(new Common("level-2", "Receiving " + siteName.toUpperCase()).genericTextUsingAncestorClass,
				lngMinTimeOutInSeconds, "Receiving " + siteName.toUpperCase(), "Navigation Menu", siteName, true);
		// make sure page is completely loaded
		refreshPageIfNotLoaded(CtrlTimeOut);
		compareURL("https://www." + siteName.toLowerCase() + ".com/receiving-" + siteName.toLowerCase() + "",
				"Receiving " + siteName);
		verifyReadButton();

		if (siteName.equalsIgnoreCase("Yescarta")) {
			clickAndVerifyLink(new Common("What is the treatment process?").linkedText,
					"What is the treatment process?", "What is the treatment process?");
		} else if (siteName.equalsIgnoreCase("Tecartus")) {
			clickAndVerifyPane("Use the locator tool", "atc-locator", "locator tool", siteName);
		}
	}

	/**
	 * Verifies the clinical trial results
	 */
	public void verifyClinicalTrialResults(String siteName) {
		// Navigate to 'Results'
		clickByJS(new Common("About " + siteName.toUpperCase()).navigationMenu, lngMinTimeOutInSeconds,
				"About " + siteName.toUpperCase(), "Navigation Menu", siteName, true);
		clickByJS(new Common("level-2", siteName.toUpperCase() + " results").genericTextUsingAncestorClass,
				lngMinTimeOutInSeconds, siteName.toUpperCase() + " results", "Navigation Menu", siteName, true);
		// make sure page is completely loaded
		refreshPageIfNotLoaded(CtrlTimeOut);
		compareURL("https://www." + siteName.toLowerCase() + ".com/clinical-trial-results", "Clinical Trial Results");
		verifyReadButton();
		if (siteName.equalsIgnoreCase("Tecartus")) {
			clickAndVerifyLink(new Common("trail-MCL", "Trial design").textWithDataPaneID, "Trial Design - MCL",
					"Trial in MCL");
			clickAndVerifyLink(new Common("trail-MCL", "Results and highlights").genericTextUsingAncestorID,
					"Results and highlights - MCL", "Trial in MCL");
			clickAndVerifyLink(new Common("trail-MCL", "Close").genericTextUsingAncestorID, "Close",
					"What are the results from the clinical trials?");

			clickAndVerifyLink(new Common("trail-ALL", "Trial design").textWithDataPaneID, "Trial Design - ALL",
					"Trial in ALL");
			clickAndVerifyLink(new Common("trail-ALL", "Results and highlights").genericTextUsingAncestorID,
					"Results and highlights - ALL", "Trial in ALL");
			clickAndVerifyLink(new Common("trail-ALL", "Close").genericTextUsingAncestorID, "Close",
					"What are the results from the clinical trials?");
		}
	}

	/**
	 * Navigates and verifies all necessary elements on the 'Support & Resources'
	 * site.
	 *
	 * @return No return value
	 */
	public void verifySupportAndResourcesSite(String siteName) {
		// Verify 'Support & Resources' navigation
		clickByJS(new Common("Support & Resources").navigationMenu, lngMinTimeOutInSeconds, "Support & Resources",
				"Navigation Menu", siteName, true);
		// make sure page is completely loaded
		refreshPageIfNotLoaded(CtrlTimeOut);
		if (siteName.equalsIgnoreCase("Yescarta")) {
			compareURL("https://www." + siteName.toLowerCase() + ".com/support-and-resources", "Support and Resources");
		}
		verifyReadButton();

		if (siteName.equalsIgnoreCase("Tecartus")) {
			handleFileDownload(new Common("TECARTUS Patient").simpleLinkedText,
					"yescarta-tecartus-rems-patient-wallet-card-english-2024", "pdf", siteName);
		}
	}

	/**
	 * Navigates and verifies all necessary elements on the 'Support & Resources'
	 * site.
	 *
	 * @return No return value
	 */
	public void validateCaregiverSupportSite(String siteName) {
		// Verify 'Support & Resources' navigation
		clickByJS(new Common("Caregiver Support").navigationMenu, lngMinTimeOutInSeconds, "Caregiver Support",
				"Navigation Menu", siteName, true);
		// make sure page is completely loaded
		refreshPageIfNotLoaded(CtrlTimeOut);
		compareURL("https://www." + siteName.toLowerCase() + ".com/caregiver-support", "Caregiver Support");
	}

	/**
	 * Verifies the "YESCARTA Patient Wallet Card" PDF functionality on the "Support
	 * & Resources" page. Includes steps to navigate to the page, validate UI
	 * elements, download the PDF, and remove it post-verification.
	 */
	public void verifyPDFKiteKonnect(String siteName) {
		// Navigate to "Support & Resources" page and verify it has loaded
		navigateAndVerifyMenu("General_Data", "Support & Resources", "Support & Resources", false);

		// Verify that the ISI tray is displayed
		verifyReadButton();

		// Verify 'YESCARTA/TECARTUS Patient Wallet Card' PDF download
		handleFileDownload(new Common(siteName.toUpperCase() + " Patient Wallet Card").simpleLinkedText,
				"yescarta-tecartus-rems-patient-wallet-card-english-2024", "pdf", siteName);
	}

	/**
	 * Navigates and verifies all navigation menus in Yescarta/Tecartus.
	 *
	 * @return No return value
	 */
	public void accessNavigationMenu() {
		// make sure page is completely loaded
		refreshPageIfNotLoaded(CtrlTimeOut);
		if (objectExists(new Common("primaryNavigation").genericElementWithID, "isDisplayed", loadingWindowTimeOut,
				"Menu", "Button", "Yescarta.com", false)) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus(
					"Verify the main menu are visible"
							+ "1. Starting points; 2. About Tecartus ; 3. Support & Resources; 4. Caregiver Support",
					"All the menu should be visible and displayed", "All the menu are visible", true);
			// Verify that the ISI tray is displayed
			verifyReadButton();
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus(
					"Verify the main menu are visible"
							+ "1. Starting points; 2. About Tecartus ; 3. Support & Resources; 4. Caregiver Support",
					"All the menu should be visible and displayed", "All the menu are not displayed", true);
		}
	}

	/**
	 * public void tecartusHCPMenuValidation()
	 *
	 * @return
	 */
	public void tecartusHCPMenuValidation() {
		if (objectExists(new Common("message", "MCL").genericTextUsingAncestorClass, "isDisplayed",
				lngMinTimeOutInSeconds, "MCL", "Tab", "Tecartus HCP", true)) {
			handleFileDownload(new Common("message", "MCL").genericTextUsingAncestorClass, "tecartus-pi", "pdf",
					"TecartusHCP");
		}

		navigateToALLPage();
		compareURL("https://www.tecartushcp.com/car-t-cell-therapy/acute-lymphoblastic-leukemia", "All Indication");
	}

	/**
	 * public void verifyAdditionalEfficacyDataSite()
	 *
	 * @return
	 */
	public void verifyAdditionalEfficacyDataSite() {
		navigateToALLPage();
		if (objectExists(new Common("Efficacy").simpleLinkedText, "isDisplayed", lngMinTimeOutInSeconds, "Efficacy",
				"Menu", "Tecartus HCP", true)) {
			clickByJS(new Common("Efficacy").simpleLinkedText, lngMinTimeOutInSeconds, "Efficacy", "Menu",
					"Tecartus HCP", true);
			clickAndVerifyLink(new Common("Overall Survival Data").simpleLinkedText, "Overall Survival Data",
					"42% alive");
		}
		if (objectExists(new Common("Efficacy").simpleLinkedText, "isDisplayed", lngMinTimeOutInSeconds, "Efficacy",
				"Menu", "Tecartus HCP", true)) {
			clickByJS(new Common("Efficacy").simpleLinkedText, lngMinTimeOutInSeconds, "Efficacy", "Menu",
					"Tecartus HCP", true);
			clickAndVerifyLink(new Common("pageNav", "ZUMA-3").genericTextUsingAncestorID, "Zuma-3 Pivotal Trial",
					"ZUMA-3: the longest follow-up in an adult-only study of a CAR T-cell therapy for R/R B-cell precursor ALL");
		}
	}

	/**
	 * public void verifyLeukemiaResponseSite()
	 *
	 * @return
	 */
	public void verifyLeukemiaResponseSite() {
		navigateToALLPage();

		if (objectExists(new Common("Efficacy").simpleLinkedText, "isDisplayed", lngMinTimeOutInSeconds, "Efficacy",
				"Menu", "Tecartus HCP", true)) {
			clickByJS(new Common("Efficacy").simpleLinkedText, lngMinTimeOutInSeconds, "Efficacy", "Menu",
					"Tecartus HCP", true);
			clickAndVerifyLink(new Common("Response Rates").simpleLinkedText, "Response Rates", "Primary analysis");
			clickAndVerifyLink(new Common("purple-tabs-container", "OCR").genericTextUsingAncestorID, "OCR",
					"Primary analysis");
			clickAndVerifyLink(new Common("purple-tabs-container", "DOR").genericTextUsingAncestorID, "DOR",
					"~4-year analysis (median follow-up: 50.7 months)*: TECARTUS provided a median DOR of 13.7 months");
		}
	}

	/**
	 * Verifies if a specified element is visible on a given page.
	 *
	 * @param locator  Locator for the element.
	 * @param eleName  Name of the element for reporting purposes.
	 * @param eleType  Type of the element (e.g., button, link, text field).
	 * @param pageName Name of the page where the element is expected to be visible.
	 */
	public void verifyElementVisible(By locator, String eleName, String eleType, String pageName) {
		if (objectExists(locator, "isDisplayed", lngMinTimeOutInSeconds, eleName, eleType, pageName, false)) {
			pagescroll(locator, pageName);
			ALMFunctions.UpdateReportLogAndALMForPassStatus(
					"Verify " + eleName + " " + eleType + " is present on " + pageName,
					eleName + " " + eleType + " should be displayed", eleName + " " + eleType + " is visible", true);
		} else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus(
					"Verify " + eleName + " " + eleType + " is present on " + pageName,
					eleName + " " + eleType + " should be displayed", eleName + " " + eleType + " is not visible",
					true);
		}
	}

	/**
	 * public void verifyLeukemiaTreatmentSite()
	 *
	 * @return
	 */
	public void verifyLeukemiaTreatmentSite() {
		navigateToALLPage();

		if (objectExists(new Common("TECARTUS").simpleLinkedText, "isDisplayed", lngMinTimeOutInSeconds,
				"About TECARTUS", "Menu", "Tecartus HCP", true)) {
			clickByJS(new Common("TECARTUS").simpleLinkedText, lngMinTimeOutInSeconds, "About TECARTUS", "Menu",
					"Tecartus HCP", true);
			clickAndVerifyLink(new Common("Treatment Process").simpleLinkedText, "Treatment Proces",
					"Rapid and reliable manufacturing, with a 92% manufacturing success rate");
			clickAndVerifyLink(new Common("atc-button", "Find a Treatment Center").genericTextUsingAncestorClass,
					"Find a Treatment center", "Find a treatment center");
			clickAndVerifyLink(new Common("avera_mckennan_hospital").medicalCenterLoc, "AVERA MCKENNAN HOSPITAL",
					"General information");
		}
	}

	/**
	 * public void verifySafetyProfileSite()
	 *
	 * @return
	 */
	public void verifySafetyProfileSite() {
		navigateToALLPage();

		if (objectExists(new Common("Safety").simpleLinkedText, "isDisplayed", lngMinTimeOutInSeconds, "Safety", "Menu",
				"Tecartus HCP", true)) {
			clickByJS(new Common("Safety").simpleLinkedText, lngMinTimeOutInSeconds, "About TECARTUS", "Menu",
					"Tecartus HCP", true);
			clickAndVerifyLink(new Common("Overall Safety Profile").simpleLinkedText, "Overall Safety Profile",
					"Summary of adverse");
			clickAndVerifyLink(new Common("tab_1").genericElementWithID, "Adverse reactions", "Adverse reactions");
		}
	}

	/**
	 * public void validateTecartusHCPIndicatorChange()
	 *
	 * @return
	 */
	public void validateTecartusHCPIndicatorChange() {
		navigateToALLPage();

		if (objectExists(new Common("utilityButtons", "ALL Indication").genericTextUsingAncestorID, "isDisplayed",
				lngMinTimeOutInSeconds, "ALL Indication", "Menu", "Tecartus HCP", true)) {
			clickByJS(new Common("utilityButtons", "ALL Indication").genericTextUsingAncestorID, lngMinTimeOutInSeconds,
					"ALL Indication", "Menu", "Tecartus HCP", true);
			handleFileDownload(new Common("utilityButtons", "MCL Indication").genericTextUsingAncestorID, "tecartus-pi",
					"pdf", "Tecartus HCP");
		}
	}

	/**
	 * public void validateTecartusHCPWebForm()
	 *
	 * @return
	 */
	public void validateTecartusHCPWebForm() {
		navigateToALLPage();

		clickAndVerifyLink(new Common("utilityButtons", "Request a Rep").genericTextUsingAncestorID, "Request a Rep",
				"Have questions or need additional information about TECARTUS?");
		verifyElementVisible(new Common("NPI number").simpleGenericText, "NPI number", "Web Form", "Tecartus HCP");
		verifyElementVisible(new Common("IMPORTANT SAFETY INFORMATION").simpleGenericText, "ISI", "Tab",
				"Tecartus HCP");
	}

	/**
	 * public void verifyYescartaTecartusREMSSite()
	 *
	 * @return
	 */
	public void verifyYescartaTecartusREMSSite() {
		clickAndVerifyLink(new Common("trigger-0", "YESCARTA logo").imageWithAlternateTextUsingAncestorID,
				"YESCARTA Logo", "BOXED WARNING FOR YESCARTA");
		handleFileDownload(new Common("tab-0", "download").imageWithAlternateTextUsingAncestorID, "yescarta-pi", "pdf",
				"REMS");
		clickAndVerifyLink(new Common("trigger-1", "TECARTUS logo").imageWithAlternateTextUsingAncestorID,
				"TECARTUS logo", "BOXED WARNING FOR TECARTUS");
		handleFileDownload(new Common("tab-1", "download").imageWithAlternateTextUsingAncestorID, "tecartus-pi", "pdf",
				"REMS");
	}

}