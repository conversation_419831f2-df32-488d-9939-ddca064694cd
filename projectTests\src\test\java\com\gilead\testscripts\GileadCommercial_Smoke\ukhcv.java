package com.gilead.testscripts.GileadCommercial_Smoke;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "UKHCV_Smoke_Tests", testlab = "${testlab}", testset = "${testset}")

public class ukhcv extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void validateGileadAndEliminationMenu() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 3)
	public void validateGileadAndEliminationPage() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 4)
	public void validateHepCKi() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 5)
	public void validateWhatIsHepCKiSection() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 6)
	public void verifyHomePage() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 7)
	public void validateBeFreeOfHepCMenu() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 8)
	public void validateWhatIsHepCSection() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 9)
	public void verifyEliminationResources() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 10)
	public void validateEliminationPartnersMenu() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 11)
	public void validateEliminationPartnersPage() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 12)
	public void validateWhatIsHepCMenu() throws Exception {
		try {
			objCommonFunctions.clickSubMenuLoc();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 13)
	public void validateWhatIsHepCPage() throws Exception {
		try {
			objCommonFunctions.verifyComponentExists("VerifyComponentPresent");
		} finally {
			checkErrors();
		}
	}
}
