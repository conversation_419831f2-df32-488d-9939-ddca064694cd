package com.gilead.maintenance;

public class TestConfiguration {
	
	private String testScenario;
	private String testCase;
	private String testInstance;
	private String description;
	private String execute;
	private IterationOptions iterationMode;
	private int startIteration;
	private int endIteration;
	private String testCasePackage;
	private String remoteGrid;
	private String tempSuite;
	private String applitoolsValidation;
	
	public String getTestScenario() {
		return testScenario;
	}
	public void setTestScenario(String testScenario) {
		this.testScenario = testScenario;
	}
	public String getTestCase() {
		return testCase;
	}
	public void setTestCase(String testCase) {
		this.testCase = testCase;
	}
	public String getTestInstance() {
		return testInstance;
	}
	public void setTestInstance(String testInstance) {
		this.testInstance = testInstance;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getExecute() {
		return execute;
	}
	public void setExecute(String execute) {
		this.execute = execute;
	}
	public IterationOptions getIterationMode() {
		return iterationMode;
	}
	public void setIterationMode(IterationOptions iterationMode) {
		this.iterationMode = iterationMode;
	}
	public int getStartIteration() {
		return startIteration;
	}
	public void setStartIteration(int startIteration) {
		this.startIteration = startIteration;
	}
	public int getEndIteration() {
		return endIteration;
	}
	public void setEndIteration(int endIteration) {
		this.endIteration = endIteration;
	}
	public String getTestCasePackage() {
		return testCasePackage;
	}
	public void setTestCasePackage(String testCasePackage) {
		this.testCasePackage = testCasePackage;
	}
	public String getRemoteGrid() {
		return remoteGrid;
	}
	public void setRemoteGrid(String remoteGrid) {
		this.remoteGrid = remoteGrid;
	}
	public String getTempSuite() {
		return tempSuite;
	}
	public void setTempSuite(String tempSuite) {
		this.tempSuite = tempSuite;
	}
	/**
	 * @return the applitoolsValidation
	 */
	public String getApplitoolsValidation() {
		return applitoolsValidation;
	}
	/**
	 * @param applitoolsValidation the applitoolsValidation to set
	 */
	public void setApplitoolsValidation(String applitoolsValidation) {
		this.applitoolsValidation = applitoolsValidation;
	}
	

}
