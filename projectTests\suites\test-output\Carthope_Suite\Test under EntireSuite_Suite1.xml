<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="15" name="Test under EntireSuite_Suite1" tests="12" failures="1" timestamp="2025-05-20T17:46:59 IST" time="544.949" errors="0">
  <testcase name="invokeURL" time="77.845" classname="com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website"/>
  <testcase name="invokeURL" time="147.608" classname="com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website"/>
  <testcase name="invokeURL" time="140.013" classname="com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website"/>
  <testcase name="PDFVerificationForCarthopeWebsite" time="125.831" classname="com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website">
    <failure type="com.gilead.config.FrameworkAssertion" message="&amp;apos;1747743093785.pdf&amp;apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles">
      <![CDATA[com.gilead.config.FrameworkAssertion: '1747743093785.pdf' is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
at com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website.PDFVerificationForCarthopeWebsite(PDF_Download_Verification_for_Carthope_Website.java:32)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames]]>
    </failure>
  </testcase> <!-- PDFVerificationForCarthopeWebsite -->
  <testcase name="carthopeNavigationAndFunctionalityVerification" time="81.001" classname="com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website"/>
  <testcase name="invokeURL" time="77.747" classname="com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site"/>
  <testcase name="invokeURL" time="84.278" classname="com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site"/>
  <testcase name="carthopeNaviationVerification" time="228.461" classname="com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website"/>
  <testcase name="toVerifyCarTConsultationSite" time="111.058" classname="com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site"/>
  <testcase name="invokeURL" time="50.15" classname="com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site"/>
  <testcase name="toVerifyPatientEligibiltySite" time="113.857" classname="com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site"/>
  <testcase name="toVerifySafetySite" time="81.307" classname="com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site"/>
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
