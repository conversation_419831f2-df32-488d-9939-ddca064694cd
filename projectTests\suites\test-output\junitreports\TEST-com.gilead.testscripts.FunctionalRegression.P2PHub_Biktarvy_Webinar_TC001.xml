<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.FunctionalRegression.P2PHub_Biktarvy_Webinar_TC001" tests="2" failures="0" timestamp="2025-05-20T18:41:38 IST" time="28.646" errors="2">
  <testcase name="invokeURL" time="14.170" classname="com.gilead.testscripts.FunctionalRegression.P2PHub_Biktarvy_Webinar_TC001">
    <error type="com.gilead.config.FrameworkException" message="The specified sheet &quot;SmokeCICD&quot;does not exist within the workbook &quot;Thread14FunctionalRegression_P2PHub_Biktarvy_Webinar_TC001_Instance1.xlsx&quot;">
      <![CDATA[com.gilead.config.FrameworkException: The specified sheet "SmokeCICD"does not exist within the workbook "Thread14FunctionalRegression_P2PHub_Biktarvy_Webinar_TC001_Instance1.xlsx"
at com.gilead.maintenance.ExcelDataAccess.getWorkSheet(ExcelDataAccess.java:139)
at com.gilead.maintenance.ExcelDataAccess.getRowNum(ExcelDataAccess.java:163)
at com.gilead.maintenance.CraftDataTable.getData(CraftDataTable.java:120)
at businesscomponents.CommonFunctions.preCondition(CommonFunctions.java:591)
at businesscomponents.CommonFunctions.launchApplication(CommonFunctions.java:862)
at com.gilead.testscripts.FunctionalRegression.P2PHub_Biktarvy_Webinar_TC001.invokeURL(P2PHub_Biktarvy_Webinar_TC001.java:21)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </error>
  </testcase> <!-- invokeURL -->
  <system-out/>
  <testcase name="verifySubmenuInWebPage" time="14.476" classname="com.gilead.testscripts.FunctionalRegression.P2PHub_Biktarvy_Webinar_TC001">
    <error type="org.openqa.selenium.StaleElementReferenceException" message="stale element reference: stale element not found
  (Session info: chrome=133.0.6943.99)
For documentation on this error, please visit: https://selenium.dev/exceptions/#stale_element_reference
Build info: version: &#039;4.1.2&#039;, revision: &#039;9a5a329c5a&#039;
System info: host: &#039;Z1VD7STHPRDN255&#039;, ip: &#039;************&#039;, os.name: &#039;Windows 10&#039;, os.arch: &#039;amd64&#039;, os.version: &#039;10.0&#039;, java.version: &#039;1.8.0_291&#039;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [0e3d528fd4b0e493c570391dc19a3489, getElementAttribute {id=f.3BA415C37FDF59CEB76ADA74BA1A6C96.d.2C6BA90D6231A8B315AD89C95336ACE2.e.87, name=href}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:52431}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:52431/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[ChromeDriver: chrome on WINDOWS (0e3d528fd4b0e493c570391dc19a3489)] -&gt; xpath: //div[@class=&#039;card-footer&#039;]//a]
Session ID: 0e3d528fd4b0e493c570391dc19a3489">
      <![CDATA[org.openqa.selenium.StaleElementReferenceException: stale element reference: stale element not found
  (Session info: chrome=133.0.6943.99)
For documentation on this error, please visit: https://selenium.dev/exceptions/#stale_element_reference
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [0e3d528fd4b0e493c570391dc19a3489, getElementAttribute {id=f.3BA415C37FDF59CEB76ADA74BA1A6C96.d.2C6BA90D6231A8B315AD89C95336ACE2.e.87, name=href}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:52431}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:52431/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[ChromeDriver: chrome on WINDOWS (0e3d528fd4b0e493c570391dc19a3489)] -> xpath: //div[@class='card-footer']//a]
Session ID: 0e3d528fd4b0e493c570391dc19a3489
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:251)
at org.openqa.selenium.remote.RemoteWebElement.getAttribute(RemoteWebElement.java:152)
at businesscomponents.CommonFunctions.checkRegisterLinks(CommonFunctions.java:4061)
at com.gilead.testscripts.FunctionalRegression.P2PHub_Biktarvy_Webinar_TC001.verifySubmenuInWebPage(P2PHub_Biktarvy_Webinar_TC001.java:31)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </error>
  </testcase> <!-- verifySubmenuInWebPage -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.FunctionalRegression.P2PHub_Biktarvy_Webinar_TC001 -->
