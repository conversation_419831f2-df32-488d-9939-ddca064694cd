id: naming_convention_string
message: "Ensure all string variable naming should starts with 'str'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: type_identifier
        regex: '^String$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^str'
---
id: naming_convention_linkedlist
message: "Ensure all LinkedList variable naming should starts with 'lkdLst'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^LinkedList$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^lkdLst"
---
id: naming_convention_array
message: "Ensure all ArrayList variable naming should starts with 'arr'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^ArrayList$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^arr"
---
id: naming_convention_list
message: "Ensure all List variable naming should starts with 'lst'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^List$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^lst"
---
id: naming_convention_hashmap
message: "Ensure all HashMap variable naming should starts with 'hm'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^HashMap$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^hm"
---
id: naming_convention_map
message: "Ensure all Map variable naming should starts with 'map'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^Map$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^map"
---
id: naming_convention_linkedhashmap
message: "Ensure all LinkedHashMap variable naming should starts with 'lkdhm'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^LinkedHashMap$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^lkdhm"
---
id: naming_convention_treemap
message: "Ensure all TreeMap variable naming should starts with 'treeMap'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^TreeMap$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^treeMap"
---
id: naming_convention_set
message: "Ensure all Set variable naming should starts with 'set'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^Set$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^set"
---
id: naming_convention_hashset
message: "Ensure all Set variable naming should starts with 'hset'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^HashSet$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^hset"
---
id: naming_convention_linkedhashset
message: "Ensure all LinkedHashSet variable naming should starts with 'lkdhset'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: generic_type
        has:
          stopBy: end
          kind: type_identifier
          regex: '^LinkedHashSet$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: "^lkdhset"