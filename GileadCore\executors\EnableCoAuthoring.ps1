﻿param (
    [string]$arg1,
    [string]$arg2,
	[string]$arg3,
	[string]$arg4
)
# Define variables
$siteUrl = $arg1
$libraryName = $arg2
$fileName = $arg3
$destinationPath = $arg4

# Connect to SharePoint
Connect-PnPOnline -Url $siteUrl -UseWebLogin

# Download the file
Get-PnPFile -Url "/$libraryName/$fileName" -Path $destinationPath -FileName $fileName -AsFile -Force

Disconnect-PnPOnline

$result = Join-Path -Path $arg4 -ChildPath $arg3

if (Test-Path $result) {
	Write-Output "The path exists: $fullPath"
    exit 0  # Exit with code 0 (success)
} else {
	Write-Output "The path exists1: $fullPath"
    exit 1  # Exit with code 1 (failure)
}