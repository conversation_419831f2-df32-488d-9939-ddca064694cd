<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.FunctionalRegression.Mysupportpath_HCP_Main_page_TC001" tests="2" failures="1" timestamp="2025-05-20T18:41:38 IST" time="8.875" errors="1">
  <testcase name="invokeURL" time="5.371" classname="com.gilead.testscripts.FunctionalRegression.Mysupportpath_HCP_Main_page_TC001">
    <error type="com.gilead.config.FrameworkException" message="The specified sheet &quot;SmokeCICD&quot;does not exist within the workbook &quot;Thread13FunctionalRegression_Mysupportpath_HCP_Main_page_TC001_Instance1.xlsx&quot;">
      <![CDATA[com.gilead.config.FrameworkException: The specified sheet "SmokeCICD"does not exist within the workbook "Thread13FunctionalRegression_Mysupportpath_HCP_Main_page_TC001_Instance1.xlsx"
at com.gilead.maintenance.ExcelDataAccess.getWorkSheet(ExcelDataAccess.java:139)
at com.gilead.maintenance.ExcelDataAccess.getRowNum(ExcelDataAccess.java:163)
at com.gilead.maintenance.CraftDataTable.getData(CraftDataTable.java:120)
at businesscomponents.CommonFunctions.preCondition(CommonFunctions.java:591)
at businesscomponents.CommonFunctions.launchApplication(CommonFunctions.java:862)
at com.gilead.testscripts.FunctionalRegression.Mysupportpath_HCP_Main_page_TC001.invokeURL(Mysupportpath_HCP_Main_page_TC001.java:20)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </error>
  </testcase> <!-- invokeURL -->
  <system-out/>
  <testcase name="verifyCriticalComponents" time="3.504" classname="com.gilead.testscripts.FunctionalRegression.Mysupportpath_HCP_Main_page_TC001">
    <failure type="com.gilead.config.FrameworkAssertion" message="Below exception is thrown while trying to click &quot;Link &quot;&lt;br&gt;&lt;br&gt;element click intercepted: Element &lt;a class=&quot;in-page-scroll-trigger&quot; href=&quot;#co-pay&quot; target=&quot;&quot;&gt;...&lt;/a&gt; is not clickable at point (733, 134). Other element would receive the click: &lt;div class=&quot;gl-cmp-modal modal fade hcp-modal show&quot; data-bs-backdrop=&quot;static&quot; data-bs-keyboard=&quot;false&quot; tabindex=&quot;-1&quot; role=&quot;dialog&quot; aria-modal=&quot;true&quot; style=&quot;display: block;&quot;&gt;...&lt;/div&gt;
  (Session info: chrome=133.0.6943.99)
Build info: version: &#039;4.1.2&#039;, revision: &#039;9a5a329c5a&#039;
System info: host: &#039;Z1VD7STHPRDN255&#039;, ip: &#039;************&#039;, os.name: &#039;Windows 10&#039;, os.arch: &#039;amd64&#039;, os.version: &#039;10.0&#039;, java.version: &#039;1.8.0_291&#039;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [fc08fc52ee92f9f832d21e6a577d84f9, clickElement {id=f.E536B38FCDAD9C2029DCC7BAA1E3A2AB.d.BFBD04A962EDC81F772C785A0A99D6DE.e.44}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:52370}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:52370/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[ChromeDriver: chrome on WINDOWS (fc08fc52ee92f9f832d21e6a577d84f9)] -&gt; xpath: //a[@class=&#039;in-page-scroll-trigger&#039;][contains(text(),&#039;Co-pay&#039;)]]
Session ID: fc08fc52ee92f9f832d21e6a577d84f9">
      <![CDATA[com.gilead.config.FrameworkAssertion: Below exception is thrown while trying to click "Link "<br><br>element click intercepted: Element <a class="in-page-scroll-trigger" href="#co-pay" target="">...</a> is not clickable at point (733, 134). Other element would receive the click: <div class="gl-cmp-modal modal fade hcp-modal show" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" role="dialog" aria-modal="true" style="display: block;">...</div>
  (Session info: chrome=133.0.6943.99)
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [fc08fc52ee92f9f832d21e6a577d84f9, clickElement {id=f.E536B38FCDAD9C2029DCC7BAA1E3A2AB.d.BFBD04A962EDC81F772C785A0A99D6DE.e.44}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:52370}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:52370/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Element: [[ChromeDriver: chrome on WINDOWS (fc08fc52ee92f9f832d21e6a577d84f9)] -> xpath: //a[@class='in-page-scroll-trigger'][contains(text(),'Co-pay')]]
Session ID: fc08fc52ee92f9f832d21e6a577d84f9
at com.gilead.reports.Report.updateTestLog(Report.java:423)
at com.gilead.maintenance.ALMFunctions.ThrowException(ALMFunctions.java:252)
at com.gilead.maintenance.CommonActionsAndFunctions.click(CommonActionsAndFunctions.java:436)
at businesscomponents.CommonFunctions.clickLinks(CommonFunctions.java:896)
at businesscomponents.CommonFunctions.verifyLinksInWebPage(CommonFunctions.java:882)
at com.gilead.testscripts.FunctionalRegression.Mysupportpath_HCP_Main_page_TC001.verifyCriticalComponents(Mysupportpath_HCP_Main_page_TC001.java:29)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- verifyCriticalComponents -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.FunctionalRegression.Mysupportpath_HCP_Main_page_TC001 -->
