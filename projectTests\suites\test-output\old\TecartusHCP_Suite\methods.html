<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>TecartusHCP_Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:12:37</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/23 18:08:27</td>   <td>-249678</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Navigation_and_Functionality_Verification_for_TecartusHCP_Website.accessNavigationMenuOnYescartaHCP()[pri:2, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">accessNavigationMenuOnYescartaHCP</td> 
  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:08:00</td>   <td>-276783</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:08:27</td>   <td>-249678</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/23 18:12:01</td>   <td>-35921</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Treatment_Site.verifyLukemiaTreatmentSite()[pri:2, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">verifyLukemiaTreatmentSite</td> 
  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:05</td>   <td>-152226</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:11:43</td>   <td>-53772</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:13:29</td>   <td>51559</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@2005373414</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/23 18:08:20</td>   <td>-257229</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Navigation_and_Functionality_Verification_for_TecartusHCP_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">invokeURL</td> 
  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:08:46</td>   <td>-231126</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:04</td>   <td>-153327</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@2005373414</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/23 18:09:35</td>   <td>-182451</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Search_Functionality_Verification_For_TecartusHCP_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">invokeURL</td> 
  <td>Thread3@2005373414</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:13:29</td>   <td>52127</td> <td title="&lt;&lt;CRAFTLiteTestCase.tearDownTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&lt;&lt;tearDownTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@2061543916</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:09:41</td>   <td>-176418</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@2005373414</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/23 18:11:02</td>   <td>-94557</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Safety_Profile_Site.invokeURL()[pri:1, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">invokeURL</td> 
  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:09:02</td>   <td>-215002</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/23 18:11:49</td>   <td>-48187</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Treatment_Site.invokeURL()[pri:1, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">invokeURL</td> 
  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:57</td>   <td>-100167</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:11:41</td>   <td>-55496</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:08:27</td>   <td>-249678</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:12:01</td>   <td>-35921</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/23 18:10:15</td>   <td>-141546</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="API_Integration_And_Navigation_Verification_For_TecartusHCP_Website.tecartusHCPNavigationAndAPI()[pri:2, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">tecartusHCPNavigationAndAPI</td> 
  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:56</td>   <td>-101375</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:11:09</td>   <td>-87831</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:15</td>   <td>-141546</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:13:29</td>   <td>51587</td> <td title="&lt;&lt;BaseTest.afterSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&lt;&lt;afterSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@2061543916</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:08:00</td>   <td>-277053</td> <td title="&gt;&gt;CRAFTLiteTestCase.setUpTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&gt;&gt;setUpTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@2061543916</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/23 18:09:41</td>   <td>-176417</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Search_Functionality_Verification_For_TecartusHCP_Website.tecartuHCPSearchFuntionalityVerification()[pri:2, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">tecartuHCPSearchFuntionalityVerification</td> 
  <td>Thread3@2005373414</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:12:38</td>   <td>1438</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:09:29</td>   <td>-188231</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:57</td>   <td>-100164</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:11:41</td>   <td>-55496</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/23 18:09:02</td>   <td>-215002</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Additional_Efficacy_Data_Site.verifyAdditionalEfficacyDataSite()[pri:2, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">verifyAdditionalEfficacyDataSite</td> 
  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:12:38</td>   <td>1439</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:13:27</td>   <td>50458</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:09:02</td>   <td>-215002</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:13:01</td>   <td>24058</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread7@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:11:43</td>   <td>-53778</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:56</td>   <td>-101375</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:09:29</td>   <td>-188238</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:13:27</td>   <td>50458</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:08:46</td>   <td>-231125</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:08:00</td>   <td>-276783</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@2005373414</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:10:57</td>   <td>-100164</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/23 18:12:48</td>   <td>11143</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Response_Site.invokeURL()[pri:1, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">invokeURL</td> 
  <td>Thread7@2005373414</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/23 18:11:09</td>   <td>-87831</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Safety_Profile_Site.verifySafetyProfileSite()[pri:2, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">verifySafetyProfileSite</td> 
  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/23 18:08:56</td>   <td>-220830</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Additional_Efficacy_Data_Site.invokeURL()[pri:1, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">invokeURL</td> 
  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:12:01</td>   <td>-35921</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:12:38</td>   <td>1434</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:09:29</td>   <td>-188231</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:12:37</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:08:45</td>   <td>-232401</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:08:46</td>   <td>-231138</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:05</td>   <td>-152238</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:08:45</td>   <td>-232401</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:09:41</td>   <td>-176418</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:09:27</td>   <td>-189749</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:11:43</td>   <td>-53772</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/23 18:13:01</td>   <td>24058</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Response_Site.verifyLukemiaResponseSite()[pri:2, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">verifyLukemiaResponseSite</td> 
  <td>Thread7@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:09:27</td>   <td>-189749</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@2005373414</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/23 18:10:10</td>   <td>-147073</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="API_Integration_And_Navigation_Verification_For_TecartusHCP_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">invokeURL</td> 
  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:15</td>   <td>-141546</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:13:01</td>   <td>24058</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread7@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:11:09</td>   <td>-87831</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@2005373414</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/04/23 18:10:04</td>   <td>-153327</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@2005373414</td>   <td></td> </tr>
<tr bgcolor="8d92a7">  <td>25/04/23 18:10:05</td>   <td>-152227</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@2005373414</td>   <td></td> </tr>
</table>
