<html><head><title>testng.xml for YescartaHCP_Suite</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;guice-stage="DEVELOPMENT"&nbsp;name="YescartaHCP_Suite"&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="2"&nbsp;name="Test&nbsp;under&nbsp;EntireSuite_Suite1"&nbsp;parallel="classes"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;parameter&nbsp;name="RunID"&nbsp;value="0"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_Headers_On_YescartaHCP"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_Footer_On_YescartaHCP"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_Body_On_YescartaHCP_HomePage"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy_HomePage"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy2L_Page"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy3L_Page"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_Safety_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_PatientID_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.YescartaHCP.To_Verify_Manufacturing_And_Process_page"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;Test&nbsp;under&nbsp;EntireSuite_Suite1&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;YescartaHCP_Suite&nbsp;--&gt;
<br/></tt></body></html>