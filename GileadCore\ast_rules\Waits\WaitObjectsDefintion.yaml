id: WaitObject_objectExists
message: "Wait objects value should be defined in properties file"
language: java
severity: warning
rule:
  pattern: objectExists($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 3
      kind: identifier
      pattern: $PAT
      inside:
        stopBy: end
        kind: program
        has:
          stopBy: end
          kind: variable_declarator
          all:
            - has: 
                stopBy: end
                kind: identifier
                pattern: $PAT
            - not: 
                has: 
                  stopBy: end
                  kind: argument_list
                  has: 
                    stopBy: end
                    kind: method_invocation
                    regex: '^properties.getProperty'
