package com.gilead.reports;

import java.util.Map;

import org.openqa.selenium.WebDriver;

import com.applitools.eyes.selenium.Eyes;
import com.aventstack.extentreports.ExtentTest;
import com.gilead.maintenance.ALMFunctions;
import com.gilead.maintenance.CraftDataTable;
import com.gilead.maintenance.DriverManager;
import com.gilead.maintenance.WebDriverUtil;

/**
 * Wrapper class for common framework objects, to be used across the entire test
 * case and dependent libraries
 * 
 * <AUTHOR>
 */
public class ScriptHelper {

	private final CraftDataTable dataTable;
	private final SeleniumReport report;
	private CraftDriver craftDriver;
	private WebDriverUtil driverUtil;
	private APIReusuableLibrary apiDriver;
	private ExtentTest extentTest;
	private Map<String, String> reusableHandle;
	private final ALMFunctions ALMFunctions;
	private final DriverManager driverManager;
	private int currentIteration;
	private int currentSubIteration;
	private Eyes eyes;

	/**
	 * Constructor to initialize all the objects wrapped by the
	 * {@link ScriptHelper} class
	 * 
	 * @param dataTable
	 *            The {@link CraftDataTable} object
	 * @param report
	 *            The {@link SeleniumReport} object
	 * @param driver
	 *            The {@link WebDriver} object
	 * @param driverUtil
	 *            The {@link WebDriverUtil} object
	 * @param reusableHandle
	 * @param extentTest
	 * @param apiDriver
	 */

	public ScriptHelper(CraftDataTable dataTable, SeleniumReport report, CraftDriver craftDriver, DriverManager driverManager,
			WebDriverUtil driverUtil, ALMFunctions functions, APIReusuableLibrary apiDriver, ExtentTest extentTest,
			Map<String, String> reusableHandle, Eyes eyes) {
		this.dataTable = dataTable;
		this.report = report;
		this.craftDriver = craftDriver;
		this.driverUtil = driverUtil;
		this.apiDriver = apiDriver;
		this.extentTest = extentTest;
		this.ALMFunctions = functions;
		this.reusableHandle = reusableHandle;
		this.driverManager = driverManager;
		this.eyes = eyes;
	}

	/**
	 * Function to get the {@link CraftDataTable} object
	 * 
	 * @return The {@link CraftDataTable} object
	 */
	public CraftDataTable getDataTable() {
		return dataTable;
	}

	/**
	 * Function to get the {@link SeleniumReport} object
	 * 
	 * @return The {@link SeleniumReport} object
	 */
	public SeleniumReport getReport() {
		return report;
	}

	/**
	 * Function to get the {@link CraftDriver} object
	 * 
	 * @return The {@link CraftDriver} object
	 */
	public CraftDriver getcraftDriver() {
		return craftDriver;
	}

	/**
	 * Function to get the {@link WebDriverUtil} object
	 * 
	 * @return The {@link WebDriverUtil} object
	 */
	public WebDriverUtil getDriverUtil() {
		return driverUtil;
	}
	
	public ALMFunctions getALMFunctions()
	{
		return ALMFunctions;
	}

	/**
	 * Function to get the {@link APIReusuableLibrary} object
	 * 
	 * @return The {@link APIReusuableLibrary} object
	 */
	public APIReusuableLibrary getApiDriver() {
		return apiDriver;
	}

	/**
	 * Function to get the {@link ExtentTest} object
	 * 
	 * @return The {@link ExtentTest} object
	 */
	public ExtentTest getExtentTest() {
		return extentTest;
	}

	public Map<String, String> getReusablehandle() {
		return reusableHandle;
	}

	public int getCurrentIteration() {
		return currentIteration;
	}

	public void setCurrentIteration(int currentIteration) {
		this.currentIteration = currentIteration;
	}

	public int getCurrentSubIteration() {
		return currentSubIteration;
	}
	
	public DriverManager getDriverManager()
	{
		return driverManager;
	}

	public void setCurrentSubIteration(int currentSubIteration) {
		this.currentSubIteration = currentSubIteration;
	}

	/**
	 * @return the eyes
	 */
	public Eyes getEyes() {
		return eyes;
	}

	/**
	 * @param eyes the eyes to set
	 */
	public void setEyes(Eyes eyes) {
		this.eyes = eyes;
	}
	
	

}