<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>framework</groupId>
		<artifactId>gileadTestNG</artifactId>
		<version>1.0</version>
	</parent>

	<groupId>gilead.apps</groupId>
	<artifactId>gileadCore</artifactId>
	<version>0.0.1-SNAPSHOT</version>


	<properties>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<selenium.version>4.1.2</selenium.version>
		<testng.version>7.5.1</testng.version>
		<aspectj.version>1.8.10</aspectj.version>
		<java.client.appium>8.0.0</java.client.appium>
		<alm-connector.jar>${project.basedir}/libs/alm-connector.jar</alm-connector.jar>
		<decrypter.jar>${project.basedir}/libs/Password-Decrypter.jar</decrypter.jar>
		<testClass>default</testClass>
		<testPackage>default</testPackage>
		<testSuite>default</testSuite>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.gilead.apps</groupId>
			<artifactId>alm-connector</artifactId>
			<version>1.0</version>
		</dependency>
		
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-java</artifactId>
			<version>${selenium.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.6.2</version>
		</dependency>

		<!-- TestNG Dependency -->
		<dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>${testng.version}</version>
		</dependency>


		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.17</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.17</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>3.17</version>
		</dependency>

		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-core</artifactId>
			<version>1.3</version>
		</dependency>

		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-library</artifactId>
			<version>1.3</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/org.json/json -->
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20210307</version>
		</dependency>
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.7.0</version>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/io.github.bonigarcia/webdrivermanager -->
		<dependency>
			<groupId>io.github.bonigarcia</groupId>
			<artifactId>webdrivermanager</artifactId>
			<version>5.8.0</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-core -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.18.0</version>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-api -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.18.0</version>
		</dependency>
		<dependency>
			<groupId>com.aventstack</groupId>
			<artifactId>extentreports</artifactId>
			<version>3.1.1</version>
		</dependency>

		<dependency>
			<groupId>io.appium</groupId>
			<artifactId>java-client</artifactId>
			<version>${java.client.appium}</version>
		</dependency>
		<dependency>
			<groupId>io.rest-assured</groupId>
			<artifactId>rest-assured</artifactId>
			<version>3.0.7</version>
		</dependency>
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itext7-core</artifactId>
			<version>7.1.9</version>
			<type>pom</type>
		</dependency>

		<!-- iText pdfHTML add-on -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>html2pdf</artifactId>
			<version>2.1.6</version>
		</dependency>
		<dependency>
			<groupId>com.applitools</groupId>
			<artifactId>eyes-selenium-java5</artifactId>
			<version>5.39.0</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/org.glassfish.jersey.core/jersey-client -->
		<dependency>
			<groupId>org.glassfish.jersey.core</groupId>
			<artifactId>jersey-client</artifactId>
			<version>2.39.1</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/org.glassfish.jersey.containers/jersey-container-grizzly2-http -->
		<dependency>
			<groupId>org.glassfish.jersey.containers</groupId>
			<artifactId>jersey-container-grizzly2-http</artifactId>
			<version>2.39.1</version>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.connectors</groupId>
			<artifactId>jersey-apache-connector</artifactId>
			<version>2.39.1</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/org.glassfish.jersey.media/jersey-media-multipart -->
		<dependency>
			<groupId>org.glassfish.jersey.media</groupId>
			<artifactId>jersey-media-multipart</artifactId>
			<version>2.39.1</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/org.glassfish.jersey.media/jersey-media-jaxb -->
		<dependency>
			<groupId>org.glassfish.jersey.media</groupId>
			<artifactId>jersey-media-jaxb</artifactId>
			<version>2.39.1</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/org.glassfish.jersey.media/jersey-media-json-jackson -->
		<dependency>
			<groupId>org.glassfish.jersey.media</groupId>
			<artifactId>jersey-media-json-jackson</artifactId>
			<version>2.39.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/javax.xml.bind/jaxb-api -->
		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>2.3.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.commons/commons-text -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.10.0</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.15.0</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.15.0</version>
		</dependency>

		<!--
		https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-annotations -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.15.0</version>
		</dependency>

	</dependencies>

	<build>
		<finalName>gilead-ng-core</finalName>
		<testResources>
			<testResource>
				<directory>${project.basedir}/src/test/resources</directory>
			</testResource>
		</testResources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-install-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>intall-alm-connector-jar</id>
						<phase>validate</phase>
						<goals>
							<goal>install-file</goal>
						</goals>
						<configuration>
							<file>${alm-connector.jar}</file>
							<groupId>com.gilead.apps</groupId>
							<artifactId>alm-connector</artifactId>
							<version>1.0</version>
							<packaging>jar</packaging>
						</configuration>
					</execution>
					<execution>
						<id>install-decrypter-jar</id>
						<phase>validate</phase>
						<goals>
							<goal>install-file</goal>
						</goals>
						<configuration>
							<file>${decrypter.jar}</file>
							<groupId>com.gilead.apps</groupId>
							<artifactId>decrypter</artifactId>
							<version>1.0</version>
							<packaging>jar</packaging>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/libs</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.2.0</version>
				<configuration>
					<excludes>
						<exclude>**/logs/**</exclude>
					</excludes>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<mainClass>init.ALMRunner</mainClass>
							<classpathPrefix>.</classpathPrefix>
							<classpathLayoutType>repository</classpathLayoutType>
						</manifest>
					</archive>
				</configuration>
				<executions>
					<execution>
						<phase>prepare-package</phase>
						<goals>
							<goal>test-jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>gilead-ng-core-execution</id>
						<goals>
							<goal>exec</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<executable>java</executable>
					<arguments>
						<argument>-classpath</argument>
						<classpath />
						<argument>init.ALMRunner</argument>
						<argument>${testSuite}</argument>
						<argument>${testClass}</argument>
						<argument>${testPackage}</argument>
					</arguments>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>