package com.gilead.maintenance;

import java.io.FileInputStream;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.Alert;
import org.openqa.selenium.By;
import org.openqa.selenium.ElementNotSelectableException;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Keys;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.SkipException;

import com.gilead.base.DriverScript;
import com.gilead.base.RunContext;
import com.gilead.reports.ReusableLibrary;
import com.gilead.reports.ScriptHelper;
import com.gilead.reports.Status;
import com.gilead.utils.ServiceRegister;

@SuppressWarnings({"unused","deprecation"})
public class CommonActionsAndFunctions extends ReusableLibrary {
	private final long lngPagetimeOutInSeconds = Long.parseLong(properties.getProperty("PageLoadTimeout"));
	private final long lngMinTimeOutInSeconds = Long.parseLong(properties.getProperty("MinObjectSyncTimeout"));
	protected DriverScript driverScript;
	protected Map<String, Integer> iterations = new HashMap<>();
	private static final Logger logger = LogManager.getLogger("loggers");
	//private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());

	public CommonActionsAndFunctions(ScriptHelper scriptHelper) {
		super(scriptHelper);
	}

	public CommonActionsAndFunctions(RunContext runContext) {
		super(runContext);
	}

	/**
	 * Function to input data in text box
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 * @param Text             Expected text to be passed to the input field
	 * @param elementName      The name of the element in which the expected text to
	 *                         be entered
	 * @param pageName         Page in which title is to be compared with expected
	 *                         title @
	 * @param blnlogStatus     boolean to indicate whether report log is to be added
	 *                         or not
	 */
	public void sendkeys(By locator, long timeOutInSeconds, String Text, String elementName, String elementType,
			String pageName, boolean blnlogStatus) {
		if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, elementType, pageName, true)) {
			if (driverUtil.waitUntilElementVisible(locator, timeOutInSeconds, elementName, elementType, pageName,
					true)) {
				if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, elementName, elementType, pageName)) {
					try {
						WebElement e = driver.findElement(locator);
						e.sendKeys(Text);
						if (blnlogStatus) {
							logger.info(String.format("Entered '%s' in %s of %s using %s", Text, elementName, pageName,
									locator));
							report.updateTestLog("Enter the " + elementName + " " + elementType,
									elementName + " " + elementType + " should be entered by the user is: " + "\""
											+ Text + "\"" + " in the page " + "\"" + pageName + "\"",
									elementName + " " + elementType + " entered by the user is: " + "\"" + Text + "\""
											+ " in the page " + "\"" + pageName + "\"",
									Status.DONE);
						}
					} catch (Exception e) {
						ALMFunctions.ThrowException("Error - " + elementName + " " + elementType,
								"User should be able to enter text " + "\"" + "*********" + "\"" + " in " + "\""
										+ elementName + "\"" + " in the page " + "\"" + pageName + "\"",
								"Below exception is thrown while trying to enter text in field " + "\"" + elementName
										+ "\"" + "<br><br>" + e.getLocalizedMessage(),
								true);
					}
				} else {
					logger.error(String.format("Element not enabled, not able to enter '%s' in %s of %s using %s", Text,
							elementName, pageName, locator));
				} // ENABLED
			} else {
				logger.error(String.format("Element not visible, not able to enter '%s' in %s of %s using %s", Text,
						elementName, pageName, locator));
			} // VISIBLE
		} else {
			logger.error(String.format("Element not located, not able to enter '%s' in %s of %s using %s", Text,
					elementName, pageName, locator));
		} // LOCATED
	}

	/**
	 * Function to input data in text box
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 * @param Text             Expected text to be passed to the input field
	 * @param elementName      The name of the element in which the expected text to
	 *                         be entered
	 * @param pageName         Page in which title is to be compared with expected
	 *                         title @
	 * @param blnlogStatus     boolean to indicate whether report log is to be added
	 *                         or not
	 */
	public void sendkeys(WebElement locator, long timeOutInSeconds, String Text, String elementName, String pageName,
			boolean blnlogStatus) {

		if (driverUtil.waitUntilElementVisible(locator, timeOutInSeconds, elementName, "input field", pageName)) {
			if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, elementName, "input field", pageName)) {
				try {
					locator.sendKeys(Text);
					if (blnlogStatus) {
						logger.info(String.format("Entered '%s' in %s of %s using %s", Text, elementName, pageName,
								locator));
						report.updateTestLog(elementName,
								elementName + " should be entered by the user is: " + "\"" + Text + "\""
										+ " in the page " + "\"" + pageName + "\"",
								elementName + " entered by the user is: " + "\"" + Text + "\"" + " in the page " + "\""
										+ pageName + "\"",
								Status.DONE);
					}
				} catch (Exception e) {
					logger.error(String.format("Not able to Enter '%s' in %s of %s using %s", Text, elementName,
							pageName, locator));
					ALMFunctions.ThrowException(elementName,
							"User should be able to enter text " + "\"" + Text + "\"" + " in " + "\"" + elementName
									+ "\"" + " in the page " + "\"" + pageName + "\"",
							"Below exception is thrown while trying to enter text in field " + "\"" + elementName + "\""
									+ "<br><br>" + e.getLocalizedMessage(),
							true);

				}
			} else {
				logger.error(String.format("Element not enabled, not able to enter '%s' in %s of %s using %s", Text,
						elementName, pageName, locator));
			} // ENABLED
		} else {
			logger.error(String.format("Element not visible, not able to enter '%s' in %s of %s using %s", Text,
					elementName, pageName, locator));
		} // VISIBLE
	}

	public void sendkeys(WebElement locator, long timeOutInSeconds, Keys keyStroke, String elementName, String pageName,
			boolean blnlog) {
		if (driverUtil.waitUntilElementVisible(locator, timeOutInSeconds, elementName, "input field", pageName)) {
			if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, elementName, "input field", pageName)) {
				try {
					locator.sendKeys(keyStroke);
					logger.info(String.format("Entered '%s' in %s of %s using %s", keyStroke.toString(), elementName,
							pageName, locator));
					if (blnlog)
						report.updateTestLog(elementName,
								elementName + " should be entered by the user is: " + "\"" + keyStroke + "\""
										+ " in the page " + "\"" + pageName + "\"",
								elementName + " entered by the user is: " + "\"" + keyStroke + "\"" + " in the page "
										+ "\"" + pageName + "\"",
								Status.DONE);
				} catch (Exception e) {
					logger.error(String.format("Not able to enter '%s' in %s of %s using %s", keyStroke.toString(),
							elementName, pageName, locator));
					ALMFunctions.ThrowException(elementName,
							"User should be able to provide keystroke in " + "\"" + elementName + "\"" + " in the page "
									+ "\"" + pageName + "\"",
							"Below exception is thrown while trying to provide keystroke in field " + "\"" + elementName
									+ "\"" + "<br><br>" + e.getLocalizedMessage(),
							true);
				}
			} else {
				logger.error(String.format("Element not enabled, not able to enter '%s' in %s of %s using %s",
						keyStroke, elementName, pageName, locator));
			} // ENABLED
		} else {
			logger.error(String.format("Element not visible, not able to enter '%s' in %s of %s using %s", keyStroke,
					elementName, pageName, locator));
		} // VISIBLE
	}

	public String getValue(By locator, long lngTimeOutInSeconds, String strValue, String strPageName) {
		if (objectExists(locator, "isDisplayed", lngTimeOutInSeconds, strValue, "field", strPageName, false)) {
			String value = driver.findElement(locator).getAttribute(strValue);
			logger.info(
					String.format("Fetched value '%s' of %s in %s using %s", value, strValue, strPageName, locator));

			return value;

		} else {
			logger.error(String.format("Not able to fetch value '%s' in %s using %s", strValue, strPageName, locator));
			ALMFunctions.ThrowException(strValue, strValue + " should be displayed in the page " + strPageName,
					"Error - " + strValue + " is not displayed in " + strPageName, true);
			return "";
		}
	}

	/**
	 * Method to get text from the element
	 * 
	 * @param locator             - By locator of the control
	 * @param lngTimeOutInSeconds - No of seconds to wait for the control to reach
	 *                            its pre-requisite
	 * @param strElementName      - Name of the control
	 * @param strPageName         - Page name which the control resides
	 * @return - Text present in the control @
	 */
	public String getText(By locator, long lngTimeOutInSeconds, String strElementName, String strPageName)

	{

		String strGetText = "";
		if (driverUtil.waitUntilElementEnabled(locator, lngTimeOutInSeconds, strElementName, "Label", strPageName)) {
			if (driverUtil.waitUntilElementLocated(locator, lngTimeOutInSeconds, strElementName, "Label", strPageName,
					false)) {
				// pagescrollByActions(locator, strElementName,strPageName);
				strGetText = driver.findElement(locator).getText().trim();
				logger.info(String.format("Fetched value '%s' from %s in %s using %s", strGetText, strElementName,
						strPageName, locator));
			}
		} else {
			logger.error(
					String.format("Not able to fetch value '%s in %s using %s", strElementName, strPageName, locator));
			ALMFunctions.ThrowException(strElementName,
					strElementName + " should be displayed in the page " + strPageName,
					"Error - " + strElementName + " is not displayed in " + strPageName, true);
			return null;
		}
		return strGetText;
	}

	/**
	 * Method to get text from the element
	 * 
	 * @param locator             - By locator of the control
	 * @param lngTimeOutInSeconds - No of seconds to wait for the control to reach
	 *                            its pre-requisite
	 * @param strElementName      - Name of the control
	 * @param strPageName         - Page name which the control resides
	 * @return - Text present in the control @
	 */
	public String getText(WebElement locator, long lngTimeOutInSeconds, String strElementName, String strPageName)

	{

		String strGetText = "";
		if (driverUtil.waitUntilElementEnabled(locator, lngTimeOutInSeconds, strElementName, "Label", strPageName)) {
			/*
			 * if(driverUtil.waitUntilElementLocated(locator, lngTimeOutInSeconds,
			 * strElementName, "Label", strPageName,false)) { //
			 * pagescrollByActions(locator, strElementName,strPageName); strGetText=
			 * locator.getText().trim(); }
			 */
			strGetText = locator.getText().trim();
			logger.info(String.format("Fetched value '%s' from %s in %s using %s", strGetText, strElementName,
					strPageName, locator));
		} else {
			logger.error(
					String.format("Not able to fetch value '%s in %s using %s", strElementName, strPageName, locator));
			ALMFunctions.ThrowException(strElementName,
					strElementName + " should be displayed in the page " + strPageName,
					"Error - " + strElementName + " is not displayed in " + strPageName, true);
			return null;
		}
		return strGetText;
	}

	/**
	 * Method to get text from the element
	 * 
	 * @param locator             - By locator of the control
	 * @param lngTimeOutInSeconds - No of seconds to wait for the control to reach
	 *                            its pre-requisite
	 * @param strElementName      - Name of the control
	 * @param strPageName         - Page name which the control resides
	 * @return - Text present in the control @
	 */
	public String getAttributeValue(By locator, long lngTimeOutInSeconds, String strAttValue, String strElementName,
			String strPageName) {
		if (driverUtil.waitUntilElementLocated(locator, lngTimeOutInSeconds, strElementName, "Label", strPageName,
				false)) {
			// pagescrollByActions(locator, strElementName,strPageName);
			String attributeValue = driver.findElement(locator).getAttribute(strAttValue).trim();
			logger.info(String.format("Fetched attrbute value '%s' from %s in %s using %s", attributeValue,
					strElementName, strPageName, locator));
			return attributeValue;
		} else {
			logger.error(String.format("Not able to fetch value of %s in %s using %s", strAttValue, strElementName,
					strPageName, locator));
			ALMFunctions.ThrowException(strElementName,
					strElementName + " should be displayed in the page " + strPageName,
					"Error - " + strElementName + " is not displayed in " + strPageName, true);
			return "";
		}
	}

	/**
	 * Function to click on element
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 * @param elementName      The name of the element in which the expected text to
	 *                         be entered
	 * @param elementType      Type of the element
	 * @param pageName         Page in which title is to be compared with expected
	 *                         title @
	 */
	public void click(By locator, long timeOutInSeconds, String elementName, String elementType, String pageName,
			Boolean ReportLog) {
		try {
			if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, elementType, pageName,
					true)) {
				if (driverUtil.waitUntilElementVisible(locator, timeOutInSeconds, elementName, elementType, pageName,
						true)) {
					if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, elementName, elementType,
							pageName)) {

						driver.findElement(locator).click();
						logger.info(String.format("Clicked element %s in %s using %s", elementName, pageName, locator));
						if (ReportLog)
							report.updateTestLog("Click " + elementName + " " + elementType,
									elementName + " " + elementType + " should be clicked in the " + pageName + " Page",
									elementName + " " + elementType + " is clicked in the " + pageName + " Page",
									Status.DONE);
					} else {
						logger.error(String.format("Element not enabled, not able to click the '%s' in %s using %s",
								elementName, pageName, locator));
					} // ENABLED
				} else {
					logger.error(String.format("Element not visible, not able to click the '%s' in %s using %s",
							elementName, pageName, locator));
				} // VISIBLE
			} else {
				logger.error(String.format("Element not located, not able to click the '%s' in %s using %s",
						elementName, pageName, locator));
			} // LOCATE
		} catch (Exception e) {
			logger.error(String.format("Not able to click element %s in %s using %s", elementName, pageName, locator));
			ALMFunctions.ThrowException(elementName + " " + elementType,
					"User should be able to click on " + elementName, "Error - Unable to click " + elementName + " "
							+ elementType + " in page: " + "\"" + pageName + "\"",
					true);
		}
	}

	/**
	 * Function to click on element
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 * @param elementName      The name of the element in which the click operation
	 *                         is to be done
	 * @param elementType      Type of the element
	 * @param pageName         Page in which the element exists @
	 */
	public void clickWithoutVisibileCheck(By locator, long timeOutInSeconds, String elementName, String elementType,
			String pageName, Boolean ReportLog) {
		if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, elementType, pageName, true)) {
			try {
				driver.findElement(locator).click();
				if (ReportLog) {
					report.updateTestLog("Click " + elementName + " " + elementType,
							elementName + " " + elementType + " should be clicked in the " + pageName + " Page",
							elementName + " " + elementType + " is clicked in the " + pageName + " Page", Status.DONE);
				}
			} catch (Exception e) {
				ALMFunctions.ThrowException(elementName + " " + elementType,
						"User should be able to click " + "\"" + elementName + "\"" + " in the page " + "\"" + pageName
								+ "\"",
						"Below exception is thrown while trying to click " + "\"" + elementName + "\"" + "<br><br>"
								+ e.getLocalizedMessage(),
						true);
			}
		}
	}

	public void clickWithoutEnabledCheck(WebElement elm, long timeOutInSeconds, String elementName, String elementType,
			String pageName, Boolean ReportLog) {
		if (driverUtil.waitUntilElementVisible(elm, timeOutInSeconds, elementName, elementType, pageName, true)) {
			try {
				JavascriptExecutor jsExec = (JavascriptExecutor) driver.getWebDriver();
				jsExec.executeScript("arguments[0].click();", elm);
				if (ReportLog) {
					report.updateTestLog("Click " + elementName + " " + elementType,
							elementName + " " + elementType + " should be clicked in the " + pageName + " Page",
							elementName + " " + elementType + " is clicked in the " + pageName + " Page", Status.DONE);
				}
			} catch (Exception e) {
				ALMFunctions.ThrowException(elementName,
						"User should be able to click " + "\"" + elementName + "\"" + " in the page " + "\"" + pageName
								+ "\"",
						"Below exception is thrown while trying to click " + "\"" + elementName + "\"" + "<br><br>"
								+ e.getLocalizedMessage(),
						true);
			}
		}
	}

	public void click(WebElement elm, long timeOutInSeconds, String elementName, String elementType, String pageName,
			Boolean ReportLog) {
		if (driverUtil.waitUntilElementVisible(elm, timeOutInSeconds, elementName, elementType, pageName, true)) {
			if (driverUtil.waitUntilElementEnabled(elm, timeOutInSeconds, elementName, elementType, pageName)) {
				try {
					elm.click();
					if (ReportLog) {
						report.updateTestLog("Click " + elementName + " " + elementType,
								elementName + " " + elementType + " should be clicked in the " + pageName + " Page",
								elementName + " " + elementType + " is clicked in the " + pageName + " Page",
								Status.DONE);
					}
				} catch (Exception e) {
					ALMFunctions.ThrowException(elementName,
							"User should be able to click " + "\"" + elementName + "\"" + " in the page " + "\""
									+ pageName + "\"",
							"Below exception is thrown while trying to click " + "\"" + elementName + "\"" + "<br><br>"
									+ e.getLocalizedMessage(),
							true);
				}
			}
		}
	}

	public void selectDropdownByvalue(By locator, String strLabel, String strValue, String pageName,
			Boolean ReportLog) {
		try {
			if (objectExists(locator, "isDisplayed", lngPagetimeOutInSeconds, strLabel, strValue, pageName, false)) {

				Select printer = new Select(driver.findElement(locator));
				printer.selectByVisibleText(strValue);
				logger.info(String.format("Selected %s in pageName using %s", strValue, pageName, locator));
				if (ReportLog) {
					report.updateTestLog("Click",
							strLabel + " - " + strLabel + " in " + pageName + " should be clicked",
							strLabel + " - " + strLabel + " in " + pageName + " is clicked", Status.DONE);
				}
			}

		} catch (Exception e) {
			logger.error(String.format("Not able to select %s in pageName using %s", strValue, pageName, locator));
			ALMFunctions.ThrowException(strValue, "Specified value should be present in " + strLabel,
					"Error - Unable to search the " + strValue + " provided by the user in the " + strLabel, false);

		}

	}

	public void clickByJS(By locator, long timeOutInSeconds, String elementName, String elementType, String pageName,
			Boolean ReportLog) {
		if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, elementType, pageName,
				ReportLog)) {
			try {
				JavascriptExecutor jsExec = (JavascriptExecutor) driver.getWebDriver();
				WebElement e = driver.findElement(locator);
				jsExec.executeScript("arguments[0].click();", e);
				logger.info(String.format("Clicked %s in %s by javascript using %s", elementName, pageName, locator));
				if (ReportLog) {
					report.updateTestLog("Click " + elementName + " " + elementType,
							elementName + " " + elementType + " should be clicked in the " + pageName + " Page",
							elementName + " " + elementType + " is clicked in the " + pageName + " Page", Status.DONE);
				}
			} catch (Exception e) {
				logger.error(String.format("Not able to click %s in %s by javascript using %s", elementName, pageName,
						locator));
				ALMFunctions.ThrowException(elementName,
						"User should be able to click " + "\"" + elementName + "\"" + " in the page " + "\"" + pageName
								+ "\"",
						"Below exception is thrown while trying to click " + "\"" + elementName + "\"" + "<br><br>"
								+ e.getLocalizedMessage(),
						true);
			}
		}
	}

	public void clickByJS(WebElement element, long timeOutInSeconds, String elementName, String elementType,
			String pageName, Boolean ReportLog) {

		if (driverUtil.waitUntilElementEnabled(element, timeOutInSeconds, elementName, elementType, pageName)) {
			try {
				JavascriptExecutor jsExec = (JavascriptExecutor) driver.getWebDriver();
				jsExec.executeScript("arguments[0].click();", element);
				logger.info(String.format("Clicked %s in %s by javascript", elementName, pageName));
				if (ReportLog) {
					report.updateTestLog("Click " + elementName + " " + elementType,
							elementName + " " + elementType + " should be clicked in the " + pageName + " Page",
							elementName + " " + elementType + " is clicked in the " + pageName + " Page", Status.DONE);
				}
			} catch (Exception e) {
				logger.error(String.format("Not able to click %s in %s by javascript", elementName, pageName));
				ALMFunctions.ThrowException(elementName,
						"User should be able to click " + "\"" + elementName + "\"" + " in the page " + "\"" + pageName
								+ "\"",
						"Below exception is thrown while trying to click " + "\"" + elementName + "\"" + "<br><br>"
								+ e.getLocalizedMessage(),
						true);
			}
		}

	}

	public void doubleClick(By locator, long timeOutInSeconds, String elementName, String elementType, String pageName,
			Boolean ReportLog) {
		if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, elementType, pageName, true)) {

			try {
				driver.findElement(locator).sendKeys(Keys.ENTER);
				if (ReportLog) {
					report.updateTestLog("Click",
							elementType + " - " + elementName + " in " + pageName + " should be clicked",
							elementType + " - " + elementName + " in " + pageName + " is clicked", Status.DONE);
				}
			} catch (Exception e) {
				ALMFunctions.ThrowException(elementName,
						"User should be able to click " + "\"" + elementName + "\"" + " in the page " + "\"" + pageName
								+ "\"",
						"Below exception is thrown while trying to click " + "\"" + elementName + "\"" + "<br><br>"
								+ e.getLocalizedMessage(),
						true);

			}
		}

	}

	/**
	 * Function to clear data in text box
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 * @param elementName      The name of the element in which the expected text to
	 *                         be entered
	 * @param pageName         Page in which title is to be compared with expected
	 *                         title @
	 */
	public void clear(By locator, long timeOutInSeconds, String elementName, String pageName) {
		try {
			if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, "input field", pageName,
					true)) {
				if (driverUtil.waitUntilElementVisible(locator, timeOutInSeconds, elementName, "input field", pageName,
						true)) {
					if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, elementName, "input field",
							pageName)) {
						driver.findElement(locator).clear();
						logger.info(String.format("Cleared %s in %s using %s", elementName, pageName, locator));
					}
				}
			}
		} catch (Exception e) {
			logger.error(String.format("Not able to clear %s in %s using %s", elementName, pageName, locator));
			ALMFunctions.ThrowException(elementName, "User should be able to clear text on " + elementName,
					"Error - Unable to clear text in " + elementName + " input field in page: " + "\"" + pageName
							+ "\"",
					true);
		}
	}

	public void clear(WebElement element, long timeOutInSeconds, String elementName, String pageName) {
		try {

			if (driverUtil.waitUntilElementVisible(element, timeOutInSeconds, elementName, "input field", pageName,
					true)) {
				if (driverUtil.waitUntilElementEnabled(element, timeOutInSeconds, elementName, "input field",
						pageName)) {
					element.clear();
					logger.info(String.format("Cleared %s in %s", elementName, pageName));
				}
			}
		}

		catch (Exception e) {
			logger.error(String.format("Not able to clear %s in %s using %s", elementName, pageName));
			ALMFunctions.ThrowException(elementName, "User should be able to clear text on " + elementName,
					"Error - Unable to clear text in " + elementName + " input field in page: " + "\"" + pageName
							+ "\"",
					true);
		}
	}

	/**
	 * Function to select the specified value from a list box
	 * 
	 * @param by          The {@link WebDriver} locator used to identify the list
	 *                    box
	 * @param item        The value to be selected within the list box by visible
	 *                    text
	 * @param elementName The name of the list box
	 * @param pageName    Page Name in which the list box is available @
	 */
	public boolean selectListItem(By locator, long timeOutInSeconds, String[] items, String elementName,
			String pageName, String TypeofItem) {

		ArrayList<String> AppsAvaiableinApplication = new ArrayList<String>();
		ArrayList<String> AppsSelectedinApplication = new ArrayList<String>();

		if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, "list box", pageName, true)) {
			if (driverUtil.waitUntilElementVisible(locator, timeOutInSeconds, elementName, "list box", pageName,
					true)) {
				if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, elementName, "list box", pageName)) {
					Select dropDownList = new Select(driver.findElement(locator));

					AppsAvaiableinApplication = verifyItemInApplication(dropDownList, items, elementName, TypeofItem,
							true);

					if (dropDownList.isMultiple()) {
						dropDownList.deselectAll();
					}

					for (int i = 0; i < AppsAvaiableinApplication.size(); i++) {
						try {
							dropDownList.selectByVisibleText(AppsAvaiableinApplication.get(i));
							AppsSelectedinApplication.add(AppsAvaiableinApplication.get(i));
							logger.info(String.format("Selected item(s) of %s in %s using %s", elementName, pageName,
									locator));
							report.updateTestLog(elementName,
									TypeofItem + " should be selected by the user in " + elementName + " list box is "
											+ "\"" + AppsAvaiableinApplication.get(i) + "\"",
									TypeofItem + " selected by the user in " + elementName + " list box is " + "\""
											+ AppsAvaiableinApplication.get(i) + "\"",
									Status.DONE);
						} catch (ElementNotSelectableException e) {
							logger.error(String.format("Not able to select item(s) of %s in %s using %s", elementName,
									pageName, locator));
							ALMFunctions.ThrowException(elementName,
									"User should be able to select value " + AppsAvaiableinApplication.get(i) + " on "
											+ elementName,
									"Error - " + "\"" + AppsAvaiableinApplication.get(i) + "\""
											+ " is not selectable in the list box " + elementName + " in the page: "
											+ "\"" + pageName + "\"",
									true);
						} catch (Exception e) {
							logger.error(String.format("Not able to select item(s) of %s in %s using %s", elementName,
									pageName, locator));
							ALMFunctions.ThrowException(elementName,
									"User should be able to select value " + AppsAvaiableinApplication.get(i) + " on "
											+ elementName,
									"Error - Unable to select " + "\"" + AppsAvaiableinApplication.get(i) + "\""
											+ " in the list box " + elementName + " in the page: " + "\"" + pageName
											+ "\"",
									true);
						}

					}
				}
			}
		}

		if (AppsSelectedinApplication.size() == items.length) {
			return true;
		} else {
			return false;
		}
	}

	public ArrayList<String> verifyItemInApplication(Select element, String[] Items, String ElementName,
			String TypeofItem, boolean blnLog) {
		ArrayList<String> ItemsAvailableinApplication = new ArrayList<String>();
		ArrayList<String> ItemsNotAvailableinApplication = new ArrayList<String>();
		try {
			List<WebElement> options = element.getOptions();
			boolean flag = false;
			for (int i = 0; i < Items.length; i++) {
				for (int j = 0; j < options.size(); j++) {
//					driverUtil.waitFor(lngMinTimeOutInSeconds, ElementName, ElementName, TypeofItem);

					if (options.get(j).getText().trim().equalsIgnoreCase(Items[i])) {

						flag = true;
						ItemsAvailableinApplication.add(Items[i]);
						break;
					}

				}

				if (!flag)
					ItemsNotAvailableinApplication.add(Items[i]);

				flag = false;

			}

		} catch (Exception e) {
			ALMFunctions.ThrowException(ElementName, "Specified value should be present in " + ElementName,
					"Error - Unable to search the " + TypeofItem + " provided by the user in the " + ElementName, true);
		}

		if (blnLog) {
			if (ItemsNotAvailableinApplication.size() != 0) {
				ALMFunctions.ThrowException(ElementName, "Specified value should be present in " + ElementName,
						TypeofItem + ":" + ItemsNotAvailableinApplication + " provided in the"
								+ " datasheet is not available in the field " + ElementName,
						true);
			}
		}
		return ItemsAvailableinApplication;
	}

	/**
	 * Function to do a mouseover on top of the specified element
	 * 
	 * @param by          The {@link WebDriver} locator used to identify the element
	 * @param elementName The name of the element on which mouseover to be done
	 * @param elementType Type of the element
	 * @param pageName    Page in which Element is located @
	 */
	public void mouseOver(By by, long timeOutInSeconds, String elementName, String elementType, String pageName) {

		try {
			Actions actions = new Actions(driver.getWebDriver());
			actions.moveToElement(driver.findElement(by)).build().perform();
		} catch (Exception e) {
			ALMFunctions.ThrowException(elementName, "User should be able to perform mouse Over on " + elementName,
					"Error - Unable to perform MouseOver on the element " + elementName + " " + elementType
							+ " in the page: " + "\"" + pageName + "\"",
					true);
		}
	}

	/**
	 * Function to do a mouseover on top of the specified element
	 * 
	 * @param element     The {@link WebDriver} element
	 * @param elementName The name of the element on which mouseover to be done
	 * @param elementType Type of the element
	 * @param pageName    Page in which Element is located @
	 */
	public void mouseOver(WebElement element, long timeOutInSeconds, String elementName, String elementType,
			String pageName) {

		try {
			Actions actions = new Actions(driver.getWebDriver());
			actions.moveToElement(element).build().perform();
		} catch (Exception e) {
			ALMFunctions.ThrowException(elementName, "User should be able to perform mouse Over on " + elementName,
					"Error - Unable to perform MouseOver on the element " + elementName + " " + elementType
							+ " in the page: " + "\"" + pageName + "\"",
					true);
		}
	}

	public void iSelectValueOptionDropDownfromlist(By locator, String Text, String element, String ElementName,
			String pageName) {

		String value = null;
		try {

			Select printer = new Select(driver.findElement(locator));
			List<WebElement> li = printer.getOptions();
			int size = li.size();

			for (int i = 0; i < size; i++) {

				value = li.get(i).getText();
				if (value.equalsIgnoreCase(Text)) {

					printer.selectByValue(Text);
					break;
				}
			}

		} catch (Exception e) {
			ALMFunctions.ThrowException(ElementName, "Specified value should be present in " + ElementName,
					"Error - Unable to search the " + element + " provided by the user in the " + ElementName, true);
		}

	}

	/**
	 * Function to do a mouseover on top of the specified element and click on
	 * element
	 * 
	 * @param by          The {@link WebDriver} locator used to identify the element
	 * @param elementName The name of the element on which mouseover to be done
	 * @param elementType Type of the element
	 * @param pageName    Page in which Element is located @
	 */
	public void mouseOverandClick(By by, long timeOutInSeconds, String elementName, String elementType, String pageName,
			boolean ReportLog) {

		try {
			if (driverUtil.waitUntilElementLocated(by, timeOutInSeconds, elementName, elementType, pageName, true)) {
				Actions actions = new Actions(driver.getWebDriver());
				actions.moveToElement(driver.findElement(by)).click(driver.findElement(by)).build().perform();
				if (ReportLog) {
					report.updateTestLog("Click",
							elementType + " - " + elementName + " in " + pageName + " should be clicked",
							elementType + " - " + elementName + " in " + pageName + " is clicked", Status.DONE);
				}
			}
		} catch (Exception e) {
			ALMFunctions.ThrowException(elementName,
					"User should be able to perform mouse Over and click on " + elementName,
					"Error - Unable to perform MouseOver and click on the element " + elementName + " " + elementType
							+ " in the page: " + "\"" + pageName + "\"",
					true);
		}
	}

	public void mouseOverandEnter(By by, long timeOutInSeconds, String elementName, String strValue, String elementType,
			String pageName, boolean ReportLog) {

		try {
			if (driverUtil.waitUntilElementLocated(by, timeOutInSeconds, elementName, elementType, pageName, true)) {
				Actions actions = new Actions(driver.getWebDriver());
				actions.moveToElement(driver.findElement(by)).click(driver.findElement(by)).sendKeys(strValue).build()
						.perform();
				if (ReportLog) {
					report.updateTestLog(elementName,
							elementName + " should be entered by the user is: " + "\"" + strValue + "\""
									+ " in the page " + "\"" + pageName + "\"",
							elementName + " entered by the user is: " + "\"" + strValue + "\"" + " in the page " + "\""
									+ pageName + "\"",
							Status.DONE);
				}
			}
		} catch (Exception e) {
			ALMFunctions.ThrowException(elementName,
					"User should be able to perform mouse Over and Enter value on " + elementName,
					"Error - Unable to perform MouseOver and Enter value on the element " + elementName + " "
							+ elementType + " in the page: " + "\"" + pageName + "\"",
					true);
		}
	}

	/**
	 * Function to do a mouseover on top of the specified element and click on
	 * element
	 * 
	 * @param element     WebElement in which action to be performed
	 * @param elementName The name of the element on which mouseover to be done
	 * @param elementType Type of the element
	 * @param pageName    Page in which Element is located @
	 */
	public void mouseOverandClick(WebElement element, long timeOutInSeconds, String elementName, String elementType,
			String pageName) {

		try {
			if (driverUtil.waitUntilElementEnabled(element, timeOutInSeconds, elementName, elementType, pageName)) {
				Actions actions = new Actions(driver.getWebDriver());
				actions.moveToElement(element).click(element).build().perform();
			}
		} catch (Exception e) {
			ALMFunctions.ThrowException(elementName,
					"User should be able to perform mouse Over and click on " + elementName,
					"Error - Unable to perform MouseOver and click on the element " + elementName + " " + elementType
							+ " in the page: " + "\"" + pageName + "\"",
					true);
		}
	}

	public void draganddroppopup(By locator, long timeOutInSeconds, String elementName, String elementType,
			String pageName) {

		try {
			if (objectExists(locator, "isEnabled", timeOutInSeconds, elementName, elementType, pageName, false)) {

				WebElement draggable = driver.getWebDriver().findElement(locator);

				new Actions(driver.getWebDriver()).dragAndDropBy(draggable, 10, -200).build().perform();

			}

		} catch (Exception e) {
			ALMFunctions.ThrowException(elementName,
					"User should be able to perform mouse Over and click on " + elementName,
					"Error - Unable to perform MouseOver and click on the element " + elementName + " " + elementType
							+ " in the page: " + "\"" + pageName + "\"",
					true);
		}
	}

	public void menuClick(By loactor, By loactor1, long timeOutInSeconds, String elementName, String elementType,
			String pageName) {

		try {
			if (objectExists(loactor, "isEnabled", timeOutInSeconds, elementName, elementType, pageName, false)) {
				WebElement element = driver.findElement(loactor);
				WebElement element1 = driver.findElement(loactor1);
				Actions actions = new Actions(driver.getWebDriver());
				actions.moveToElement(element).moveToElement(element1).build().perform();
			}
		} catch (Exception e) {
			ALMFunctions.ThrowException(elementName,
					"User should be able to perform mouse Over and click on " + elementName,
					"Error - Unable to perform MouseOver and click on the element " + elementName + " " + elementType
							+ " in the page: " + "\"" + pageName + "\"",
					true);
		}
	}

	/**
	 * Function to do a mouse over on top of the specified element and click on
	 * element
	 * 
	 * @param element     WebElement in which action to be performed
	 * @param elementName The name of the element on which mouseover to be done
	 * @param elementType Type of the element
	 * @param pageName    Page in which Element is located @
	 */
	public void mouseOverandDoubleClick(By by, long timeOutInSeconds, String elementName, String elementType,
			String pageName) {
		if (driverUtil.waitUntilElementEnabled(by, timeOutInSeconds, elementName, elementType, pageName)) {
			try {
				Actions actions = new Actions(driver.getWebDriver());
				actions.moveToElement(driver.findElement(by)).doubleClick(driver.findElement(by)).build().perform();
			} catch (Exception e) {
				ALMFunctions.ThrowException(elementName,
						"User should be able to perform mouse Over and click on " + elementName,
						"Error - Unable to perform MouseOver and double click on the element " + elementName + " "
								+ elementType + " in the page: " + "\"" + pageName + "\"",
						true);
			}
		}
	}

	/**
	 * Function to verify whether the specified object exists within the current
	 * page
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param condition        Element condition to be determined
	 * @param condition        Element condition to be determined
	 * @param timeOutInSeconds The wait timeout in seconds
	 * @param elementName      The name of the element for which existence and
	 *                         condition to be determined
	 * @param elementType      Type of the element
	 * @param pageName         Page in which title is to be compared with expected
	 *                         title
	 * @param Log              indicator true or false for report logging
	 * @return Boolean value indicating whether the specified object exists @
	 */
	public Boolean objectExists(By by, String condition, long timeOutInSeconds, String elementName, String elementType,
			String pageName, Boolean LogIndicator) {
		Boolean StatusFlag = false;
		if (driverUtil.waitUntilElementLocated(by, timeOutInSeconds, elementName, elementType, pageName,
				LogIndicator)) {
			if (driverUtil.waitUntilElementVisible(by, timeOutInSeconds, elementName, elementType, pageName,
					LogIndicator)) {
				switch (condition) {
				case "isDisplayed":
					StatusFlag = driver.findElement(by).isDisplayed();
					break;
				case "isEnabled":
					if (driverUtil.waitUntilElementEnabled(by, timeOutInSeconds, elementName, elementType, pageName)) {
						StatusFlag = driver.findElement(by).isEnabled();
					}
					break;
				case "isSelected":
					StatusFlag = driver.findElement(by).isSelected();
					break;
				default:
					ALMFunctions.ThrowException("Input Data",
							"Only Pre-Defined Options are allowed in objExists function",
							"Change the operations as per switch case", true);
					break;
				}
			}
		}
		if (LogIndicator) {
			if (StatusFlag) {
				if (condition.equals("isDisplayed")) {
					logger.info(String.format("Object %s displayed in %s using %s", elementName, pageName, by));
					ALMFunctions.UpdateReportLogAndALMForPassStatus(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be displayed in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is displayed in the page: " + "\""
									+ pageName + "\"",
							true);
				} else if (condition.equals("isEnabled")) {
					logger.info(String.format("Object %s enabled in %s using %s", elementName, pageName, by));
					ALMFunctions.UpdateReportLogAndALMForPassStatus(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be enabled in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is enabled in the page: " + "\""
									+ pageName + "\"",
							true);
				} else if (condition.equals("isSelected")) {
					logger.info(String.format("Object %s selected in %s using %s", elementName, pageName, by));
					ALMFunctions.UpdateReportLogAndALMForPassStatus(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be selected in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is selected in the page: " + "\""
									+ pageName + "\"",
							true);
				}
			} else {
				if (condition.equals("isDisplayed")) {
					logger.error(String.format("Object %s not displayed in %s using %s", elementName, pageName, by));
					ALMFunctions.ThrowException(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be displayed in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is not displayed in the page: " + "\""
									+ pageName + "\"",
							true);
				} else if (condition.equals("isEnabled")) {
					logger.error(String.format("Object %s not enabled in %s using %s", elementName, pageName, by));
					ALMFunctions.ThrowException(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be enabled in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is not enabled in the page: " + "\""
									+ pageName + "\"",
							true);
				} else if (condition.equals("isSelected")) {
					logger.error(String.format("Object %s not selected in %s using %s", elementName, pageName, by));
					ALMFunctions.ThrowException(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be selected in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is not selected in the page: " + "\""
									+ pageName + "\"",
							true);
				}
			}
		}
		return StatusFlag;
	}

	public Boolean objectExists(WebElement elm, String condition, long timeOutInSeconds, String elementName,
			String elementType, String pageName, Boolean LogIndicator) {
		Boolean StatusFlag = false;

		if (driverUtil.waitUntilElementVisible(elm, timeOutInSeconds, elementName, elementType, pageName,
				LogIndicator)) {
			switch (condition) {
			case "isDisplayed":
				StatusFlag = elm.isDisplayed();
				break;
			case "isEnabled":
				if (driverUtil.waitUntilElementEnabled(elm, timeOutInSeconds, elementName, elementType, pageName)) {
					StatusFlag = elm.isEnabled();
				}
				break;
			case "isSelected":
				StatusFlag = elm.isSelected();
				break;
			default:
				ALMFunctions.ThrowException("Input Data", "Only Pre-Defined Options are allowed in objExists function",
						"Change the operations as per switch case", true);
				break;
			}
		}

		if (LogIndicator) {
			if (StatusFlag) {
				if (condition.equals("isDisplayed")) {
					logger.info(String.format("Object %s displayed in %s", elementName, pageName));
					ALMFunctions.UpdateReportLogAndALMForPassStatus(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be displayed in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is displayed in the page: " + "\""
									+ pageName + "\"",
							true);
				} else if (condition.equals("isEnabled")) {
					logger.info(String.format("Object %s enabled in %s", elementName, pageName));
					ALMFunctions.UpdateReportLogAndALMForPassStatus(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be enabled in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is enabled in the page: " + "\""
									+ pageName + "\"",
							true);
				} else if (condition.equals("isSelected")) {
					logger.info(String.format("Object %s selected in %s", elementName, pageName));
					ALMFunctions.UpdateReportLogAndALMForPassStatus(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be selected in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is selected in the page: " + "\""
									+ pageName + "\"",
							true);
				}
			} else {
				if (condition.equals("isDisplayed")) {
					logger.error(String.format("Object %s not displayed in %s", elementName, pageName));
					ALMFunctions.ThrowException(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be displayed in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is not displayed in the page: " + "\""
									+ pageName + "\"",
							true);
				} else if (condition.equals("isEnabled")) {
					logger.error(String.format("Object %s not enabled in %s", elementName, pageName));
					ALMFunctions.ThrowException(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be enabled in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is not enabled in the page: " + "\""
									+ pageName + "\"",
							true);
				} else if (condition.equals("isSelected")) {
					logger.error(String.format("Object %s not selected in %s", elementName, pageName));
					ALMFunctions.ThrowException(elementName,
							"\"" + elementName + "\"" + " " + elementType + " should be selected in the page: " + "\""
									+ pageName + "\"",
							"\"" + elementName + "\"" + " " + elementType + " is not selected in the page: " + "\""
									+ pageName + "\"",
							true);
				}
			}
		}
		return StatusFlag;
	}

	/**
	 * Function to verify whether the specified text is present within the current
	 * page
	 * 
	 * @param textPattern The text to be verified
	 * @return Boolean value indicating whether the specified text is present
	 */
	public Boolean isTextPresent(String textPattern) {

		return driver.findElement(By.cssSelector("BODY")).getText().matches(textPattern);
	}

	/**
	 * Function to check if an alert is present on the current page
	 * 
	 * @param timeOutInSeconds The number of seconds to wait while checking for the
	 *                         alert
	 * @return Boolean value indicating whether an alert is present
	 */
	public Boolean isAlertPresent(long timeOutInSeconds) {
		try {
			new WebDriverWait(driver.getWebDriver(), Duration.ofSeconds(timeOutInSeconds)).until(ExpectedConditions.alertIsPresent());
			return true;
		} catch (TimeoutException ex) {
			return false;
		}
	}

	/**
	 * Function to accept alert
	 * 
	 * @param timeOutInSeconds The number of seconds to wait while checking for the
	 *                         alert
	 * @return Boolean value indicating whether alert is accepted or not @
	 */
	public Boolean acceptAlert(long timeOutInSeconds) {

		if (isAlertPresent(timeOutInSeconds)) {
			Alert alert = driver.switchTo().alert();
			alert.accept();
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Verify Alert", "Alert should be accepted",
					"Alert is accepted successfully", true);
			return true;
		} else {
			ALMFunctions.ThrowException("Alert", "Alert message should be displayed", "Alert message is not displayed",
					true);
			return false;
		}
	}

	/**
	 * Function to deny alert
	 * 
	 * @param timeOutInSeconds The number of seconds to wait while checking for the
	 *                         alert
	 * @return Boolean value indicating whether alert is dismissed
	 */
	public Boolean denyAlert(long timeOutInSeconds) {

		if (isAlertPresent(timeOutInSeconds)) {
			Alert alert = driver.switchTo().alert();

			alert.dismiss();
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Function to deny alert
	 * 
	 * @param timeOutInSeconds The number of seconds to wait while checking for the
	 *                         alert
	 * @return Boolean value indicating whether alert is dismissed
	 */
	public String getAlertText(long timeOutInSeconds) {

		if (isAlertPresent(timeOutInSeconds)) {
			Alert alert = driver.switchTo().alert();
			return alert.getText();
		} else {
			return "null";
		}
	}

	/**
	 * Function to scroll down
	 * 
	 * @param By		   The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param pageName         Name of the Page which has to be scrolled @
	 */
	public void pagescroll(By locator, String pageName) {
		try {
			((JavascriptExecutor) driver.getWebDriver()).executeScript("arguments[0].scrollIntoViewIfNeeded();",
					driver.findElement(locator));
		} catch (Exception e) {
			ALMFunctions.ThrowException("Page Scroll",
					"User should be able to perform scroll in the page " + pageName
							+ " to capture desired element in screenshot",
					"Unable to perform scroll on page: " + pageName, true);
		}

	}

	/**
	 * Function to scroll down
	 * 
	 *  @param WebElement	   The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param pageName         Name of the Page which has to be scrolled @
	 */
	public void pagescroll(WebElement locator, String pageName) {
		try {

			((JavascriptExecutor) driver.getWebDriver()).executeScript("arguments[0].scrollIntoViewIfNeeded();", locator);

		} catch (Exception e) {
			ALMFunctions.ThrowException("Page Scroll",
					"User should be able to perform scroll in the page " + pageName
							+ " to capture desired element in screenshot",
					"Unable to perform scroll on page: " + pageName, true);
		}
	}

	/**
	 * Function to switch to new window
	 * 
	 * @param lngPagetimeOutInSeconds	   The number of seconds to wait while switching to new window
	 * @param strWindowHandle              The current window id
	 * @param strWindowName                The switch window name
	 */
	public void switchToNewWindow(long lngPagetimeOutInSeconds, String strWindowHandle, String strWindowName) {
		if (driverUtil.waitUntilWindowCountAvailable(2, strWindowName, lngPagetimeOutInSeconds)) {
			Set<String> handles = driver.getWindowHandles();
			for (String windowHandle : handles) {
				if (!windowHandle.equals(strWindowHandle)) {
					driver.switchTo().window(windowHandle);
					driver.manage().window().maximize();
				}
			}
		}
	}
	
	/**
	 * Function to switch to new window and close current window
	 * 
	 * @param lngPagetimeOutInSeconds	   The number of seconds to wait while switching to new window
	 * @param strWindowHandle              The current window id
	 * @param strWindowName                The switch window name
	 */
	public void switchToNewWindowANDCloseCurrentWindow(long lngPagetimeOutInSeconds, String strWindowHandle, String strWindowName) {
		if (driverUtil.waitUntilWindowCountAvailable(2, strWindowName, lngPagetimeOutInSeconds)) {
			Set<String> handles = driver.getWindowHandles();
			for (String windowHandle : handles) {
				if (!windowHandle.equals(strWindowHandle)) {
					driver.close();
					driver.switchTo().window(windowHandle);
					driver.manage().window().maximize();
				}
			}
		}
	}

	/**
	 * Function to input Keystroke in text box
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 * @param Keystroke        Expected Keystroke to be passed to the input field
	 * @param elementName      The name of the element in which the expected text to
	 *                         be entered
	 * @param pageName         Page in which title is to be compared with expected
	 *                         title @
	 * @param blnlogStatus     boolean to indicate whether report log is to be added
	 *                         or not
	 */
	public void sendkeys(By locator, long timeOutInSeconds, Keys keyStroke, String elementName, String pageName,
			boolean blnlogStatus) {
		if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, "Input field", pageName, true)) {
			if (driverUtil.waitUntilElementVisible(locator, timeOutInSeconds, elementName, "Input field", pageName,
					true)) {
				if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, elementName, "Input field",
						pageName)) {
					try {
						WebElement e = driver.findElement(locator);
						e.sendKeys(keyStroke);
						logger.info(String.format("Entered '%s' in %s of %s using %s", keyStroke.toString(),
								elementName, pageName, locator));
						if (blnlogStatus)
							report.updateTestLog(elementName,
									elementName + " should be entered by the user is: " + "\"" + keyStroke + "\""
											+ " in the page " + "\"" + pageName + "\"",
									elementName + " entered by the user is: " + "\"" + keyStroke + "\""
											+ " in the page " + "\"" + pageName + "\"",
									Status.DONE);
					} catch (Exception e) {
						logger.error(String.format("Not able to enter '%s' in %s of %s using %s", keyStroke.toString(),
								elementName, pageName, locator));
						ALMFunctions.ThrowException(elementName,
								"User should be able to provide keystroke in " + "\"" + elementName + "\""
										+ " in the page " + "\"" + pageName + "\"",
								"Below exception is thrown while trying to provide keystroke in field " + "\""
										+ elementName + "\"" + "<br><br>" + e.getLocalizedMessage(),
								true);
					}
				} else {
					logger.error(String.format("Element not enabled, not able to enter '%s' in %s of %s using %s",
							keyStroke, elementName, pageName, locator));
				} // ENABLED
			} else {
				logger.error(String.format("Element not visible, not able to enter '%s' in %s of %s using %s",
						keyStroke, elementName, pageName, locator));
			} // VISIBLE
		} else {
			logger.error(String.format("Element not located, not able to enter '%s' in %s of %s using %s", keyStroke,
					elementName, pageName, locator));
		} // LOCATED
	}

	/**
	 * Function to enter text using Javascript executor on element
	 * 
	 * @param by               The {@link WebDriver} locator used to identify the
	 *                         element
	 * @param timeOutInSeconds The wait timeout in seconds
	 * @param elementName      The name of the element in which the click operation
	 *                         is to be done
	 * @param elementType      Type of the element
	 * @param pageName         Page in which the element exists @
	 */
	public void sendKeysByJsExec(By locator, long timeOutInSeconds, String Text, String elementName, String pageName,
			Boolean ReportLog) {
		if (driverUtil.waitUntilElementLocated(locator, timeOutInSeconds, elementName, "input field", pageName, true)) {
			if (driverUtil.waitUntilElementVisible(locator, timeOutInSeconds, elementName, "input field", pageName,
					true)) {
				if (driverUtil.waitUntilElementEnabled(locator, timeOutInSeconds, elementName, "input field",
						pageName)) {
					try {
						JavascriptExecutor jsExec = (JavascriptExecutor) driver.getWebDriver();
						jsExec.executeScript("arguments[0].value='" + Text + "'", driver.findElement(locator));
						logger.info(String.format("Entered '%s' in %s in %s using %s", Text, elementName, pageName,
								locator));
						if (ReportLog) {
							report.updateTestLog(elementName,
									elementName + " should be entered by the user is: " + "\"" + Text + "\""
											+ " in the page " + "\"" + pageName + "\"",
									elementName + " entered by the user is: " + "\"" + Text + "\"" + " in the page "
											+ "\"" + pageName + "\"",
									Status.DONE);
						}
					} catch (Exception e) {
						logger.info(String.format("Not able to enter '%s' in %s in %s using %s", Text, elementName,
								pageName, locator));
						ALMFunctions.ThrowException(elementName,
								"User should be able to enter text " + "\"" + Text + "\"" + " in " + "\"" + elementName
										+ "\"" + " in the page " + "\"" + pageName + "\"",
								"Below exception is thrown while trying to enter text in field " + "\"" + elementName
										+ "\"" + "<br><br>" + e.getLocalizedMessage(),
								true);
					}
				}
			}
		}
	}

	public void mouseOverandRightClick(WebElement element, long timeOutInSeconds, String elementName,
			String elementType, String pageName) {
		if (driverUtil.waitUntilElementEnabled(element, timeOutInSeconds, elementName, elementType, pageName)) {
			try {
				Actions actions = new Actions(driver.getWebDriver());
				actions.moveToElement(element).contextClick(element).build().perform();
			} catch (Exception e) {
				ALMFunctions.ThrowException(elementName,
						"User should be able to perform mouse Over and click on " + elementName,
						"Error - Unable to perform MouseOver and double click on the element " + elementName + " "
								+ elementType + " in the page: " + "\"" + pageName + "\"",
						true);
			}
		}
	}

	public void mouseOverandRightClick(By by, long timeOutInSeconds, String elementName, String elementType,
			String pageName, boolean ReportLog) {

		try {
			if (driverUtil.waitUntilElementEnabled(by, timeOutInSeconds, elementName, elementType, pageName)) {
				Actions actions = new Actions(driver.getWebDriver());
				actions.moveToElement(driver.findElement(by)).contextClick(driver.findElement(by)).build().perform();
				if (ReportLog) {
					report.updateTestLog("Click",
							elementType + " - " + elementName + " in " + pageName + " should be clicked",
							elementType + " - " + elementName + " in " + pageName + " is clicked", Status.DONE);
				}
			}
		} catch (Exception e) {
			ALMFunctions.ThrowException(elementName,
					"User should be able to perform mouse Over and click on " + elementName,
					"Error - Unable to perform MouseOver and click on the element " + elementName + " " + elementType
							+ " in the page: " + "\"" + pageName + "\"",
					true);
		}
	}

	public void compareText(WebElement element, String valueToCompare) {

		if (element.getText().trim().equals(valueToCompare.trim())) {
			ALMFunctions.UpdateReportLogAndALMForPassStatus("Value comparison",
					"Expected" + element.getText() + "should be equal to the actual " + valueToCompare,
					"Expected" + element.getText() + "is  equal to the actual " + valueToCompare, true);
		}

		else {
			ALMFunctions.UpdateReportLogAndALMForFailStatus("Value comparison",
					"Expected : " + element.getText() + "should be equal to the actual:  " + valueToCompare,
					"Expected" + element.getText() + "is not equal to the actual " + valueToCompare, true);
		}

	}

	public int executeScript(boolean blnAutoIT, String strFilePath, String... arrArguments) {
		String[] cmdArr = new String[arrArguments.length + 1];
		cmdArr[0] = strFilePath;
		for (int i = 0; i < arrArguments.length; i++) {
			cmdArr[i + 1] = arrArguments[i];
		}
		try {
			Process p = Runtime.getRuntime().exec(cmdArr);
			p.waitFor();
			p.destroy();
			return p.exitValue();
		} catch (Exception e) {
			if (blnAutoIT) {
				ALMFunctions.ThrowException("AutoIT Script", "Should be able to execute AutoIT script",
						"Below exception is thrown while executing the AutoIT" + " script.<br><br>"
								+ e.getLocalizedMessage(),
						true);
			} else {
				ALMFunctions.ThrowException("VBS Script", "Should be able to execute VBS script",
						"Below exception is thrown while executing the VBS" + " script.<br><br>"
								+ e.getLocalizedMessage(),
						true);
			}

			return 1;// false
		}
	}

	public Sheet getSheetFromXLSWorkbook(XSSFWorkbook workbook, String strSheetName, String strFilePath) {
		boolean blnSheetFound = false;
		if (workbook.getNumberOfSheets() != 0) {
			for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
				if (workbook.getSheetName(i).equals(strSheetName)) {
					blnSheetFound = true;
					return workbook.getSheet(strSheetName);
				}
			}
			if (!blnSheetFound) {
				ALMFunctions.ThrowException("Verify Sheet",
						"Workbook should have the sheet " + "\"" + strSheetName + "\"",
						"Workbook does not contains a sheet with name " + "\"" + strSheetName + "\""
								+ " in the file found in the path " + "\"" + strFilePath + "\"",
						false);
			}
		} else {
			ALMFunctions.ThrowException("Verify Sheet", "Workbook should have the sheet " + "\"" + strSheetName + "\"",
					"Workbook is blank in the file found in the path " + "\"" + strFilePath + "\"", false);
		}
		return null;
	}

	public int getColumnIndex(String strFilePath, String strSheet, String strColumnName) {

		XSSFWorkbook wb = openExcelFile(strFilePath);
		Sheet sheet = getSheetFromXLSWorkbook(wb, strSheet, strFilePath);
		boolean blnColumnFound = false;
		Row row = sheet.getRow(0);
		if (row == null) {
			ALMFunctions.ThrowException("Header Row", "Header row should exists in the sheet " + "\"" + strSheet + "\"",
					"Header row does not exists in the sheet " + "\"" + strSheet + " of the excel file " + "\""
							+ strFilePath + "\"",
					false);
		} else {
			for (int i = 0; i < row.getLastCellNum(); i++) {
				Cell cell = row.getCell(i);
				String strActualString = getCellValueAsString(wb, cell);
				if (strActualString.equalsIgnoreCase(strColumnName)) {
					blnColumnFound = true;
					return i;
				}
			}
		}
		if (!blnColumnFound) {
			ALMFunctions.ThrowException("Column Header",
					"Column Header " + strColumnName + " should exists in the sheet " + "\"" + strSheet + "\"",
					"Column - " + "\"" + strColumnName + "\"" + " does not exists in the sheet " + "\"" + strSheet
							+ "\"" + " of the excel file " + "\"" + strFilePath + "\"",
					false);
		}
		return 0;

	}

	public int getColumnIndex(XSSFWorkbook wb, String strFilePath, String strSheet, String strColumnName) {
		
		Sheet sheet = getSheetFromXLSWorkbook(wb, strSheet, strFilePath);
		boolean blnColumnFound = false;
		Row row = sheet.getRow(0);
		if (row == null) {
			ALMFunctions.ThrowException("Header Row", "Header row should exists in the sheet " + "\"" + strSheet + "\"",
					"Header row does not exists in the sheet " + "\"" + strSheet + " of the excel file " + "\""
							+ strFilePath + "\"",
					false);
		} else {
			for (int i = 0; i < row.getLastCellNum(); i++) {
				Cell cell = row.getCell(i);
				String strActualString = getCellValueAsString(wb, cell);
				if (strActualString.equalsIgnoreCase(strColumnName)) {
					blnColumnFound = true;
					return i;
				}
			}
		}
		if (!blnColumnFound) {
			ALMFunctions.ThrowException("Column Header",
					"Column Header " + strColumnName + " should exists in the sheet " + "\"" + strSheet + "\"",
					"Column - " + "\"" + strColumnName + "\"" + " does not exists in the sheet " + "\"" + strSheet
							+ "\"" + " of the excel file " + "\"" + strFilePath + "\"",
					false);
		}
		return 0;

	}

	public XSSFWorkbook openExcelFile(String strFilePath) {
		XSSFWorkbook wb = null;
		try {
			wb = new XSSFWorkbook(new FileInputStream(strFilePath));
		} catch (IOException e) {
			ALMFunctions.ThrowException("Excel File",
					"Excel File should be available in the path " + "\"" + strFilePath + "\"",
					"Excel File is not available in the path " + "\"" + strFilePath + "\"", false);
			return null;
		}
		return wb;
	}

	public String getCellValueAsString(XSSFWorkbook wb, Cell cell) {
		DataFormatter dataFormatter = new DataFormatter();
		String strValue;
		if (cell != null) {
			if (cell.getCellType() == XSSFCell.CELL_TYPE_BLANK) {
				return "";
			} else if (cell.getCellType() == XSSFCell.CELL_TYPE_FORMULA) {
				CalculateFormula(wb, cell);
				strValue = dataFormatter.formatCellValue(cell);
			} else {
				strValue = dataFormatter.formatCellValue(cell);
			}
		} else {
			return "";
		}
		return strValue;
	}

	public void CalculateFormula(Workbook wb, Cell cell) {
		FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();
		evaluator.evaluateInCell(cell);
	}

	/**
	 * Method to get column index from the table
	 * 
	 * @param strExpectedColumnName - Name of the column for which index is to be
	 *                              found
	 * @param headers               - By locator of the header
	 * @param strTableName          - Name of the table in which the header exists
	 * @param blnLog                - Boolean to indicate whether log has to be
	 *                              captured in case of failure
	 * @param blnThrowException     - Boolean to indicate whether exception has to
	 *                              be thrown incase of failure
	 * @return - int - Index of the required header @
	 */
	public int getColumnIndex(String strExpectedColumnName, By headers, String strTableName, boolean blnLog,
			boolean blnThrowException) {
		// String strGetBrowser = testparameters.getBrowser().getValue().toLowerCase();
		int intColumnIndex = -1;
                driverUtil.waitUntilElementLocated(headers, lngPagetimeOutInSeconds);
		List<WebElement> tableHeaders = driver.getWebDriver().findElements(headers);
		// List<WebElement> tableHeaders = driver.getWebDriver().findElements(headers);
		if (tableHeaders.size() == 0) {
			ALMFunctions.ThrowException(strTableName, strTableName + " headers should be displayed",
					strTableName + " headers are not displayed", true);
		}
		for (WebElement tableHeader : tableHeaders) {

			Actions action = new Actions(driver.getWebDriver());
			action.moveToElement(tableHeader).build().perform();

			if (tableHeader.getText().replace("Click here to sort", "").toLowerCase().trim()
					.equals(strExpectedColumnName.toLowerCase().trim())) {
				intColumnIndex = tableHeaders.indexOf(tableHeader);
				break;
			}

			else if (tableHeader.getText().replace("Sort in ascending order", "").toLowerCase().trim()
					.equals(strExpectedColumnName.toLowerCase().trim())) {

				intColumnIndex = tableHeaders.indexOf(tableHeader);
				break;
			} else if (tableHeader.getText().replace("Sort in descending order", "").toLowerCase().trim()
					.equals(strExpectedColumnName.toLowerCase().trim())) {

				intColumnIndex = tableHeaders.indexOf(tableHeader);
				break;
			} else if (tableHeader.getText().toLowerCase().trim().equals(strExpectedColumnName.toLowerCase().trim())) {

				intColumnIndex = tableHeaders.indexOf(tableHeader);
				break;
			}
		}
		if (blnLog) {
			if (blnThrowException) {
				ALMFunctions.ThrowException("Column Name",
						strExpectedColumnName + " column name should be found in table - " + strTableName,
						strExpectedColumnName + " column name is not found in table - " + strTableName, true);
			} else {
				ALMFunctions.UpdateReportLogAndALMForFailStatus("Column Name",
						strExpectedColumnName + " column name should be found in table - " + strTableName,
						strExpectedColumnName + " column name is not found in table - " + strTableName, true);
			}
		}
		return intColumnIndex;
	}

	/**
	 * Function to handle the file upload
	 * 
	 * @param flag     for checking file is uploaded or not
	 * @param location of the file to be selected
	 * @param argument values for autoIT function. @ *
	 */
	public int executeScript(String strFilePath, String... arrArguments) {
		String[] cmdArr = new String[arrArguments.length + 1];
		cmdArr[0] = strFilePath;
		for (int i = 0; i < arrArguments.length; i++) {
			cmdArr[i + 1] = arrArguments[i];
		}
		try {
			Process p = Runtime.getRuntime().exec(cmdArr);
			p.waitFor();
			p.destroy();
			return p.exitValue();
		} catch (Exception e) {
			ALMFunctions.ThrowException("AutoIT Script", "Should be able to execute AutoIT script",
					"Below exception is thrown while executing the AutoIT" + " script.<br><br>"
							+ e.getLocalizedMessage(),
					true);
		}
		return 1;
	}

	public synchronized void setKeywordCount(String keyword) {
		ServiceRegister register = ServiceRegister.getInstance();
		// if (report.getFailureDescriptions().size() > 0) {

		if (Boolean
				.parseBoolean(register.getService(Thread.currentThread().getName(), "StopExecutionFlag").toString())) {
			throw new SkipException(keyword + " Skipped");
		}
		// }
		StackTraceElement[] elements = Thread.currentThread().getStackTrace();

		Optional<StackTraceElement> methodName = Arrays.asList(elements).stream()
				.filter((element) -> element.getMethodName().contains(keyword)).findFirst();

		if (methodName.isPresent()) {

			if (iterations.containsKey(keyword)) {
				iterations.put(keyword, iterations.get(keyword) + 1);
			} else {
				iterations.put(keyword, 1);
			}
			driverScript.setKeyWordIterations(keyword, iterations.get(keyword));
			logger.info(String.format("Calling %s - with iteration %s in class %s", keyword, iterations.get(keyword),
					methodName.get().getClassName()));
		}

	}

	public void setDriverScript(DriverScript driverScript) {
		this.driverScript = driverScript;
	}

}
