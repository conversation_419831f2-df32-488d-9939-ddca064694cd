<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website" tests="3" failures="0" timestamp="2025-04-22T11:31:50 IST" time="330.873" errors="1">
  <testcase name="afterMethod" time="17.251" classname="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website">
    <error type="org.openqa.selenium.WebDriverException" message="Timed out waiting for driver server to shutdown.
Build info: version: &#039;4.1.2&#039;, revision: &#039;9a5a329c5a&#039;
System info: host: &#039;Z1VD7STHPRDN255&#039;, ip: &#039;************&#039;, os.name: &#039;Windows 10&#039;, os.arch: &#039;amd64&#039;, os.version: &#039;10.0&#039;, java.version: &#039;1.8.0_291&#039;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [d117071334df6b37d2c842a5c3425aa7, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57183}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57183/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: d117071334df6b37d2c842a5c3425aa7">
      <![CDATA[org.openqa.selenium.WebDriverException: Timed out waiting for driver server to shutdown.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [d117071334df6b37d2c842a5c3425aa7, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57183}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57183/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: d117071334df6b37d2c842a5c3425aa7
at org.openqa.selenium.remote.service.DriverService.stop(DriverService.java:277)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:129)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:660)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: org.openqa.selenium.net.UrlChecker$TimeoutException: Timed out waiting for http://localhost:62897/shutdown to become unavailable after 3005 ms
at org.openqa.selenium.net.UrlChecker.waitUntilUnavailable(UrlChecker.java:130)
at org.openqa.selenium.remote.service.DriverService.stop(DriverService.java:273)
... 30 more
Caused by: java.util.concurrent.TimeoutException
at java.util.concurrent.FutureTask.get(FutureTask.java:205)
at org.openqa.selenium.net.UrlChecker.waitUntilUnavailable(UrlChecker.java:128)
... 31 more
]]>
    </error>
  </testcase> <!-- afterMethod -->
  <system-out/>
  <testcase name="invokeURL" time="41.049" classname="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website"/>
  <system-out/>
  <testcase name="navigationVerification" time="272.573" classname="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website"/>
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website -->
