package com.gilead.testscripts.Smoke_CICD;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;


public class KiteKonnectPage_il extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void checkLinksInTheWebPage() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchURL();
			objCommonFunctions.verifyRandomPageNavigation();
		} finally {
			checkErrors();
		}
	}

}
