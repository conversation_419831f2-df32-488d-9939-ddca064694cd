<html>
<head>
<title>TestNG:  Test under EntireSuite_Suite1</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Test under EntireSuite_Suite1</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>18/0/0</td>
</tr><tr>
<td>Started on:</td><td>Wed Apr 02 13:27:11 IST 2025</td>
</tr>
<tr><td>Total time:</td><td>315 seconds (315152 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001</td>
<td></td>
<td>20</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006.verifyPipeline()'><b>verifyPipeline</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006</td>
<td></td>
<td>30</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004.verifyStoryPage()'><b>verifyStoryPage</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004</td>
<td></td>
<td>44</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003.newsPressRelease()'><b>newsPressRelease</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003</td>
<td></td>
<td>70</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007</td>
<td></td>
<td>14</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008</td>
<td></td>
<td>20</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005.verifyCompanyStatement()'><b>verifyCompanyStatement</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005</td>
<td></td>
<td>53</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003</td>
<td></td>
<td>12</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003@4b013c76</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008.verifyMedicinesPage()'><b>verifyMedicinesPage</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008</td>
<td></td>
<td>36</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004</td>
<td></td>
<td>13</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004@33065d67</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007.verifySTA()'><b>verifySTA</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007</td>
<td></td>
<td>75</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002.globalSearchFuntionality()'><b>globalSearchFuntionality</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002</td>
<td></td>
<td>24</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006</td>
<td></td>
<td>21</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002</td>
<td></td>
<td>38</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002@6bbe85a8</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001.verifyMenuComponents()'><b>verifyMenuComponents</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001</td>
<td></td>
<td>5</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001.verifyFooter()'><b>verifyFooter</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001</td>
<td></td>
<td>3</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001.verifyHeader()'><b>verifyHeader</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001</td>
<td></td>
<td>5</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001@74d1dc36</td></tr>
<tr>
<td title='com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005</td>
<td></td>
<td>14</td>
<td>com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005@742ff096</td></tr>
</table><p>
</body>
</html>