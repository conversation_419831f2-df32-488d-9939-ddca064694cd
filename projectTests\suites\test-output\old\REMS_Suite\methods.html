<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>REMS_Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="956e91">  <td>25/05/13 20:33:41</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@956790150</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/05/13 20:33:24</td>   <td>-17062</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_YescartaTecartusREMS_Site.invokeURL()[pri:1, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">invokeURL</td> 
  <td>Thread2@1961085201</td>   <td></td> </tr>
<tr bgcolor="7dd167">  <td>25/05/13 20:32:55</td>   <td>-46018</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-3@956790150</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:32:55</td>   <td>-46016</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-3@956790150</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:33:44</td>   <td>3126</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@429202201</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/05/13 20:33:30</td>   <td>-10554</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.invokeURL()[pri:1, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">invokeURL</td> 
  <td>Thread3@429202201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:32:55</td>   <td>-46017</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-2@429202201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:34:23</td>   <td>42403</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@429202201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:32:55</td>   <td>-46015</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@1961085201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:34:27</td>   <td>46727</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@429202201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:33:41</td>   <td>430</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1961085201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:35:50</td>   <td>129040</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1961085201</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/05/13 20:33:22</td>   <td>-18341</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Download_Resources_From_YescartaTecartusREMS.invokeURL()[pri:1, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">invokeURL</td> 
  <td>Thread1@956790150</td>   <td></td> </tr>
<tr bgcolor="7dd167">  <td>25/05/13 20:32:55</td>   <td>-46017</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@1961085201</td>   <td></td> </tr>
<tr bgcolor="7dd167">  <td>25/05/13 20:32:55</td>   <td>-46020</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-2@429202201</td>   <td></td> </tr>
<tr bgcolor="7dd167">  <td>25/05/13 20:35:53</td>   <td>131977</td> <td title="&lt;&lt;CRAFTLiteTestCase.tearDownTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&lt;&lt;tearDownTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@809300666</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:33:41</td>   <td>430</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1961085201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:35:50</td>   <td>129040</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@1961085201</td>   <td></td> </tr>
<tr bgcolor="7dd167">  <td>25/05/13 20:32:54</td>   <td>-46314</td> <td title="&gt;&gt;CRAFTLiteTestCase.setUpTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&gt;&gt;setUpTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@809300666</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:33:44</td>   <td>3126</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@429202201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:34:11</td>   <td>30104</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@956790150</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/05/13 20:33:44</td>   <td>3126</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.toVerifyCriticalComponentsYescartaTecartusREMSSite()[pri:2, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">toVerifyCriticalComponentsYescartaTecartusREMSSite</td> 
  <td>Thread3@429202201</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/05/13 20:33:41</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Download_Resources_From_YescartaTecartusREMS.toVerifyYescartaTecartusREMSSite()[pri:2, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">toVerifyYescartaTecartusREMSSite</td> 
  <td>Thread1@956790150</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:35:52</td>   <td>131082</td> <td title="&lt;&lt;BaseTest.afterSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&lt;&lt;afterSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@809300666</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/05/13 20:33:41</td>   <td>431</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_YescartaTecartusREMS_Site.toVerifyYescartaTecartusREMSSite()[pri:2, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">toVerifyYescartaTecartusREMSSite</td> 
  <td>Thread2@1961085201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:35:52</td>   <td>131019</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@1961085201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:33:41</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@956790150</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:34:11</td>   <td>30104</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@956790150</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:34:23</td>   <td>42403</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@429202201</td>   <td></td> </tr>
<tr bgcolor="956e91">  <td>25/05/13 20:34:36</td>   <td>55349</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@956790150</td>   <td></td> </tr>
</table>
