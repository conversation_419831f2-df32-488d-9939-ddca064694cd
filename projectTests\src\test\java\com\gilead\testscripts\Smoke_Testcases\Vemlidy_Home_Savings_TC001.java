package com.gilead.testscripts.Smoke_Testcases;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;


public class Vemlidy_Home_Savings_TC001 extends BaseTest {

	CommonFunctions objCommonFunctions;


	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifySubmenuInWebPage() throws Exception {
		try {
			objCommonFunctions.clickSubMenu();
			objCommonFunctions.clickSubMenu();
			objCommonFunctions.validateComponentExists();
			objCommonFunctions.verifyConfirmationPopup();
		} finally {
			checkErrors();
		}
	}
}
