# Framework settings file

# General Settings
ProjectName=Digital Experience
BuildName=Sprint1
DefaultDataSheet=General_Data
DataReferenceIdentifier=#
DateFormatString=dd-MMM-yyyy hh:mm:ss:SSS a
RSIUserPW=FsgxUj0NFeWyB7hWsDP0tg==
RSIPeerUserPW=fE8Mnlwt0MKoHz3CG0LGyA==
NonRSIUserPW=K0aSnhsc1z8WRPOQkA6iNQ==

FileLockTimeOut=12
#Framework
Approach=KeywordDriven
#KeywordDriven
#ModularDriven

# API Settings - Environment
ENV=INT

# Object Heal Configuration
# Please reach CRAFTHelpDesk to Opt for this Feature
StartCapturingObjects=False
HealObjects=False
ForceHeal=False


# DB Configuration
SaveDataToDB=False
DBHost=localhost
DBPort=27017

# Klov Dashboard Integration
GenerateKlov=False
KlovURL=http://localhost:8080

# Browser Settings
InternetExplorerDriverPath=C:\\Javalibs\\Selenium\\Browser Drivers\\IEDriverServer.exe
ChromeDriverPath=\\src\\test\\resources\\drivers\\chromedriver.exe
GeckoDriverPath=C:\\Javalibs\\Selenium\\Browser Drivers\\geckodriver.exe
EdgeDriverPath=\\target\\test-classes\\drivers\\msedgedriver.exe
PhantomJSPath=C:\\Javalibs\\Selenium\\Browser Drivers\\phantomjs-2.0.0-windows\\bin\\phantomjs.exe
ChangeDefaultDownloadPath=Yes
DownloadPath=externalFiles
DisablePromptForDownload=Yes
AllowMultipleDownloads=Yes
DisableChromePDFViewer=Yes
EnableOpenPDFExternally=Yes
MuteAudio=Yes
ManulBrowserRequired=No
OpenIncognito=Yes

# General Execution Settings 
OnError=NEXT_ITERATION

# Allocator Execution Settings (Applicable only during a batch execution using the Allocator)
RunConfiguration=Sanity
NumberOfThreads=1

# Remote Execution & Grid Settings (Applicable only if the ExecutionMode is "REMOTE" or "GRID")
RemoteUrl=http://**************:4444/wd/hub
Grid1Url=**************
Grid2Url=**************

# list of nodes those are attached to docker swarm
SwarmNodes=**************,**************,**************,**************,**************
LeaderNodes=**************,**************,**************

#ports
Grid1Port=4444
Grid2Port=8888

# Report Settings
# LogLevel ranges between 0 to 5, with 0 being minimal reporting and 5 being highly detailed reporting
LaunchCRAFTCentral=False
CRAFTCentralURL=http://localhost:3000
LogLevel=4
TestNgReportPath=test-output
HtmlReport=True
IncludeTestDataInReport=False
TakeScreenshotFailedStep=True
TakeScreenshotPassedStep=True
ConsolidateScreenshotsInWordDoc=False
ReportsTheme=CUSTOM
# AUTUMN, CUSTOM ,CLASSIC, MYSTIC, OLIVE 

# User-defined Settings
ApplicationUrl=http://newtours.demoaut.com/
ApplicationUrlRWD=https://skinnyties.com/

#JIRA Integration Config Details
UpdateInJira=False
Jira_Url=https://cognizantmobilecoe.atlassian.net
Jira_UserName=admin
Jira_Password=password
Jira_Project_ID=10000
Jira_Issue_Reporter=tester1
Jira_Issue_Type=Bug

#Default Settings
DefaultExecutionMode=LOCAL
DefaultBrowser=CHROME
DefaultPlatform=WINDOWS

PageLoadTimeout=120
InvisibilityTimeout=80
MinObjectSyncTimeout=10
ObjectSyncTimeout=120
LoadingWindowTimeOut=60
UploadControlTimeout=30
ObjectSyncTimeout=100
StaleTimeOut=5
TestTimeOut=120
ShadowObjectLoadTimeOut=3000
MailNotificationTimeout=500000

NoTimeout=0

RemotePageLoadTimeout=180
RemoteInvisibilityTimeout=100
RemoteMinObjectSyncTimeout=60
RemoteObjectSyncTimeout=180
RemoteLoadingWindowTimeOut=180
RemoteUploadControlTimeout=60
RemoteObjectSyncTimeout=120
RemoteStaleTimeOut=20
RemoteTestTimeOut=180
ReduceViewPortSize=200
Scope=Regression
Equalto==
Semicolon=;
Exclamation=!
StepLevelScreenshotsFolderName=Step
ScreenshotImageFormat=png
ScreenshotName=Screenshot
ScreenshotNameTimeFormat=HH_mm_ss
ScreenshotFileNameDelimiter=_
ScreenshotType=webdriver

ScreenshotPath=

# Re-execution count of failed test cases automatically
RetryFailExecutionCount=1

#Thread count for TestRunner
TestRunnerThreads=25

# Boolean flag to integration with ALM default is true 
AlmIntegration=false
DetailedReports=Yes
# String value of test phase at run level 
TestPhase=Non Validation

#Applitools Info
ApiKey=XJAZOPuvMnnMRDmp67oPT1BpU205dPH4e7EY103pD1mtw110
#ApiKey=RJYUEzMEt1wDLIs4zOdmO103MxIjvXuNLJuwRn0ftS3ZM110
ServerUrl=https://gileadeyes.applitools.com
ReadURLsFromExcel=\\src\\test\\resources\\Datatables\\ListOfURLs.xlsx
#For dependency test cases we should go with 'Sequence' with Single thread
#List of suites to be executed in sequence with comma separator ex: EntireSuite,cmdb
SequenceSuites=
#SequenceSuites=EntireSuite_Suite1
#Browser Settings
ChangeDefaultDownloadPath=Yes
allowMultipleDownloads=true
DownloadPath=externalFiles
DisablePromptForDownload=Yes
DisableChromePDFViewer=Yes
EnableOpenPDFExternally=Yes
MuteAudio=Yes
DefaultLanguageEnglish=Yes
DisbaleNotificationsANDPopUps=Yes
#If Manual browser is required means all the above settings won't work
ManualBrowserRequired=No
Incognito=No
EnableClipboard=No
DetailedReports=Yes
ScreenshotType=webdriver
#This property is used to adjust the Zoom of the browser
ZoomOperationRequired=Yes
#If ZoomOperationRequired is Yes, The following Zoom Percentage to be provided Examples: 1 - 100%, 0.8 - 80%, 1.2 - 120%
ZoomPercentage=1.2
#Multiple Screenshots - This property value should be adjusted for Saleseforce application
ReduceViewPortSize=0
DatatableCoAuthoringEnabled=false

#values for result integration - AZURE,GTEST,DEFAULT. DEFAULT- Local Execution.
ResultIntegration=DEFAULT
#Azure Integration
AzureOrgURL=https://dev.azure.com/Gilead-IT-Digital-Experience/
AzureProject =Kite%20Commercial
AzurePersonalAccessToken =28raMzkxXJ10fepVF5cAM7z2PJbEENFI8H0ixlE9sgTnTl7ijYSIJQQJ99BCACAAAAAqVanvAAASAZDO3p5R
AzureExecutorName =Balaji Jayakumar (Contractor)
UpdateResultsInAzure=true
AzureTestPlan=Smoke CI CD Test Suite
AzureExecutionTestSuite=Yescarta.com