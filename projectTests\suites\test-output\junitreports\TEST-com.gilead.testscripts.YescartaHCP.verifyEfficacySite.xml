<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.YescartaHCP.verifyEfficacySite" tests="2" failures="1" timestamp="2025-04-15T12:56:25 IST" time="88.713" errors="0">
  <testcase name="invokeURL" time="5.907" classname="com.gilead.testscripts.YescartaHCP.verifyEfficacySite"/>
  <system-out/>
  <testcase name="verifyEfficacyMenuAndSite" time="82.806" classname="com.gilead.testscripts.YescartaHCP.verifyEfficacySite">
    <failure type="com.gilead.config.FrameworkAssertion" message="Error - ZUMA-7 Trial Sub-Menu is not found in the page: &quot;https://www.yescartahcp.com/large-b-cell-lymphoma&quot; even after waiting for 10 Seconds">
      <![CDATA[com.gilead.config.FrameworkAssertion: Error - ZUMA-7 Trial Sub-Menu is not found in the page: "https://www.yescartahcp.com/large-b-cell-lymphoma" even after waiting for 10 Seconds
at com.gilead.reports.Report.updateTestLog(Report.java:423)
at com.gilead.maintenance.ALMFunctions.ThrowException(ALMFunctions.java:252)
at com.gilead.maintenance.WebDriverUtil.waitUntilElementLocated(WebDriverUtil.java:304)
at com.gilead.maintenance.CommonActionsAndFunctions.clickByJS(CommonActionsAndFunctions.java:473)
at businesscomponents.CommonFunctions.verifyHeaderAndImage(CommonFunctions.java:4464)
at businesscomponents.CommonFunctions.verifyEfficacySite(CommonFunctions.java:4593)
at com.gilead.testscripts.YescartaHCP.verifyEfficacySite.verifyEfficacyMenuAndSite(verifyEfficacySite.java:30)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- verifyEfficacyMenuAndSite -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.YescartaHCP.verifyEfficacySite -->
