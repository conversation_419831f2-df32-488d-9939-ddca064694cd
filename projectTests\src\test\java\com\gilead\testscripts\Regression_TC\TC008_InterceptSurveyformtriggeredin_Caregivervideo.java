package com.gilead.testscripts.Regression_TC;

import org.testng.annotations.AfterClass;
import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}",
testname = "TC01_HCP_Patient_Enroll_CM_POPending",
testlab = "${testlab}",
testset = "${testset}" )


public class TC008_InterceptSurveyformtriggeredin_Caregivervideo extends BaseTest{


	CommonFunctions objCommonFunctions;
	


	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			
			objCommonFunctions.invokeUrl();
			objCommonFunctions.closeCookies();
		} finally {
			checkErrors();
		}
	}
	@Test(priority = 2)
	public void navigateToMenu() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.navigateToMenu("For caregivers", "What can I expect",driver.getTitle());
		} finally {
			checkErrors();
		}
	}
	@Test(priority = 3)
	public void videoValidation_Playback() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.videoPlay("Taking care of yourself video completion");
		} finally {
			checkErrors();
		}
	}
	@Test(priority = 4)
	public void verifyInterceptForm() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.surveyFormCheck("Good");
			objCommonFunctions.surveyFormCheck("Has practical advice!Links to helpful resources");
			objCommonFunctions.surveyFormCheck("Come back later to learn more!Print or download material for reference");
			objCommonFunctions.surveyFormCheck("I am caregiver to someone with blood cancer");
			objCommonFunctions.verifyObjectState("Thank you for sharing your experience","message!exist",driver.getTitle());
			
		} finally {
			checkErrors();
		}
	}
	@AfterClass
	public void resetHashMap()
	{
		CommonFunctions.resetHashMap();
	}



}
