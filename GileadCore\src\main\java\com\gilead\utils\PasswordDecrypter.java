package com.gilead.utils;

import java.security.spec.AlgorithmParameterSpec;
import java.security.spec.KeySpec;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;
import org.apache.commons.codec.binary.Base64;

public class PasswordDecrypter{

	static Cipher ecipher;
    static Cipher dcipher;
    // 8-byte Salt
    static byte[] salt = {
        (byte) 0xA9, (byte) 0x9B, (byte) 0xC8, (byte) 0x32,
        (byte) 0x56, (byte) 0x35, (byte) 0xE3, (byte) 0x03
    };
    // Iteration count
    static int iterationCount = 19;
    private static String key="dqi014atnqpeh1940auzm9";
		
	public static String decrypt(String encryptedText)
		     {
		try{
		      //Key generation for enc and desc
		        KeySpec keySpec = new PBEKeySpec(key.toCharArray(), salt, iterationCount);
		        SecretKey key = SecretKeyFactory.getInstance("PBEWithMD5AndDES").generateSecret(keySpec);        
		         // Prepare the parameter to the ciphers
		        AlgorithmParameterSpec paramSpec = new PBEParameterSpec(salt, iterationCount);
		        //Decryption process; same key will be used for decr
		        dcipher=Cipher.getInstance(key.getAlgorithm());
		        dcipher.init(Cipher.DECRYPT_MODE, key,paramSpec);
		        byte[] enc = new Base64().decode(encryptedText);
		        byte[] utf8 = dcipher.doFinal(enc);
		        String charSet="UTF-8";     
		        String plainStr = new String(utf8, charSet);
		        return plainStr;
		}
		catch(Exception e)
		{
			return "null";
		}
		}
	
}
