package com.gilead.testscripts.Smoke_Testcases;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;

public class HstvPRO_Homepage_TC001 extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifySubmenuInHeader() {
		try {
			objCommonFunctions.clickSubMenuLoc();
			objCommonFunctions.clickSubMenuLoc();
			objCommonFunctions.clickSubMenuLoc();
			
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 3)
	public void checkLinksInWebPage() {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();

		} finally {
			checkErrors();
		}
	}

}
