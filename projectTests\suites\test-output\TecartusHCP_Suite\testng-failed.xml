<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite guice-stage="DEVELOPMENT" name="Failed suite [TecartusHCP_Suite]">
  <test thread-count="5" name="Test under EntireSuite_Suite1(failed)" parallel="classes">
    <parameter name="RunID" value="0"/>
    <classes>
      <class name="com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website">
        <methods>
          <include name="invokeURL"/>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
        </methods>
      </class> <!-- com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website -->
    </classes>
  </test> <!-- Test under EntireSuite_Suite1(failed) -->
</suite> <!-- Failed suite [TecartusHCP_Suite] -->
