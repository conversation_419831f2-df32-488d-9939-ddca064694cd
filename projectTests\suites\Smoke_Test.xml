<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="Surefire suite">


	<test name="Test under Smoke_Test_Suite" thread-count="20"
		parallel="classes">
		<parameter name="RunID" value="0" />
		<!--<test name="Test under EntireSuite_Suite1" thread-count="1" > -->
		<classes>
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_at" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_fi" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_gr" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_ie" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_pl" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_es" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_cz" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_fr" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_de" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_it" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_nl" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_no" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_pt" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_se" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_dk" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_uk" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_au" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_il" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_be" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_ch" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_sk" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_ca" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_sg" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_sa" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_lu" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_br" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_jp" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.KiteKonnectPage_Global" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.letschatcart" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.yescarta" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.opscars" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.yescartahcp" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.tecartus" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.yescartatecartusrems" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.kitepharma" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.harvoni" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.trodelvy" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.hepchope" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.carthope" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.asegua" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.epclusa" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.hcpepclusa" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.hcpharvoni" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.biktarvy" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.teacartushcp" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.vemlidy" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.agencygilead" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.mysupportpath" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.hstvpro" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gileadpriceinfo" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gileadmarketaccess" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gileadclinicaltrails" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.healthysexuals" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gileadadvancingaccess" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.storiesgilead" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.trodelvyhcp" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_uk" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_kr" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_jp" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_ca" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_be" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_gr" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_at" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_se" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gileadsciences_de" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gileadswitzerland_ch" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gileadisrael_il" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_fr" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_es" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_tw" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_it" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_tr" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_hk" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gilead_au" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gnet" />
			<class
				name="com.gilead.testscripts.Smoke_CICD.gnet_knet" />
		</classes>

	</test>
</suite>
