name: Trigger Main Repo Update

on:
  push:
    branches:
      - main  

jobs:
  trigger-update:
    runs-on: ubuntu-latest
     
    steps:
    - name: Check out the repository
      uses: actions/checkout@v2
      
    - name: Install API response parser
      run: |
         sudo apt-get update
         sudo apt-get install -y jq
         
    - name: Retrive User Details
      run: | 
          user_detail1=$(curl -s -H "Authorization: token ${{ secrets.GIT_TOKEN }}" https://api.github.com/user)
          echo "user_name1=$(echo $user_detail1 | jq -r '.login')" >> $GITHUB_ENV

          user_detail2=$(curl -s -H "Authorization: token ${{ secrets.GITSECRETUSER2 }}" https://api.github.com/user)
          echo "user_name2=$(echo $user_detail2 | jq -r '.login')" >> $GITHUB_ENV
          
    - name: Set Access Token
      run: |
          if [ "${{ github.actor }}" = "${{ env.user_name1 }}" ]; then
            echo "token=${{ secrets.GIT_TOKEN }}" >> $GITHUB_ENV
          elif [ "${{ github.actor }}" = "${{ env.user_name2 }}" ]; then
            echo "token=${{ secrets.GITSECRETUSER2 }}" >> $GITHUB_ENV
          fi
    - name: Access the Project Details files
      run: |
         curl -H "Authorization: token ${{ env.token }}" \
         -H "Accept:application/vnd.github.v3.raw" \
         -O \-L "https://raw.githubusercontent.com/Gilead-IT-Quality-Engineering/Miscellaneous/main/GileadNGProjects.json"
         
    - name: Trigger workflow in EventManagement
      run: |
        projectNames=$(cat GileadNGProjects.json | jq -r '.projects[]')
        for projectName in $projectNames; do
        curl -X POST -H "Authorization: token ${{ env.token }}" \
          -H "Accept: application/vnd.github.v3+json" \
          https://api.github.com/repos/Gilead-IT-Quality-Engineering/${projectName}/dispatches \
          -d '{"event_type":"Notify_Submodule_Update"}'
          done