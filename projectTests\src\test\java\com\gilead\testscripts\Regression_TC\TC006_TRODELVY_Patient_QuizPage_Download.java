package com.gilead.testscripts.Regression_TC;

import java.io.File;

import org.testng.annotations.AfterClass;
import org.testng.annotations.Test;

import com.gilead.base.BaseTest;
import com.gilead.pageobjects.Common;
import com.gilead.utils.Util;

import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class TC006_TRODELVY_Patient_QuizPage_Download extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.invokeUrl();
			objCommonFunctions.closeCookies();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyFormFill() throws Exception {
		try {
			//objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.reqFormFill("reqFormFill", "FormCheckDownload");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 3)
	public void checkDownloadFunctionality() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			String strFileName = "Side-Effect-b0b50a7e-a659-9f15-b6fc-a3c3cae39393.pdf";
			String strFilePath =System.getProperty("user.dir") + Util.getFileSeparator() + strFileName;
			File downloadedFile = new File(strFilePath);

			objCommonFunctions.checkFileDownloaded(downloadedFile, strFilePath, strFileName);
		} finally {
			checkErrors();
		}
	}

	@AfterClass
	public void resetHashMap() {
		CommonFunctions.resetHashMap();
	}

}
