package com.gilead.base;

import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Properties;
import java.util.Timer;
import java.util.TimerTask;
import java.util.function.Predicate;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.gilead.config.FrameworkAssertion;
import com.gilead.maintenance.Settings;
import com.gilead.reports.Status;
import com.jayway.jsonpath.JsonPath;

public final class GridServiceEvaluator {

	// private FrameworkParameters frameworkParameters =
	// FrameworkParameters.getInstance();
	private Properties properties = Settings.getInstance();
	private String swarmNodes = properties.getProperty("SwarmNodes");
	// private static String leaderNodes = properties.getProperty("LeaderNodes");

	private final List<String> swarmList = Arrays.asList(swarmNodes.split(","));
	// private final static List<String> leaderList =
	// Arrays.asList(leaderNodes.split(","));
	private final String GRID1_PORT = properties.getProperty("Grid1Port");
	private final String GRID2_PORT = properties.getProperty("Grid2Port");
	private final String GRID1 = String.format("http://%s:%s", properties.getProperty("Grid1Url"), GRID1_PORT);
	private final String GRID2 = String.format("http://%s:%s", properties.getProperty("Grid2Url"), GRID2_PORT);
	
	private final String maxSessionQuery = "{\"query\":\"{ grid { maxSession } }\"}";
	private final String sessionInfo = "{\"query\":\"{ sessionsInfo { sessions { id, startTime, uri, nodeId, nodeId, sessionDurationMillis } } }\"}";
	private final String sessionQueueInfo = "{\"query\":\"{ sessionsInfo { sessionQueueRequests } }\"}";
	
	private final int DEFAULT_REQUEST_SIZE = 3;
	private final String SUCCESS = "success";
	private final String FAILURE = "failure";
	private final String ERROR = "error";

	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());
	//private static final Logger logger = LogManager.getLogger("loggers");

	public GridServiceEvaluator() {

	}

	public synchronized String getGridReachableUrl() {
		String grid = RunContext.getRunContext().getSeleniumTestParameters().getRemoteGrid();

		GridPredicate<String> gridPredicate = null;
		if (grid.equalsIgnoreCase("Grid1")) {
			gridPredicate = new GridPredicate<String>();
			final String BASEURI = String.format("http://%s:%s/graphql", properties.getProperty("Grid1Url"),
					GRID1_PORT);
			if (gridPredicate.test(BASEURI)) {
				logger.info(String.format("reached %s host at %s", grid, GRID1));
				return GRID1;
			} else {
				logger.error(String.format("not able to reach the %s host '%s', checking other host", grid, GRID1));
				//swarmList.remove(properties.getProperty("Grid1Url"));
				String reachableUrl = getReachableUrl(grid, swarmList);
				return reachableUrl;
			}
		} else if (grid.equalsIgnoreCase("Grid2")) {
			gridPredicate = new GridPredicate<String>();
			final String BASEURI = String.format("http://%s:%s/graphql", properties.getProperty("Grid2Url"),
					GRID2_PORT);
			if (gridPredicate.test(BASEURI)) {
				logger.info(String.format("reached %s host at %s", grid, GRID2));
				return GRID2;
			} else {
				logger.error(String.format("not able to reach the %s host '%s', checking other host", grid, GRID2));
				//swarmList.remove(properties.getProperty("Grid2Url"));
				String reachableUrl = getReachableUrl(grid, swarmList);
				return reachableUrl;
			}
		}

		return null;
	}

	private synchronized String getReachableUrl(String grid, List<String> urlList) {

		if (grid.equalsIgnoreCase("Grid1")) {
			Predicate<String> gp1 = new GridPredicate<String>();
			Optional<String> reachableUrl = urlList.stream().map(n -> String.format("http://%s:%s/graphql", n, GRID1_PORT))
					.filter(gp1).findFirst();

			if (reachableUrl.isPresent()) {
				String reachableHost = reachableUrl.get();
				reachableHost = reachableHost.replaceAll("graphql", "wd/hub");
				logger.info(String.format("redirecting %s remote host to %s", GRID1, reachableHost));
				return reachableHost;
			} else {
				logger.info(String.format("%s not able to reach in all swarm nodes", "Grid1"));
				Predicate<String> gp3 = new GridPredicate<String>();
				reachableUrl = urlList.stream().map(n -> String.format("http://%s:%s/graphql", n, GRID2_PORT)).filter(gp3)
						.findFirst();

				if (reachableUrl.isPresent()) {
					String reachableHost = reachableUrl.get();
					reachableHost = reachableHost.replaceAll("graphql", "wd/hub");
					logger.info(String.format("redirecting test case to alternate hub execute on %s", reachableHost));
					return reachableHost;
				} else {
					RunContext.getRunContext().getReport().updateTestLog("Verify hubs reachable", 
							"Both hub should be reachable", "Both hubs are not reachable", Status.WARNING);
					throw new FrameworkAssertion("Both hubs are not reachable");
					// both hub are not available
					// terminate execution
				}

			}
		} else if (grid.equalsIgnoreCase("Grid2")) {

			Predicate<String> gp2 = new GridPredicate<String>();
			Optional<String> reachableUrl = urlList.stream().map(n -> String.format("http://%s:%s/graphql", n, GRID2_PORT))
					.filter(gp2).findFirst();

			if (reachableUrl.isPresent()) {
				String reachableHost = reachableUrl.get();
				reachableHost = reachableHost.replaceAll("graphql", "wd/hub");
				logger.info(String.format("redirected %s remote host to %s", GRID2, reachableHost));
				return reachableHost;
			} else {
				logger.info(String.format("not able to reach in all %s swarm nodes", "Grid1"));
				Predicate<String> gp3 = new GridPredicate<String>();
				reachableUrl = urlList.stream().map(n -> String.format("http://%s:%s/graphql", n, GRID1_PORT)).filter(gp3)
						.findFirst();

				if (reachableUrl.isPresent()) {
					String reachableHost = reachableUrl.get();
					reachableHost = reachableHost.replaceAll("graphql", "wd/hub");
					logger.info(String.format("redirecting test case to alternate hub execute on %s", reachableHost));
					return reachableHost;
				} else {
					RunContext.getRunContext().getReport().updateTestLog("Verify hubs reachable", 
							"Both hub should be reachable", "Both hubs are not reachable", Status.WARNING);
					throw new FrameworkAssertion("Both hubs are not reachable");
					// both hub are not available
					// terminate execution
					//System.exit(0)
				}
			}
		}
		return null;
	}
	
	public synchronized String isQueueAvailable() {
		
		int maxSessions = 0, currentSessions = 0, sessionRequest = 0;
		String jsonString = "";
		
		String grid = RunContext.getRunContext().getSeleniumTestParameters().getRemoteGrid();
		if (grid.equalsIgnoreCase("Grid1")) {
			jsonString = executeQuery(GRID1+"/graphql", maxSessionQuery);
			if(jsonString == null) return ERROR;
			maxSessions = JsonPath.read(jsonString,"$.data.grid.maxSession");
			jsonString = executeQuery(GRID1+"/graphql", sessionInfo);
			if(jsonString == null) return ERROR;
			currentSessions = JsonPath.read(jsonString, "$.data.sessionsInfo.sessions.length()");
			jsonString = executeQuery(GRID1+"/graphql", sessionQueueInfo);
			if(jsonString == null) return ERROR;
			sessionRequest = JsonPath.read(jsonString, "$.data.sessionsInfo.sessionQueueRequests.length()");
			if ((currentSessions < maxSessions) && (sessionRequest <= DEFAULT_REQUEST_SIZE)) {
				logger.info(String.format("Current session count is %s", currentSessions));
				return SUCCESS;
			} else {
				logger.info(String.format("Max session count reached with %s and queue size is %s", currentSessions, sessionRequest));
				return FAILURE;
			}
			
		} else if (grid.equalsIgnoreCase("Grid2")) {
			jsonString = executeQuery(GRID2+"/graphql", maxSessionQuery);
			if(jsonString == null) return ERROR;
			maxSessions = JsonPath.read(jsonString,"$.data.grid.maxSession");
			jsonString = executeQuery(GRID2+"/graphql", sessionInfo);
			if(jsonString == null) return ERROR;
			currentSessions = JsonPath.read(jsonString, "$.data.sessionsInfo.sessions.length()");
			jsonString = executeQuery(GRID2+"/graphql", sessionQueueInfo);
			if(jsonString == null) return ERROR;
			sessionRequest = JsonPath.read(jsonString, "$.data.sessionsInfo.sessionQueueRequests.length()");
			if ((currentSessions < maxSessions) && (sessionRequest <= DEFAULT_REQUEST_SIZE)) {
				return SUCCESS;
			} else {
				logger.info(String.format("Max session count reached with %s and queue size is %s", currentSessions, sessionRequest));
				return FAILURE;
			}
		}
		
		return ERROR;
	}

	public String executeQuery(String url, String query) {
		
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost postMethod = new HttpPost(url);
		StringEntity input = new StringEntity(query, ContentType.APPLICATION_JSON);
		postMethod.setEntity(input);
		
		int hardTimeout = 5; // seconds
		TimerTask task = new TimerTask() {
			@Override
			public void run() {
				if (postMethod != null) {
					postMethod.abort();
				}
			}
		};
		new Timer(true).schedule(task, hardTimeout * 1000);
		
		HttpResponse res;
		try {
			res = httpClient.execute(postMethod);
			HttpEntity entity = res.getEntity();
			String jsonString = EntityUtils.toString(entity);
			
			return jsonString;
			
			
		} catch (ClientProtocolException e) {
			
			e.printStackTrace();
		} catch (IOException e) {
			
		}

		return null;
	}
	
	final class GridPredicate<E> implements Predicate<String> {
		
		final String bodyQuery = "{\"query\": \"{ nodesInfo { nodes { status } } }\"}";

		@Override
		public boolean test(String url) {
			boolean isGridReachable = false;
			CloseableHttpClient httpClient = HttpClients.createDefault();
			HttpPost postMethod = new HttpPost(url);
			StringEntity input = new StringEntity(bodyQuery, ContentType.APPLICATION_JSON);
			postMethod.setEntity(input);
			
			int hardTimeout = 5; // seconds
			TimerTask task = new TimerTask() {
				@Override
				public void run() {
					if (postMethod != null) {
						postMethod.abort();
					}
				}
			};
			new Timer(true).schedule(task, hardTimeout * 1000);

			HttpResponse res;
			try {
				res = httpClient.execute(postMethod);
				isGridReachable = ((res.getStatusLine().getStatusCode() == 200)) ? true : false;
			} catch (ClientProtocolException e) {
				isGridReachable = false;
				//e.printStackTrace();
			} catch (IOException e) {
				isGridReachable = false;
			}

			if (isGridReachable) {
				return true;
			} else {
				return false;
			}
		}

	}
}
