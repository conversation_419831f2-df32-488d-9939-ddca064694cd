<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="24" name="Test under EntireSuite_Suite1" tests="18" failures="1" timestamp="2025-04-30T18:03:57 IST" time="1537.242" errors="0">
  <testcase name="invokeURL" time="20.725" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Headers_On_YescartaHCP"/>
  <testcase name="invokeURL" time="21.927" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Footer_On_YescartaHCP"/>
  <testcase name="toVerifyFooter" time="135.107" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Footer_On_YescartaHCP"/>
  <testcase name="invokeURL" time="5.385" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Body_On_YescartaHCP_HomePage"/>
  <testcase name="toVerifyBodyOnHomePage" time="277.593" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Body_On_YescartaHCP_HomePage"/>
  <testcase name="invokeURL" time="6.616" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Manufacturing_And_Process_page"/>
  <testcase name="toVerifyManufacturingAndProcessPage" time="271.669" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Manufacturing_And_Process_page"/>
  <testcase name="invokeURL" time="10.982" classname="com.gilead.testscripts.YescartaHCP.To_Verify_PatientID_Site"/>
  <testcase name="toVerifyHeaders" time="800.898" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Headers_On_YescartaHCP">
    <failure type="com.gilead.config.FrameworkAssertion" message="&amp;apos;RESOURCES&amp;apos; Section is not displayed">
      <![CDATA[com.gilead.config.FrameworkAssertion: 'RESOURCES' Section is not displayed
at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
at com.gilead.testscripts.YescartaHCP.To_Verify_Headers_On_YescartaHCP.toVerifyHeaders(To_Verify_Headers_On_YescartaHCP.java:32)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames]]>
    </failure>
  </testcase> <!-- toVerifyHeaders -->
  <testcase name="invokeURL" time="4.741" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Safety_Site"/>
  <testcase name="toVerifyPatientIDSite" time="200.628" classname="com.gilead.testscripts.YescartaHCP.To_Verify_PatientID_Site"/>
  <testcase name="invokeURL" time="5.093" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy3L_Page"/>
  <testcase name="toVerifySafetySite" time="221.211" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Safety_Site"/>
  <testcase name="invokeURL" time="7.469" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy2L_Page"/>
  <testcase name="toVerifyEfficacy3LPage" time="274.303" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy3L_Page"/>
  <testcase name="invokeURL" time="4.826" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy_HomePage"/>
  <testcase name="toVerifyEfficacy2LPage" time="278.829" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy2L_Page"/>
  <testcase name="toVerifyEfficacyHomePage" time="266.169" classname="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy_HomePage"/>
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
