id: testng_annotations_alm
message: "Ensure TestNG classes must have ALMTarget annotation with testplan, testname, testlab and testset values"
language: java
severity: warning
rule:
  kind: class_declaration
  has:
    stopBy: end
    kind: annotation
    regex: ALMTarget
    has:
      kind: annotation_argument_list
      not:
        all:
        - {has: {stopBy: end, kind: identifier, regex: testplan}}
        - {has: {stopBy: end, kind: identifier, regex: testname}}
        - {has: {stopBy: end, kind: identifier, regex: testlab}}
        - {has: {stopBy: end, kind: identifier, regex: testset}}
        
  inside:
    stopBy: end
    kind: program
    has:
      stopBy: end
      kind: package_declaration
      has:
        kind: scoped_identifier
        regex: com.gilead.testscripts