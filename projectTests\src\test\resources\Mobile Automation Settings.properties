# Mobile Automation settings file

# Appium URL
AppiumURL=http://127.0.0.1:4723/wd/hub
# For Appium Parallel Execution use below URL
#AppiumURL=http://*************:4444/wd/hub

# Android Device and Application Details
Application_Package_Name=com.experitest.ExperiBank
Application_MainActivity_Name=com.experitest.ExperiBank.LoginActivity
InstallApplicationInDevice=False
AndroidApplicationPath=C:\\SampleApps\\Eribank.apk
iPhoneApplicationPath=C:\\SampleApps\\TestMunk.ipa
iPhoneBundleID=com.mcoe.TestmunkDemo
ResetApp=False

# Perfecto MobileCloud Settings
PerfectoHost=https://mobiletestlab.cognizant.com/nexperience/perfectomobile/wd/hub
PerfectoHostDefault=mobiletestlab.cognizant.com
PerfectoUser=<EMAIL>
PerfectoPassword=XXXX
PerfectoAndroidIdentifier=EriBank
PerfecttoIosBundleID=TestMunk
PerfectoReportGeneration=False

# Perfecto Desktop Configuration
Location=US East
Resolution=1280x1024

# Sauce MobileCloud Settings 
SauceHost=http://USERNAME:<EMAIL>:80/wd/hub
SauceAndroidIdentifier=sauce-storage:eribank.apk
SauceIosBundleID=sauce-storage:bmi-calculator.app.zip
SaucelabAppiumDriverVersion=1.6.4

# Test Object Settings
TestObjectHost=http://us1.appium.testobject.com/wd/hub
AccessKeyAndroid=C107A07087DA49CCB6D25E9DC45DB6FA
AccessKeyiOS=EFDB25AE68244761BF5A5D5EB725BD1F
TestObjectAppiumVersion=1.7.2
TestObjectAndroidAppKey=1
TestObjectiOSAppKey=1

# BrowserStack Cloud Settings 
BrowserStackHost=http://USERNAME:<EMAIL>/wd/hub
BrowserStackScreenResolution=800x600

#Mint Cloud Settings 
MintHost=https://www.mintdeviceinchnp1.cognizant.com/wd/hub
MintUsername=<EMAIL>
MintPassword=XXXXXXXX
MintServiceRequestId=XX-XXXXX-XXXXXX-XXXXXXX
MintAndroidApplicationName=mint-storage:<EMAIL>/eribank.apk
MintiOSApplicationName=mint-storage:<EMAIL>/Eribank-resigned-resigned.ipa

# fastest Cloud Settings
FastestHost=http://fastest.cognizant.com/wd/hub
FastestUserame=<EMAIL>
FastestPassword=XXXXXXXXXXXXXXXXXXXXXXXXXX
FastestServiceRequestId=XX-XXXXX-XXXXXX-XXXXXXX

#Default Settings
DefaultDevice=Apple_iPad_Air
DefaultMobileExecutionPlatform=IOS
DefaultMobileToolName=DEFAULT

