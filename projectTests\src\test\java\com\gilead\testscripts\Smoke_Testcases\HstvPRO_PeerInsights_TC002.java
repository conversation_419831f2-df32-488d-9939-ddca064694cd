package com.gilead.testscripts.Smoke_Testcases;

import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;

public class HstvPRO_PeerInsights_TC002 extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)@Ignore
	public void checkComponentPresents() {
		try {
			objCommonFunctions.validateComponentExists();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 3)
	public void validateRegisterLinks() {
		try {
			objCommonFunctions.checkRegisterLinksLoc();

		} finally {
			checkErrors();
		}
	}

}
