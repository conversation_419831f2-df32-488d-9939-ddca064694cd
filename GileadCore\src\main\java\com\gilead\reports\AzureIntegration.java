package com.gilead.reports;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import com.gilead.maintenance.Settings;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

@SuppressWarnings({ "unused", "deprecation" })
public class AzureIntegration {
	private static Properties properties = Settings.getInstance();
	private String organizationURL = properties.getProperty("AzureOrgURL");
	private String projectName = properties.getProperty("AzureProject");
	private static String strTestPlanName = properties.getProperty("AzureTestPlan");
	private static String strMasterTestSuiteName = properties.getProperty("AzureTestPlan");
	private static String strTestSuiteName = properties.getProperty("AzureExecutionTestSuite");
	private static int runID = 0;
	private static int testPlanID = 0;
	private static int testMasterSuiteID = 0;
	private static int testRegressionSuiteID = 0;
	private static int testSuiteID = 0;
	private static int testCaseID = 0;
	private int testPointID;
	private static final String AUTHORIZATIONHEADER = "Authorization";
	private String accessToken = "";
	private static final String MIMETYPEJSON = "application/json";

	public void encrypt() {
		String personalAccessToken = properties.getProperty("AzurePersonalAccessToken");
		accessToken = "Basic " + java.util.Base64.getEncoder().encodeToString((personalAccessToken + ":").getBytes());
	}

	public void updateStatusToAzureDevOps(String testCaseName, String testStatus, String reportPath, String reportName,
			String strManualTcID, String strConfiguration) {
		try {
			encrypt();
			getTestPlanIDAPI();
			createTestSuite();
			String strTestCaseName = testCaseName;
			int intTestPoint;
			if (strConfiguration.isEmpty()) {
				intTestPoint = getTestPoint(strManualTcID);
			} else {
				intTestPoint = getTestPoint(strManualTcID, strConfiguration);
			}
			if (intTestPoint != 0) {
				createRun(intTestPoint);
				int intTestResultID = createResultsInRun(intTestPoint, testStatus, strManualTcID);
				addAttachmentToResult(intTestResultID, reportPath, reportName,
						"Automation Test HTML Result for " + strTestCaseName);
				updateTestResult(intTestResultID, testStatus);
				updateRun(testStatus);
				updateTestPoint(intTestPoint);
			} else {
				if (strConfiguration.isEmpty()) {
					System.out.println("Azure result not update for 'Automation TC:" + strTestCaseName
							+ "',Manual Testcase in Azure Test suite Mismatch/not Found,Please check the Azure Mapping sheet / Test Suite");
				} else {
					System.out.println("Azure result not update for 'Automation TC:" + strTestCaseName
							+ "','Manual Testcase: " + strManualTcID + "' and 'Configuration: " + strConfiguration
							+ "' in Azure Test suite Mismatch/not Found,Please check the Azure Mapping sheet / Test Suite");
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
	}

	public void getTestPlanIDAPI() {
		String continuationToken = null;
		do {
			String strAzureURL = organizationURL + projectName + "/_apis/testplan/plans?api-version=7.0";
			HttpClient httpClient = HttpClientBuilder.create().build();
			HttpGet getRequest = new HttpGet(
					strAzureURL + (continuationToken != null ? "&continuationToken=" + continuationToken : ""));
			try {
				getRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
				getRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
				getRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
				HttpResponse response = httpClient.execute(getRequest);
				String strResponse = EntityUtils.toString(response.getEntity());
				JsonParser jsonParser = new JsonParser();
				JsonObject jsonobj = jsonParser.parse(strResponse).getAsJsonObject();
				for (JsonElement jsonTestCase : jsonobj.get("value").getAsJsonArray()) {
					if (jsonTestCase.getAsJsonObject().get("name").getAsString().trim().equals(strTestPlanName)) {
						testPlanID = jsonTestCase.getAsJsonObject().get("id").getAsInt();
						break;
					}
				}
				continuationToken = (response.getFirstHeader("x-ms-continuationtoken") != null && testPlanID == 0
						? response.getFirstHeader("x-ms-continuationtoken").getValue()
						: null);
			} catch (IOException e) {
				e.printStackTrace();
			}
		} while (continuationToken != null);
	}

	public void createTestSuite() { // getTestSuitId
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpGet getRequest = new HttpGet(
				organizationURL + projectName + "/_apis/testplan/Plans/" + testPlanID + "/suites?api-version=7.0");
		try {
			getRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			getRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			getRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			HttpResponse response = httpClient.execute(getRequest);
			String strResponse = EntityUtils.toString(response.getEntity());
			JsonParser jsonParser = new JsonParser();
			JsonObject jsonobj = jsonParser.parse(strResponse).getAsJsonObject();
			for (JsonElement jsonTestCase : jsonobj.get("value").getAsJsonArray()) {
				if (jsonTestCase.getAsJsonObject().get("name").getAsString().trim().equals(strMasterTestSuiteName)) {
					testMasterSuiteID = jsonTestCase.getAsJsonObject().get("id").getAsInt(); // id 15534
					break;
				}
			}
			boolean blnSuiteFound = false;
			for (JsonElement jsonTestCase : jsonobj.get("value").getAsJsonArray()) {
				if (jsonTestCase.getAsJsonObject().get("name").getAsString().trim().equals(strTestSuiteName)) {
					testRegressionSuiteID = jsonTestCase.getAsJsonObject().get("id").getAsInt(); // id 15546 People
																									// search folder
					blnSuiteFound = true;
					break;
				}
			}

//		    if(!blnSuiteFound) {
//		    	httpClient = HttpClientBuilder.create().build();
//				HttpPost postRequest = new HttpPost(organizationURL+projectName+"/_apis/testplan/Plans/"+testPlanID+"/suites?api-version=7.0");			
//				postRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
//				postRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
//			    postRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
//			    String strJson = "{"
//			    					+ "\"suiteType\" : \"staticTestSuite\","
//				    				+ "\"name\" : \""+strTestSuiteName+"\","
//				    				+"\"parentSuite\":{"
//				    					+ "\"id\" : "+testMasterSuiteID
//				    				+ "}"
//					    			+"}";
//			    postRequest.setEntity(new StringEntity(strJson, ContentType.APPLICATION_JSON));
//			    response = httpClient.execute(postRequest);
//			    strResponse = EntityUtils.toString(response.getEntity());
//			    jsonParser = new JsonParser();
//			    jsonobj = jsonParser.parse(strResponse).getAsJsonObject();
//			    testRegressionSuiteID = jsonobj.get("id").getAsInt();
//		    }

		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public void testCaseClone(String strTestCaseName) {

		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpGet getRequest = new HttpGet(organizationURL + projectName + "/_apis/testplan/Plans/" + testPlanID
				+ "/Suites/" + testRegressionSuiteID + "/TestCase?api-version=7.0"); // test suit id 0 //check url try
																						// testSuiteID

		try {
			getRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			getRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			getRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			HttpResponse response = httpClient.execute(getRequest);
			String strResponse = EntityUtils.toString(response.getEntity());
			JsonParser jsonParser = new JsonParser();
			JsonObject jsonobj = jsonParser.parse(strResponse).getAsJsonObject();
			boolean blnTestCaseFound = false;
			for (JsonElement jsonTestCase : jsonobj.get("value").getAsJsonArray()) {
				if (jsonTestCase.getAsJsonObject().get("workItem").getAsJsonObject().get("name").getAsString()
						.equals(strTestCaseName)) {
					blnTestCaseFound = true;
					break;
				}

			}

//		    if(!blnTestCaseFound) {
//		    	httpClient = HttpClientBuilder.create().build();
//		    	int intTestCaseID = getTestCaseIDFromMasterSuite(strTestCaseName);
//				HttpPost postRequest = new HttpPost(organizationURL+projectName+"/_apis/testplan/TestCases/CloneTestCaseOperation?api-version=7.0");
//				String runName = "Run_"+new SimpleDateFormat("dd_MMM_yyyy_hh_mm_ss").format(new Date());
//				postRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
//				postRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
//			    postRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
//			    String strJson = "{"
//			    		+ "    \"cloneOptions\":{"
//			    		+ "        \"includeAttachments\": false"
//			    		+ "    },"
//			    		+ "    \"sourceTestPlan\":{"
//			    		+ "        \"id\": "+testPlanID+""
//			    		+ "    },"
//			    		+ "    \"sourceTestSuite\":{"
//			    		+ "        \"id\": "+testMasterSuiteID+""
//			    		+ "    },"
//			    		+ "    \"destinationTestPlan\":{"
//			    		+ "        \"id\": "+testPlanID+""
//			    		+ "    },"
//			    		+ "    \"destinationTestSuite\":{"
//			    		+ "        \"id\": "+testSuiteID+""
//			    		+ "    },"
//			    		+ "    \"testCaseIds\": ["+intTestCaseID+"]"
//			    		+ "}";
//			    postRequest.setEntity(new StringEntity(strJson, ContentType.APPLICATION_JSON));
//			    response = httpClient.execute(postRequest);
//			    /*strResponse = EntityUtils.toString(response.getEntity());
//			    jsonParser = new JsonParser();
//			    jsonobj = jsonParser.parse(strResponse).getAsJsonObject();
//			    runID = jsonobj.get("id").getAsInt();*/
//		    }

		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public int getTestPoint(String strCurrentTestName) {
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpGet getRequest = new HttpGet(organizationURL + projectName + "/_apis/test/Plans/" + testPlanID + "/Suites/"
				+ testRegressionSuiteID + "/points?api-version=7.0"); // testSuiteID
		try {
			getRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			getRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			getRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			/*
			 * String strJson = "{" + "\"automated\" : \"true\"" + "}";
			 * postRequest.setEntity(new StringEntity(strJson,
			 * ContentType.APPLICATION_JSON));
			 */
			HttpResponse response = httpClient.execute(getRequest);
			String strResponse = EntityUtils.toString(response.getEntity());
			JsonParser jsonParser = new JsonParser();
			JsonObject jsonobj = jsonParser.parse(strResponse).getAsJsonObject();

			for (JsonElement jsonTestCase : jsonobj.get("value").getAsJsonArray()) {
				jsonTestCase.getAsJsonObject().get("testCase");
				if (jsonTestCase.getAsJsonObject().get("testCase").getAsJsonObject().get("name").getAsString().trim()
						.equals(strCurrentTestName)) {
//				String TcNameFromAzure = jsonTestCase.getAsJsonObject().get("testCase").getAsJsonObject().
//						get("name").getAsString().trim();
//				System.out.println("TC name from azure= "+TcNameFromAzure);
//				System.out.println("TC name from script= "+strCurrentTestName);

					testCaseID = jsonTestCase.getAsJsonObject().get("testCase").getAsJsonObject().get("id").getAsInt();
					return jsonTestCase.getAsJsonObject().get("id").getAsInt();
				}
			}
			return 0;

		} catch (IOException e) {
			e.printStackTrace();
			return 0;
		}
	}

	/**
	 * Method to create Run in tests tab of release
	 */
	public void createRun(int intTestPointID) {
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost postRequest = new HttpPost(organizationURL + projectName + "/_apis/test/runs?api-version=5.0");
		String runName = "Run_" + new SimpleDateFormat("dd_MMM_yyyy_hh_mm_ss").format(new Date());
		try {
			postRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			postRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			postRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			String strJson = "{" + "\"automated\" : \"true\"," + "\"name\" : \"" + runName + "\","
					+ "\"outcome\" : \"inProgress\"," + "\"plan\":{" + "\"id\" : " + testPlanID + "},"
					+ "\"testPoint\": {" + "  \"id\": \"" + intTestPointID + "\"" + " } ," + "}";
			postRequest.setEntity(new StringEntity(strJson, ContentType.APPLICATION_JSON));
			HttpResponse response = httpClient.execute(postRequest);
			String strResponse = EntityUtils.toString(response.getEntity());
			JsonParser jsonParser = new JsonParser();
			JsonObject jsonobj = jsonParser.parse(strResponse).getAsJsonObject();
			runID = jsonobj.get("id").getAsInt();
		} catch (IOException e) {
			e.printStackTrace();

		}
	}

	public int getRunID() {
		return runID;
	}

	public int getTestPlanID() {
		return testPlanID;
	}

	public int getTestSuiteID() {
		return testSuiteID;
	}

	/**
	 * Method to add Attachments to Run in Azure
	 * 
	 * @param strAttachmentPath - path of the file which has to be attached
	 * @param strAttachmentName - Name of the Attachment in Azure
	 * @param strComments       - Comments to be captured against attachment in
	 *                          Azure
	 */

	public void addAttachmentToResult(int intTestResultID, String strAttachmentPath, String strAttachmentName,
			String strComments) {
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost postRequest = new HttpPost(organizationURL + projectName + "/_apis/test/Runs/" + runID + "/Results/"
				+ intTestResultID + "/attachments?api-version=7.0");
		try {
			postRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			postRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			postRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			String strJson = "{" + "\"Stream\" : \"" + getStream(strAttachmentPath) + "\"," + "\"fileName\" : \""
					+ strAttachmentName + "\"," + "\"comment\" : \"" + strComments + "\","
					+ "\"attachmentType\" : \"GeneralAttachment\"" + "}";
			postRequest.setEntity(new StringEntity(strJson, ContentType.APPLICATION_JSON));
			httpClient.execute(postRequest);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * Method to get base64 encoded file stream
	 * 
	 * @param strFilePath - path of the file for which encoded string is to be
	 *                    returned
	 * @return - Base64 encoded file stream
	 */
	public String getStream(String strFilePath) {
		File file = new File(strFilePath);
		String strEncodedString = "";
		byte[] encoded = null;
		try {
			encoded = Base64.encodeBase64(FileUtils.readFileToByteArray(file));
			strEncodedString = new String(encoded, StandardCharsets.UTF_8);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return strEncodedString;
	}

	public int createResultsInRun(int intTestPointID, String strTestResult, String strTestCaseName) { // check
																										// AzureTesterName
																										// in prop
		try {
			String strAzureExecutorName = properties.getProperty("AzureExecutorName");
			HttpClient httpClient = HttpClientBuilder.create().build();
			HttpPost postRequest = new HttpPost(
					organizationURL + projectName + "/_apis/test/Runs/" + runID + "/results?api-version=7.0");
			postRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			postRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			postRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			String strJson = "[" + "  {" + "    \"outcome\": \"" + strTestResult + "\","
					+ "    \"state\": \"inProgress\","
//	    		+ "    \"computerName\": \""+strComputerHostName+"\","
					+ "\"runBy\": {" + "\"displayName\": \"" + strAzureExecutorName + "\""
//                 +            "\"id\": \"78eb12be-d84f-64c1-91b4-03def39c7299\""
					+ "}," + "    \"testCase\": {" + "        \"id\": \"" + testCaseID + "\"" + "    },"
					+ "    \"testPoint\": {" + "        \"id\": \"" + intTestPointID + "\"" + "    } ,"
					+ "    \"testCaseTitle\": \"" + strTestCaseName + "\","
					// +" \"comment\":"+ "<a
					// href="+"\"https://dev.azure.com/gilead-mobile/gmobile-2.0/_build/results?buildId="+buildId+"&view=JakubRumpca.azure-pipelines-html-report.build-html-report-tab\""+">"+"
					// \""+buildId+"\" "+ "</a>"+","
					+ "    \"testCaseRevision\": 1" + "  }" + "]";
			postRequest.setEntity(new StringEntity(strJson, ContentType.APPLICATION_JSON));
			HttpResponse response = httpClient.execute(postRequest);
			String strResponse = EntityUtils.toString(response.getEntity());
			JsonParser jsonParser = new JsonParser();
			JsonObject jsonobj = jsonParser.parse(strResponse).getAsJsonObject();

			for (JsonElement jsonTestResult : jsonobj.get("value").getAsJsonArray()) {
				return jsonTestResult.getAsJsonObject().get("id").getAsInt();
			}
//	    String strID = jsonobj.get("id").getAsString();
//	    return Integer.parseInt(strID);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return 0;
	}

	public void updateTestResult(int intTestResultID, String strStatus) { // check
		try {
			HttpClient httpClient = HttpClientBuilder.create().build();
			HttpPatch postRequest = new HttpPatch(
					organizationURL + projectName + "/_apis/test/Runs/" + runID + "/results?api-version=7.0");
			postRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			postRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			postRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			String strJson = "[" + "        {" + "    \"id\": \"" + intTestResultID + "\"," + "    \"outcome\": \""
					+ strStatus + "\"," + "    \"state\": \"Completed\"" + "        }" + "    ]";
//	    String strJson = "["
//	    		+ "  {"
//	    		+ "    \"id\": \""+intTestResultID+"\","
//	    		+ "    \"outcome\": \""+strStatus+"\""
//	    		//+ "    \"state\": \"Completed\""
//	    		+ "  }"
//	    		+ "]";
			postRequest.setEntity(new StringEntity(strJson, ContentType.APPLICATION_JSON));
			HttpResponse response = httpClient.execute(postRequest);
			String strResponse = EntityUtils.toString(response.getEntity());
			JsonParser jsonParser = new JsonParser();
			JsonObject jsonobj = jsonParser.parse(strResponse).getAsJsonObject();
			for (JsonElement jsonTestResult : jsonobj.get("value").getAsJsonArray()) {
				jsonTestResult.getAsJsonObject().get("id").getAsInt();
			}

			// jsonobj.get("id").getAsInt();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * Method to update Run Status in Azure
	 */
	public void updateRun(String strStatus) {
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPatch patchRequest = new HttpPatch(
				organizationURL + projectName + "/_apis/test/runs/" + runID + "?api-version=5.0");
		try {
			patchRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			patchRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			patchRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			String strJson = "{" + "\"state\" : \"Completed\"," + "\"outcome\" : \"" + strStatus + "\"" + "}";
			patchRequest.setEntity(new StringEntity(strJson, ContentType.APPLICATION_JSON));
			httpClient.execute(patchRequest);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public int getTestPoint(String strCurrentTestName, String strConfiguration) {
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpGet getRequest = new HttpGet(organizationURL + projectName + "/_apis/test/Plans/" + testPlanID + "/Suites/"
				+ testRegressionSuiteID + "/points?api-version=7.0"); // testSuiteID
		try {
			getRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			getRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			getRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			HttpResponse response = httpClient.execute(getRequest);
			String strResponse = EntityUtils.toString(response.getEntity());
			JsonObject jsonobj = JsonParser.parseString(strResponse).getAsJsonObject();

			for (JsonElement jsonTestCase : jsonobj.get("value").getAsJsonArray()) {

				System.out.println(jsonTestCase.getAsJsonObject().get("configuration").getAsJsonObject().get("name")
						.getAsString().trim());

				if (jsonTestCase.getAsJsonObject().get("testCase").getAsJsonObject().get("name").getAsString().trim()
						.contains(strCurrentTestName)) {

					if (jsonTestCase.getAsJsonObject().get("configuration").getAsJsonObject().get("name").getAsString()
							.trim().contains(strConfiguration)) {
						testCaseID = jsonTestCase.getAsJsonObject().get("testCase").getAsJsonObject().get("id")
								.getAsInt();
						return jsonTestCase.getAsJsonObject().get("id").getAsInt();
					}

				}
			}
			return 0;

		} catch (IOException e) {
			e.printStackTrace();
			return 0;
		}
	}

	public void updateTestPoint(int intTestPointID) {
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpPatch patchRequest = new HttpPatch(
				organizationURL + projectName + "/_apis/test/runs/" + runID + "/results?api-version=7.0");
		try {
			patchRequest.setHeader(HttpHeaders.CONTENT_TYPE, MIMETYPEJSON);
			patchRequest.setHeader(HttpHeaders.ACCEPT, MIMETYPEJSON);
			patchRequest.setHeader(AUTHORIZATIONHEADER, accessToken);
			String strJson = "[{\"id\":100000,{\"testPoint\": {" + "\"id\": \"" + intTestPointID + "\"" + "}}]";
			patchRequest.setEntity(new StringEntity(strJson, ContentType.APPLICATION_JSON));
			httpClient.execute(patchRequest);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	/*
	 * loadFromPropertiesFile
	 * 
	 * 
	 */
	/*
	 * private static Properties loadFromPropertiesFile() { Properties properties =
	 * new Properties();
	 * 
	 * String path = ".\\src\\test\\resources\\config\\ADO_Integration.properties";
	 * try { properties.load(new FileInputStream(path)); } catch
	 * (FileNotFoundException e) { // TODO Auto-generated catch block
	 * e.printStackTrace(); } catch (IOException e) { // TODO Auto-generated catch
	 * block e.printStackTrace(); }
	 * 
	 * return properties; }
	 */

}
