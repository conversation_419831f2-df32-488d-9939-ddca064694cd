package com.gilead.testscripts.Carthope;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class Validate_CartHope_More_About_CART extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
			objCommonFunctions.logonCarthope();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 2)
	public void validateCartHopeMoreAboutCART() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPage();
		} finally {
			checkErrors();
		}
	}
	
}
