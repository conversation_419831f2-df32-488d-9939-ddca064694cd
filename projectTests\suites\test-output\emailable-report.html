<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "https://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
<title>TestNG Report</title>
<style type="text/css">table {margin-bottom:10px;border-collapse:collapse;empty-cells:show}th,td {border:1px solid #009;padding:.25em .5em}th {vertical-align:bottom}td {vertical-align:top}table a {font-weight:bold}.stripe td {background-color: #E6EBF9}.num {text-align:right}.passedodd td {background-color: #3F3}.passedeven td {background-color: #0A0}.skippedodd td {background-color: #DDD}.skippedeven td {background-color: #CCC}.failedodd td,.attn {background-color: #F33}.failedeven td,.stripe .attn {background-color: #D00}.stacktrace {white-space:pre;font-family:monospace}.totop {font-size:85%;text-align:center;border-bottom:2px solid #000}.invisible {display:none}</style>
</head>
<body>
<table>
<tr><th>Test</th><th># Passed</th><th># Skipped</th><th># Retried</th><th># Failed</th><th>Time (ms)</th><th>Included Groups</th><th>Excluded Groups</th></tr>
<tr><th colspan="8">FunctionalRegression_Suite</th></tr>
<tr><td><a href="#t0">Test under EntireSuite_Suite1</a></td><td class="num">9</td><td class="num">0</td><td class="num">0</td><td class="num attn">3</td><td class="num">168,800</td><td></td><td></td></tr>
</table>
<table id='summary'><thead><tr><th>Class</th><th>Method</th><th>Start</th><th>Time (ms)</th></tr></thead><tbody><tr><th colspan="4">FunctionalRegression_Suite</th></tr></tbody><tbody id="t0"><tr><th colspan="4">Test under EntireSuite_Suite1 &#8212; failed</th></tr><tr class="failedeven"><td rowspan="1">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003</td><td><a href="#m0">invokeURL</a></td><td rowspan="1">1747814699378</td><td rowspan="1">4829</td></tr><tr class="failedodd"><td rowspan="1">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002</td><td><a href="#m1">invokeURL</a></td><td rowspan="1">1747814667881</td><td rowspan="1">5431</td></tr><tr class="failedeven"><td rowspan="1">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004</td><td><a href="#m2">verifyCriticalComponents</a></td><td rowspan="1">1747814725601</td><td rowspan="1">13697</td></tr><tr><th colspan="4">Test under EntireSuite_Suite1 &#8212; passed</th></tr><tr class="passedeven"><td rowspan="2">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001</td><td><a href="#m3">invokeURL</a></td><td rowspan="1">1747814636051</td><td rowspan="1">5674</td></tr><tr class="passedeven"><td><a href="#m4">verifyCriticalComponents</a></td><td rowspan="1">1747814641728</td><td rowspan="1">15703</td></tr><tr class="passedodd"><td rowspan="1">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003</td><td><a href="#m5">verifyCriticalComponents</a></td><td rowspan="1">1747814704236</td><td rowspan="1">8167</td></tr><tr class="passedeven"><td rowspan="1">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002</td><td><a href="#m6">verifyCriticalComponents</a></td><td rowspan="1">1747814673343</td><td rowspan="1">15341</td></tr><tr class="passedodd"><td rowspan="2">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005</td><td><a href="#m7">invokeURL</a></td><td rowspan="1">1747814587502</td><td rowspan="1">6898</td></tr><tr class="passedodd"><td><a href="#m8">verifyCriticalComponents</a></td><td rowspan="1">1747814594407</td><td rowspan="1">9532</td></tr><tr class="passedeven"><td rowspan="1">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004</td><td><a href="#m9">invokeURL</a></td><td rowspan="1">1747814720371</td><td rowspan="1">5227</td></tr><tr class="passedodd"><td rowspan="2">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006</td><td><a href="#m10">invokeURL</a></td><td rowspan="1">1747814616577</td><td rowspan="1">5212</td></tr><tr class="passedodd"><td><a href="#m11">verifyCriticalComponents</a></td><td rowspan="1">1747814621796</td><td rowspan="1">6048</td></tr></tbody>
</table>
<h2>Test under EntireSuite_Suite1</h2><h3 id="m0">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003#invokeURL</h3><table class="result"><tr><th>Exception</th></tr><tr><td><div class="stacktrace">com.gilead.config.FrameworkException: The specified sheet &quot;SmokeCICD&quot;does not exist within the workbook &quot;Thread5FunctionalRegression_Gileadadvancingaccess_HCP_FAQs_TC003_Instance1.xlsx&quot;
	at com.gilead.maintenance.ExcelDataAccess.getWorkSheet(ExcelDataAccess.java:139)
	at com.gilead.maintenance.ExcelDataAccess.getRowNum(ExcelDataAccess.java:163)
	at com.gilead.maintenance.CraftDataTable.getData(CraftDataTable.java:120)
	at businesscomponents.CommonFunctions.preCondition(CommonFunctions.java:785)
	at businesscomponents.CommonFunctions.launchApplication(CommonFunctions.java:1117)
	at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003.invokeURL(Gileadadvancingaccess_HCP_FAQs_TC003.java:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames</div></td></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m1">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002#invokeURL</h3><table class="result"><tr><th>Exception</th></tr><tr><td><div class="stacktrace">com.gilead.config.FrameworkException: The specified sheet &quot;SmokeCICD&quot;does not exist within the workbook &quot;Thread4FunctionalRegression_Gileadadvancingaccess_HCP_PharmacyFinder_TC002_Instance1.xlsx&quot;
	at com.gilead.maintenance.ExcelDataAccess.getWorkSheet(ExcelDataAccess.java:139)
	at com.gilead.maintenance.ExcelDataAccess.getRowNum(ExcelDataAccess.java:163)
	at com.gilead.maintenance.CraftDataTable.getData(CraftDataTable.java:120)
	at businesscomponents.CommonFunctions.preCondition(CommonFunctions.java:785)
	at businesscomponents.CommonFunctions.launchApplication(CommonFunctions.java:1117)
	at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002.invokeURL(Gileadadvancingaccess_HCP_PharmacyFinder_TC002.java:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames</div></td></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m2">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004#verifyCriticalComponents</h3><table class="result"><tr><th>Exception</th></tr><tr><td><div class="stacktrace">org.openqa.selenium.NoSuchElementException: no such element: Unable to locate element: {&quot;method&quot;:&quot;xpath&quot;,&quot;selector&quot;:&quot; //div[@class=&apos;navbar-collapse collapse&apos;]//a[contains(text(),&apos;Activate&apos;)]&quot;}
  (Session info: chrome=133.0.6943.99)
For documentation on this error, please visit: https://selenium.dev/exceptions/#no_such_element
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [fbcabfabcaf3acee2648df9c79f95c44, findElement {using=xpath, value= //div[@class=&apos;navbar-collapse collapse&apos;]//a[contains(text(),&apos;Activate&apos;)]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:54986}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:54986/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: fbcabfabcaf3acee2648df9c79f95c44
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElement(ElementLocation.java:162)
	at org.openqa.selenium.remote.ElementLocation.findElement(ElementLocation.java:60)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:382)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:374)
	at com.gilead.reports.CraftDriver.findElement(CraftDriver.java:164)
	at businesscomponents.CommonFunctions.verifyLinksInWebPage(CommonFunctions.java:1137)
	at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004.verifyCriticalComponents(Gileadadvancingaccess_Patient_CopayCouponpage_TC004.java:29)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 16 stack frames</div></td></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m3">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001#invokeURL</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m4">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001#verifyCriticalComponents</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m5">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003#verifyCriticalComponents</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m6">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002#verifyCriticalComponents</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m7">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005#invokeURL</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m8">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005#verifyCriticalComponents</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m9">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004#invokeURL</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m10">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006#invokeURL</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m11">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006#verifyCriticalComponents</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
</body>
</html>
