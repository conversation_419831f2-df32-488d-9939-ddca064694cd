<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>YESCARTA_Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:29:30</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@1028261743</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/22 11:16:45</td>   <td>-765414</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Navigation_and_Functionality_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">invokeURL</td> 
  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:24:55</td>   <td>-275466</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:24:07</td>   <td>-323751</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:30:08</td>   <td>37614</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread9@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:31:47</td>   <td>136490</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread8@563103238</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:31:50</td>   <td>139077</td> <td title="&lt;&lt;CRAFTLiteTestCase.tearDownTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;tearDownTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:16:23</td>   <td>-787913</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:16:23</td>   <td>-787900</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:17:22</td>   <td>-728473</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/22 11:26:56</td>   <td>-154703</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_receiving_yescarta_site.verifyReceivingYescarta()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">verifyReceivingYescarta</td> 
  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="b0fd6f">  <td>25/04/22 11:24:55</td>   <td>-275466</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_support_and_resources_site.verifySupportAndResourcesSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">verifySupportAndResourcesSite</td> 
  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:31:18</td>   <td>107797</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread9@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:23:35</td>   <td>-355635</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/22 11:29:46</td>   <td>15702</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">invokeURL</td> 
  <td>Thread9@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:24:44</td>   <td>-286959</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:22:12</td>   <td>-438617</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="79a2a1">  <td>25/04/22 11:24:44</td>   <td>-286959</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_at_a_glance_site.verifyYescartaSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">verifyYescartaSite</td> 
  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:27:57</td>   <td>-93914</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:23:40</td>   <td>-350103</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:17:25</td>   <td>-725529</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:29:30</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:28:47</td>   <td>-43622</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:23:40</td>   <td>-350103</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:28:45</td>   <td>-45636</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:29:21</td>   <td>-9682</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread8@563103238</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/22 11:23:13</td>   <td>-377951</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PDF_Download_Verification_for_YesCARTA_Website.PDFverification()[pri:2, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">PDFverification</td> 
  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:31:17</td>   <td>106580</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread9@1028261743</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/22 11:30:08</td>   <td>37614</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.PIandNavigationVerification()[pri:2, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">PIandNavigationVerification</td> 
  <td>Thread9@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:17:25</td>   <td>-725529</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:28:30</td>   <td>-60808</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread7@1028261743</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/22 11:28:52</td>   <td>-38943</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_clinical_trial_results_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">invokeURL</td> 
  <td>Thread8@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:26:33</td>   <td>-177202</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/22 11:17:25</td>   <td>-725529</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders()[pri:2, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">verifyNavigationHeaders</td> 
  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:26:31</td>   <td>-179457</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:23:39</td>   <td>-351355</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="79a2a1">  <td>25/04/22 11:23:51</td>   <td>-339754</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_at_a_glance_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">invokeURL</td> 
  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:23:39</td>   <td>-351357</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:26:56</td>   <td>-154703</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:23:39</td>   <td>-351355</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:21:55</td>   <td>-455891</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:27:42</td>   <td>-108367</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:26:56</td>   <td>-154703</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:24:55</td>   <td>-275466</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:16:23</td>   <td>-787900</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:24:44</td>   <td>-286959</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:31:17</td>   <td>106580</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread9@1028261743</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/22 11:17:22</td>   <td>-728473</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.navigationVerification()[pri:2, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">navigationVerification</td> 
  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/22 11:29:21</td>   <td>-9680</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_clinical_trial_results_site.verifyClinicalTrialResults()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">verifyClinicalTrialResults</td> 
  <td>Thread8@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:24:07</td>   <td>-323398</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:24:07</td>   <td>-323398</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:29:39</td>   <td>8864</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@1028261743</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/22 11:16:41</td>   <td>-769568</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">invokeURL</td> 
  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/22 11:22:20</td>   <td>-430100</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PDF_Download_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">invokeURL</td> 
  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:28:45</td>   <td>-45636</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:28:47</td>   <td>-43623</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:26:31</td>   <td>-179457</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:27:57</td>   <td>-93918</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:23:35</td>   <td>-355635</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:31:49</td>   <td>138033</td> <td title="&lt;&lt;BaseTest.afterSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;afterSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:22:12</td>   <td>-438601</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:23:13</td>   <td>-377951</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:26:33</td>   <td>-177207</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:28:30</td>   <td>-60808</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread7@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:31:47</td>   <td>136490</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread8@563103238</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/22 11:26:39</td>   <td>-171601</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_receiving_yescarta_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">invokeURL</td> 
  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:27:57</td>   <td>-93914</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="b0fd6f">  <td>25/04/22 11:24:20</td>   <td>-310509</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_support_and_resources_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">invokeURL</td> 
  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:22:12</td>   <td>-438602</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:28:47</td>   <td>-43622</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:23:13</td>   <td>-377951</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@1028261743</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:26:33</td>   <td>-177203</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@563103238</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:16:22</td>   <td>-788239</td> <td title="&gt;&gt;CRAFTLiteTestCase.setUpTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;setUpTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:29:39</td>   <td>8864</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:31:48</td>   <td>137976</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread8@563103238</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:30:08</td>   <td>37614</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread9@1028261743</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/22 11:28:30</td>   <td>-60807</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_managing_side_effects_site.verifyManagingSideEffectsSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">verifyManagingSideEffectsSite</td> 
  <td>Thread7@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:17:22</td>   <td>-728473</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/22 11:28:02</td>   <td>-88860</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_managing_side_effects_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">invokeURL</td> 
  <td>Thread7@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:29:21</td>   <td>-9682</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site@1c5920df]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread8@563103238</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:16:23</td>   <td>-787913</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:27:42</td>   <td>-108367</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site@7f485fda]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:21:55</td>   <td>-455891</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@1028261743</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:29:39</td>   <td>8862</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site@196a42c3]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@1028261743</td>   <td></td> </tr>
</table>
