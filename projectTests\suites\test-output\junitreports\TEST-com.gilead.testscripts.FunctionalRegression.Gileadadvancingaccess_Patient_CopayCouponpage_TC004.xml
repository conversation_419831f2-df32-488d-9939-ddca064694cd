<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004" tests="2" failures="0" timestamp="2025-05-21T13:35:41 IST" time="19.922" errors="1">
  <testcase name="invokeURL" time="6.224" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004"/>
  <system-out/>
  <testcase name="verifyCriticalComponents" time="13.698" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004">
    <error type="org.openqa.selenium.NoSuchElementException" message="no such element: Unable to locate element: {&quot;method&quot;:&quot;xpath&quot;,&quot;selector&quot;:&quot; //div[@class=&#039;navbar-collapse collapse&#039;]//a[contains(text(),&#039;Activate&#039;)]&quot;}
  (Session info: chrome=133.0.6943.99)
For documentation on this error, please visit: https://selenium.dev/exceptions/#no_such_element
Build info: version: &#039;4.1.2&#039;, revision: &#039;9a5a329c5a&#039;
System info: host: &#039;Z1VD7STHPRDN255&#039;, ip: &#039;************&#039;, os.name: &#039;Windows 10&#039;, os.arch: &#039;amd64&#039;, os.version: &#039;10.0&#039;, java.version: &#039;1.8.0_291&#039;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [fbcabfabcaf3acee2648df9c79f95c44, findElement {using=xpath, value= //div[@class=&#039;navbar-collapse collapse&#039;]//a[contains(text(),&#039;Activate&#039;)]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:54986}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:54986/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: fbcabfabcaf3acee2648df9c79f95c44">
      <![CDATA[org.openqa.selenium.NoSuchElementException: no such element: Unable to locate element: {"method":"xpath","selector":" //div[@class='navbar-collapse collapse']//a[contains(text(),'Activate')]"}
  (Session info: chrome=133.0.6943.99)
For documentation on this error, please visit: https://selenium.dev/exceptions/#no_such_element
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [fbcabfabcaf3acee2648df9c79f95c44, findElement {using=xpath, value= //div[@class='navbar-collapse collapse']//a[contains(text(),'Activate')]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:54986}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:54986/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: fbcabfabcaf3acee2648df9c79f95c44
at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElement(ElementLocation.java:162)
at org.openqa.selenium.remote.ElementLocation.findElement(ElementLocation.java:60)
at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:382)
at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:374)
at com.gilead.reports.CraftDriver.findElement(CraftDriver.java:164)
at businesscomponents.CommonFunctions.verifyLinksInWebPage(CommonFunctions.java:1137)
at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004.verifyCriticalComponents(Gileadadvancingaccess_Patient_CopayCouponpage_TC004.java:29)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </error>
  </testcase> <!-- verifyCriticalComponents -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004 -->
