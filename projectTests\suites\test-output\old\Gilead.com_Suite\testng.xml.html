<html><head><title>testng.xml for Gilead.com_Suite</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;guice-stage="DEVELOPMENT"&nbsp;name="Gilead.com_Suite"&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="2"&nbsp;name="Test&nbsp;under&nbsp;EntireSuite_Suite1"&nbsp;parallel="classes"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;parameter&nbsp;name="RunID"&nbsp;value="0"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;Test&nbsp;under&nbsp;EntireSuite_Suite1&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;Gilead.com_Suite&nbsp;--&gt;
<br/></tt></body></html>