id: AvoidHardocodedURLForVariable
message: "Ensure URL will be provided by either dataTable.getData() or run-time values"
language: java
severity: warning
rule:
  pattern: driver.get($$$A)
  has:
    kind: argument_list
    all:
      - has:
          kind: identifier
          pattern: $PAT
    inside:
      stopBy: end
      kind: program
      has:
        stopBy: end
        kind: local_variable_declaration
        has:
          stopBy: end
          kind: variable_declarator
          all:
            - {has: {stopBy: end, kind: identifier, pattern: $PAT}}
            - not: {has: {stopBy: end, kind: method_invocation, regex: getText}}
            - not: {has: {stopBy: end, kind: method_invocation, regex: getAttributeValue}}
            - not: {has: {stopBy: end, kind: method_invocation, regex: getValue}}
            - not: {has: {stopBy: end, kind: method_invocation, regex: dataTable.getData}}
---
id: AvoidHardcodedURLForArgument
message: "Ensure URL will be provided by either dataTable.getData() or run-time values"
language: java
severity: warning
rule:
  pattern: driver.get($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: method_invocation
      all:
        - not: {regex: dataTable.getData}
        - not: {regex: getText}
        - not: {regex: getAttributeValue}
        - not: {regex: getValue}