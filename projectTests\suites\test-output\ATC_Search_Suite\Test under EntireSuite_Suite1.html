<html>
<head>
<title>TestNG:  Test under EntireSuite_Suite1</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Test under EntireSuite_Suite1</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>0/0/18</td>
</tr><tr>
<td>Started on:</td><td>Wed Apr 02 12:56:28 IST 2025</td>
</tr>
<tr><td>Total time:</td><td>6 seconds (6483 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED CONFIGURATIONS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_yescarta_at_a_glance_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1134318879", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1134318879'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace962656675", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace962656675'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2101539073", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2101539073'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_managing_side_effects_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1465902937", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1465902937'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace535563300", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace535563300'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_receiving_yescarta_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1574863508", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1574863508'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_yescarta_at_a_glance_site.verifyYescartaSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.verifyYescartaSite() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace584297694", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace584297694'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2133869600", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2133869600'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace302152531", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace302152531'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1880351361", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1880351361'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1204535388", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1204535388'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace742312233", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace742312233'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace511439203", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace511439203'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name={null} status=CREATED method=Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders()[pri:2, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a] output={null}], public void com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1243024267", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1243024267'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name={null} status=CREATED method=Navigation_and_Functionality_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a] output={null}], public void com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace616137063", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace616137063'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace78220407", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace78220407'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1916552867", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1916552867'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_clinical_trial_results_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace189300776", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace189300776'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace677661953", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace677661953'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace58121694", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace58121694'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_support_and_resources_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace130456430", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace130456430'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name={null} status=CREATED method=PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.PIandNavigationVerification()[pri:2, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096] output={null}], public void com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.PIandNavigationVerification() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace166297091", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace166297091'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace822012882", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace822012882'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name={null} status=CREATED method=PDF_Download_Verification_for_YesCARTA_Website.PDFverification()[pri:2, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b] output={null}], public void com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.PDFverification() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2140551226", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2140551226'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_clinical_trial_results_site.verifyClinicalTrialResults()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.verifyClinicalTrialResults() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1607121316", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1607121316'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_support_and_resources_site.verifySupportAndResourcesSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.verifySupportAndResourcesSite() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1018202049", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1018202049'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1666798571", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1666798571'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>3</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.afterClass()'><b>afterClass</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace268033293", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace268033293'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:217)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:130)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_managing_side_effects_site.verifyManagingSideEffectsSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.verifyManagingSideEffectsSite() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace271746503", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace271746503'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name={null} status=CREATED method=PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096] output={null}], public void com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace985036800", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace985036800'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1902977243", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1902977243'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.beforeClass()'><b>beforeClass</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site<br>Parameters: org.testng.TestRunner@30b65186</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1865116060", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1865116060'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site<br>Parameters: [TestResult name={null} status=CREATED method=To_Verify_receiving_yescarta_site.verifyReceivingYescarta()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a] output={null}], public void com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.verifyReceivingYescarta() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1599864300", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1599864300'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name={null} status=CREATED method=Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.navigationVerification()[pri:2, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be] output={null}], public void com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.navigationVerification() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1665147897", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1665147897'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name={null} status=CREATED method=Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be] output={null}], public void com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace960825402", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace960825402'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website<br>Parameters: [TestResult name={null} status=CREATED method=PDF_Download_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b] output={null}], public void com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.invokeURL() throws java.lang.Exception</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1563817162", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1563817162'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED CONFIGURATIONS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website</td>
<td></td>
<td>-1743578795</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site</td>
<td></td>
<td>-1743578794</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site</td>
<td></td>
<td>-1743578792</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website</td>
<td></td>
<td>-1743578795</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website</td>
<td></td>
<td>-1743578792</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site</td>
<td></td>
<td>-1743578793</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website</td>
<td></td>
<td>-1743578792</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site</td>
<td></td>
<td>-1743578793</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site</td>
<td></td>
<td>-1743578792</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site</td>
<td></td>
<td>-1743578794</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website</td>
<td></td>
<td>-1743578795</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website</td>
<td></td>
<td>-1743578792</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website</td>
<td></td>
<td>-1743578792</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site</td>
<td></td>
<td>-1743578794</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site</td>
<td></td>
<td>-1743578793</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site</td>
<td></td>
<td>-1743578793</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website</td>
<td></td>
<td>-1743578795</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.beforeMethod()'><b>beforeMethod</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site</td>
<td></td>
<td>-1743578794</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-skipped'>
<tr><td colspan='4' align='center'><b>SKIPPED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.navigationVerification()'><b>navigationVerification</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1789852622", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1789852622'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace846768056", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace846768056'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.PIandNavigationVerification()'><b>PIandNavigationVerification</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2053713545", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2053713545'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.verifyManagingSideEffectsSite()'><b>verifyManagingSideEffectsSite</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1349604178", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1349604178'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace196424249", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace196424249'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace429843538", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace429843538'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site.verifyYescartaSite()'><b>verifyYescartaSite</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1332807967", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1332807967'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders()'><b>verifyNavigationHeaders</b><br>Test class: com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1537301875", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1537301875'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace165550604", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace165550604'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website.PDFverification()'><b>PDFverification</b><br>Test class: com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace109995036", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace109995036'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1823935683", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1823935683'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace460987202", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace460987202'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.verifySupportAndResourcesSite()'><b>verifySupportAndResourcesSite</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1785188191", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1785188191'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace2053011715", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace2053011715'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace680984746", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace680984746'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site.verifyReceivingYescarta()'><b>verifyReceivingYescarta</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site</td>
<td><div><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace844780324", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace844780324'><pre>java.lang.NullPointerException
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:546)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site.verifyClinicalTrialResults()'><b>verifyClinicalTrialResults</b><br>Test class: com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace990605077", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace990605077'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website</td>
<td><div><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace105620533", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace105620533'><pre>com.gilead.config.FrameworkException: Configuration error
	at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestMethodWorker.invokeBeforeClassMethods(TestMethodWorker.java:176)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:122)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>0</td>
<td>com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096</td></tr>
</table><p>
</body>
</html>