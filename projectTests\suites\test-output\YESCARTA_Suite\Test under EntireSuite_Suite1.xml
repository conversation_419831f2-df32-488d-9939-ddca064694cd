<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="24" name="Test under EntireSuite_Suite1" tests="18" failures="1" timestamp="2025-04-22T11:31:48 IST" time="925.918" errors="0">
  <testcase name="@AfterMethod afterMethod" time="17.251" classname="com.gilead.base.BaseTest">
    <failure type="org.openqa.selenium.WebDriverException" message="Timed out waiting for driver server to shutdown.
Build info: version: &amp;apos;4.1.2&amp;apos;, revision: &amp;apos;9a5a329c5a&amp;apos;
System info: host: &amp;apos;Z1VD7STHPRDN255&amp;apos;, ip: &amp;apos;************&amp;apos;, os.name: &amp;apos;Windows 10&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;1.8.0_291&amp;apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [d117071334df6b37d2c842a5c3425aa7, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57183}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57183/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: d117071334df6b37d2c842a5c3425aa7">
      <![CDATA[org.openqa.selenium.WebDriverException: Timed out waiting for driver server to shutdown.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [d117071334df6b37d2c842a5c3425aa7, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57183}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57183/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: d117071334df6b37d2c842a5c3425aa7
at org.openqa.selenium.remote.service.DriverService.stop(DriverService.java:277)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:129)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: org.openqa.selenium.net.UrlChecker$TimeoutException: Timed out waiting for http://localhost:62897/shutdown to become unavailable after 3005 ms
at org.openqa.selenium.net.UrlChecker.waitUntilUnavailable(UrlChecker.java:130)
at org.openqa.selenium.remote.service.DriverService.stop(DriverService.java:273)
... 30 more
Caused by: java.util.concurrent.TimeoutException
at java.util.concurrent.FutureTask.get(FutureTask.java:205)
at org.openqa.selenium.net.UrlChecker.waitUntilUnavailable(UrlChecker.java:128)
... 31 more
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterMethod afterMethod" time="26.306" classname="com.gilead.base.BaseTest">
    <failure type="org.openqa.selenium.WebDriverException" message="Timed out waiting for driver server to stop.
Build info: version: &amp;apos;4.1.2&amp;apos;, revision: &amp;apos;9a5a329c5a&amp;apos;
System info: host: &amp;apos;Z1VD7STHPRDN255&amp;apos;, ip: &amp;apos;************&amp;apos;, os.name: &amp;apos;Windows 10&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;1.8.0_291&amp;apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [30d253b9e6edae7f10b6d7d29a0facfa, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57833}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57833/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: 30d253b9e6edae7f10b6d7d29a0facfa">
      <![CDATA[org.openqa.selenium.WebDriverException: Timed out waiting for driver server to stop.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [30d253b9e6edae7f10b6d7d29a0facfa, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:57833}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:57833/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: 30d253b9e6edae7f10b6d7d29a0facfa
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:132)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.ExecutionException: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: driver.version: unknown
at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:128)
... 29 more
Caused by: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: driver.version: unknown
at org.openqa.selenium.os.OsProcess.waitFor(OsProcess.java:174)
at org.openqa.selenium.os.CommandLine.waitFor(CommandLine.java:127)
at org.openqa.selenium.remote.service.DriverCommandExecutor.lambda$execute$2(DriverCommandExecutor.java:122)
at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)
... 3 more
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="invokeURL" time="41.049" classname="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website"/>
  <testcase name="invokeURL" time="39.877" classname="com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website"/>
  <testcase name="navigationVerification" time="272.573" classname="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website"/>
  <testcase name="invokeURL" time="52.142" classname="com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website"/>
  <testcase name="verifyNavigationHeaders" time="369.888" classname="com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website">
    <failure type="com.gilead.config.FrameworkAssertion" message="Error -  Navigation Menu is not enabled in the page: &amp;quot;https://www.yescarta.com/managing-side-effects#infusion-and-monitoring&amp;quot; even after waiting for 10 Seconds">
      <![CDATA[com.gilead.config.FrameworkAssertion: Error -  Navigation Menu is not enabled in the page: "https://www.yescarta.com/managing-side-effects#infusion-and-monitoring" even after waiting for 10 Seconds
at com.gilead.reports.Report.updateTestLog(Report.java:423)
at com.gilead.maintenance.ALMFunctions.ThrowException(ALMFunctions.java:252)
at com.gilead.maintenance.WebDriverUtil.waitUntilElementEnabled(WebDriverUtil.java:559)
at com.gilead.maintenance.CommonActionsAndFunctions.clickByJS(CommonActionsAndFunctions.java:501)
at businesscomponents.CommonFunctions.navigateAndVerifyMenu(CommonFunctions.java:3920)
at businesscomponents.CommonFunctions.accessNavigationMenu(CommonFunctions.java:4983)
at com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders(Navigation_and_Functionality_Verification_for_YesCARTA_Website.java:29)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames]]>
    </failure>
  </testcase> <!-- verifyNavigationHeaders -->
  <testcase name="PDFverification" time="27.84" classname="com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website"/>
  <testcase name="invokeURL" time="52.793" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site"/>
  <testcase name="invokeURL" time="35.039" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site"/>
  <testcase name="verifyYescartaSite" time="107.501" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site"/>
  <testcase name="invokeURL" time="16.896" classname="com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site"/>
  <testcase name="verifySupportAndResourcesSite" time="167.096" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_support_and_resources_site"/>
  <testcase name="invokeURL" time="28.047" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site"/>
  <testcase name="verifyReceivingYescarta" time="109.06" classname="com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site"/>
  <testcase name="invokeURL" time="29.256" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site"/>
  <testcase name="verifyManagingSideEffectsSite" time="60.799" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_managing_side_effects_site"/>
  <testcase name="invokeURL" time="21.91" classname="com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website"/>
  <testcase name="PIandNavigationVerification" time="68.964" classname="com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website"/>
  <testcase name="verifyClinicalTrialResults" time="146.163" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_clinical_trial_results_site"/>
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
