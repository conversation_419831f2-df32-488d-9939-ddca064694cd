<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite guice-stage="DEVELOPMENT" name="Failed suite [YescartaHCP_Suite]">
  <test thread-count="5" name="Test under EntireSuite_Suite1(failed)" parallel="classes">
    <parameter name="RunID" value="0"/>
    <classes>
      <class name="com.gilead.testscripts.YescartaHCP.To_Verify_Headers_On_YescartaHCP">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="toVerifyHeaders"/>
          <include name="setUpTestSuite"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
        </methods>
      </class> <!-- com.gilead.testscripts.YescartaHCP.To_Verify_Headers_On_YescartaHCP -->
    </classes>
  </test> <!-- Test under EntireSuite_Suite1(failed) -->
</suite> <!-- Failed suite [YescartaHCP_Suite] -->
