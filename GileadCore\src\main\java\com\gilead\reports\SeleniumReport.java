package com.gilead.reports;

import java.awt.AWTException;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.HeadlessException;
import java.awt.Rectangle;
import java.awt.Robot;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import javax.imageio.ImageIO;

import org.apache.commons.io.FileUtils;
import org.openqa.selenium.Capabilities;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.remote.Augmenter;
import org.openqa.selenium.remote.RemoteWebDriver;

import com.gilead.config.FrameworkException;
import com.gilead.maintenance.Settings;
import com.gilead.utils.ServiceRegister;

/**
 * Class which extends the {@link Report} class with a Selenium specific
 * override for taking screenshots
 * 
 * <AUTHOR>
 */
public class SeleniumReport extends Report {

	private CraftDriver driver;
	private final SeleniumTestParameters testParameters;

	ServiceRegister register = ServiceRegister.getInstance();

	/**
	 * Constructor to initialize the Report object
	 * 
	 * @param reportSettings The {@link ReportSettings} object
	 * @param reportTheme    The {@link ReportTheme} object
	 * @param testParameters
	 */
	public SeleniumReport(ReportSettings reportSettings, ReportTheme reportTheme,
			SeleniumTestParameters testParameters) {
		super(reportSettings, reportTheme, testParameters);
		this.testParameters = testParameters;
	}

	/**
	 * Function to set the {@link CraftDriver} object
	 * 
	 * @param driver The {@link CraftDriver} object
	 */

	public void setDriver(CraftDriver driver) {
		this.driver = driver;
	}

	@SuppressWarnings("unused")
	@Override
	protected synchronized void takeScreenshot(String screenshotName) {
		if (driver == null) {
			throw new FrameworkException("Report.driver is not initialized!");
		}
		File scrFile = null;

		String sessionId = register.getService(Thread.currentThread().getName(), "Session").toString();
		WebDriver wd = (RemoteWebDriver) register.getService(sessionId, "WD");

		switch (testParameters.getExecutionMode()) {
		case API:
			break;
		case LOCAL:
		case GRID:
		case MOBILE:
		case PERFECTO:
		case SAUCELABS:
		case TESTOBJECT:
		case FASTEST:
		case BROWSERSTACK:

			try {
				if ("RemoteWebDriver".equals(driver.getWebDriver().getClass().getSimpleName())) {
					Capabilities capabilities = ((RemoteWebDriver) wd).getCapabilities();
					if ("htmlunit".equals(capabilities.getBrowserName())) {
						return; // Screenshots not supported in headless mode
					}
					synchronized (this) {
						scrFile = ((RemoteWebDriver) wd).getScreenshotAs(OutputType.FILE);
					}
				} else {
					scrFile = ((TakesScreenshot) driver.getWebDriver()).getScreenshotAs(OutputType.FILE);
				}
			} catch (Exception ex) {
				ex.printStackTrace();
				throw new FrameworkException("Error while capturing the screenshot " + ex.getLocalizedMessage());
			}
			break;
		default:
			throw new FrameworkException("Take Screenshot", "Invalid Execution Mode for screenshot handling");

		}

		if (!(scrFile == null)) {
			try {
				Properties properties = Settings.getInstance();
				String testCase = register.getService(Thread.currentThread().getName(), "CurrentTestCase").toString();
				String screenshotPath = register
						.getService(Thread.currentThread().getName() + testCase + "ScreenShotPath", screenshotName)
						.toString();
				switch (properties.getProperty("ScreenshotType").toLowerCase()) {
				case "robot":
					BufferedImage image;
					try {
						// Get the default screen device configuration
						GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
						GraphicsDevice screen = ge.getDefaultScreenDevice();
						image = new Robot().createScreenCapture(
								new Rectangle(screen.getDisplayMode().getWidth(), screen.getDisplayMode().getHeight()));
						ImageIO.write(image, properties.getProperty("ScreenshotImageFormat"), new File(screenshotPath));
					} catch (HeadlessException e) {
						throw new FrameworkException("Take Screenshot",
								"Unable to capture screenshot using Robot class due to exception :: "
										+ e.getLocalizedMessage());
					} catch (AWTException e) {
						throw new FrameworkException("Take Screenshot",
								"Unable to capture screenshot using Robot class due to exception :: "
										+ e.getLocalizedMessage());
					}

					break;
				case "webdriverwithannotation":
					String dPattern = properties.getProperty("DateFormatString");
					BufferedImage img = ImageIO.read(scrFile);
					Graphics g = img.getGraphics();
					FontMetrics fm = g.getFontMetrics();
					LocalDateTime istNow = LocalDateTime.now();
					DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dPattern);
					g.setFont(new Font("Serif", Font.BOLD, 25));
					String dt = istNow.format(dateTimeFormatter);
					dt = "IST :" + dt;
					g.setColor(Color.RED);
					g.drawString(dt, 10, img.getHeight() - 20);
					ZoneId pstZone = ZoneId.of("America/Los_Angeles");
					LocalDateTime pstDateTime = istNow.atZone(ZoneId.systemDefault()).withZoneSameInstant(pstZone)
							.toLocalDateTime();
					dt = pstDateTime.format(dateTimeFormatter);
					dt = "PST :" + dt;
					g.drawString(dt, 10, img.getHeight() - 50);
					ImageIO.write(img, "png", new File(screenshotPath));
					break;
				case "webdriver":
					scrFile = ((TakesScreenshot) driver.getWebDriver()).getScreenshotAs(OutputType.FILE);
					image = ImageIO.read(scrFile);
					ImageIO.write(image, properties.getProperty("ScreenshotImageFormat"), new File(screenshotPath));
					break;
				default:
					throw new FrameworkException("Take Screenshot",
							properties.getProperty("ScreenshotType") + " is not handled");
				}

			} catch (IOException e) {
				e.printStackTrace();
				throw new FrameworkException("Error while writing screenshot to file");
			}
		}

	}

	@Override
	protected synchronized List<String> takeScreenshots(String screenshotPath) {
		boolean blnExit = false;
		if (driver == null) {
			throw new FrameworkException("Report.driver is not initialized!");
		}
		File scrFile = null;
		List<String> screenshotsName = new ArrayList<String>();
		switch (testParameters.getExecutionMode()) {
		case LOCAL:
		case GRID:
		case MOBILE:
		case SAUCELABS:
		case BROWSERSTACK:

			try {

				if ("HtmlUnitDriver".equals(driver.getWebDriver().getClass().getSimpleName())
						|| "class org.openqa.selenium.htmlunit.HtmlUnitDriver"
								.equals(driver.getWebDriver().getClass().getGenericSuperclass().toString())) {
					return null; // Screenshots not supported in headless mode
				}

				if ("RemoteWebDriver".equals(driver.getWebDriver().getClass().getSimpleName())) {
					Capabilities capabilities = ((RemoteWebDriver) driver.getWebDriver()).getCapabilities();
					if ("htmlunit".equals(capabilities.getBrowserName())) {
						return null; // Screenshots not supported in headless mode
					}
					WebDriver augmentedDriver = new Augmenter().augment(driver.getWebDriver());
					scrFile = ((TakesScreenshot) augmentedDriver).getScreenshotAs(OutputType.FILE);
				} else {

					JavascriptExecutor jsExec = (JavascriptExecutor) driver.getWebDriver();
					Long lngWindowHeight = (Long) jsExec.executeScript("return window.innerHeight;");
					Long lngWebpageHeight = (Long) jsExec.executeScript("return document.body.scrollHeight;");
					if (lngWebpageHeight == 0) {
						try {
							lngWebpageHeight = (Long) jsExec.executeScript(
									"return (document.getElementsByClassName('viewport')[0]).scrollHeight;");
						} catch (Exception e) {

						}
					}
					if (driver.getTitle().equalsIgnoreCase("Content Editor")) {
						lngWebpageHeight = (Long) jsExec.executeScript(
								"return (document.getElementsByClassName('scEditorPanel')[0]).scrollHeight;");
						lngWindowHeight = (Long) jsExec
								.executeScript("return (document.querySelector('.scEditorPanel').clientHeight)");
					} else {
						try {
							String strReduceViewPortSize = Settings.getInstance().getProperty("ReduceViewPortSize");
							long longReduceViewPortSize = Long.parseLong(strReduceViewPortSize);
							lngWindowHeight = lngWindowHeight - longReduceViewPortSize;
						} catch (Exception e) {
						}
					}
					Long currentWindowScroll = 0L;
					int i = 0;
					int intTotalScreenshots = 0;
					String scrn = screenshotPath.replaceAll(".png", "");

					do {
						jsExec.executeScript("window.scrollTo(0, " + currentWindowScroll + ");");
						if (driver.getTitle().equalsIgnoreCase("Content Editor"))
							jsExec.executeScript(
									"document.querySelector('.scEditorPanel').scrollTop=" + currentWindowScroll + "");

						try {
							Thread.sleep(2000);
						} catch (InterruptedException e) {
						}
						try {

							Properties properties = Settings.getInstance();
							TakesScreenshot scrShot = ((TakesScreenshot) driver.getWebDriver());
							File SrcFile = scrShot.getScreenshotAs(OutputType.FILE);
							File DestFile = new File(scrn + "_scroll" + (i + 1) + ".png");
							switch (properties.getProperty("ScreenshotType").toLowerCase()) {
							case "robot":
								BufferedImage image;
								try {
									// Get the default screen device configuration
									GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
									GraphicsDevice screen = ge.getDefaultScreenDevice();
									image = new Robot().createScreenCapture(new Rectangle(
											screen.getDisplayMode().getWidth(), screen.getDisplayMode().getHeight()));
									ImageIO.write(image, properties.getProperty("ScreenshotImageFormat"), DestFile);
								} catch (HeadlessException e) {
									throw new FrameworkException("Take Screenshot",
											"Unable to capture screenshot using Robot class due to exception :: "
													+ e.getLocalizedMessage());
								} catch (AWTException e) {
									throw new FrameworkException("Take Screenshot",
											"Unable to capture screenshot using Robot class due to exception :: "
													+ e.getLocalizedMessage());
								}

								break;
							case "webdriverwithannotation":

								String dPattern = Settings.getInstance().getProperty("DateFormatString");
								BufferedImage img = ImageIO.read(SrcFile);
								Graphics g = img.getGraphics();
								//FontMetrics fm = g.getFontMetrics();
								LocalDateTime istNow = LocalDateTime.now();
								DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dPattern);
								g.setFont(new Font("Serif", Font.BOLD, 25));
								String dt = istNow.format(dateTimeFormatter);
								dt = "IST :" + dt;
								g.setColor(Color.RED);
								g.drawString(dt, 10, img.getHeight() - 20);
								ZoneId pstZone = ZoneId.of("America/Los_Angeles");
								LocalDateTime pstDateTime = istNow.atZone(ZoneId.systemDefault())
										.withZoneSameInstant(pstZone).toLocalDateTime();
								dt = pstDateTime.format(dateTimeFormatter);
								dt = "PST :" + dt;
								g.drawString(dt, 10, img.getHeight() - 50);
								ImageIO.write(img, "png", DestFile);
								break;
							case "webdriver":
								FileUtils.copyFile(SrcFile, DestFile);
								break;
							}
						} catch (Exception e) {
							// TODO: handle exception
						}
						screenshotsName.add(scrn + "_scroll" + (i + 1) + ".png");
						currentWindowScroll = currentWindowScroll + lngWindowHeight;
						intTotalScreenshots++;
						i++;
					} while (currentWindowScroll <= lngWebpageHeight);

					System.setProperty("ScreenshotsCount", String.valueOf(intTotalScreenshots));
					jsExec.executeScript("window.scrollBy(0,-document.body.scrollHeight)");
				}
				blnExit = true;
			} catch (Exception ex) {
				ex.printStackTrace();
				throw new FrameworkException("Error while capturing the screenshot");
			}

			break;

		case PERFECTO:
			try {

				if ("HtmlUnitDriver".equals(driver.getWebDriver().getClass().getSimpleName())
						|| "class org.openqa.selenium.htmlunit.HtmlUnitDriver"
								.equals(driver.getWebDriver().getClass().getGenericSuperclass().toString())) {
					return null; // Screenshots not supported in headless mode
				}

				if ("RemoteWebDriver".equals(driver.getWebDriver().getClass().getSimpleName())) {
					Capabilities capabilities = ((RemoteWebDriver) driver.getWebDriver()).getCapabilities();
					if ("htmlunit".equals(capabilities.getBrowserName())) {
						return null; // Screenshots not supported in headless
								// mode
					}
					WebDriver augmentedDriver = new Augmenter().augment(driver.getWebDriver());
					scrFile = ((TakesScreenshot) augmentedDriver).getScreenshotAs(OutputType.FILE);
				} else {
					scrFile = ((TakesScreenshot) driver.getWebDriver()).getScreenshotAs(OutputType.FILE);

				}
			} catch (Exception ex) {
				ex.printStackTrace();
			}
			break;
		default:
			throw new FrameworkException("Take Screenshot", "Invalid Execution Mode for screenshot handling");
		}

		if (!blnExit) {
			try {
				FileUtils.copyFile(scrFile, new File(screenshotPath), true);
			} catch (IOException e) {
				e.printStackTrace();
				throw new FrameworkException("Error while writing screenshot to file");
			}
		}
		return screenshotsName;
	}

}