<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="YescartaHCP_Suite">

	<test name="Test under EntireSuite_Suite1" thread-count="1"
		parallel="classes">
		<parameter name="RunID" value="0" />
		<!--<test name="Test under EntireSuite_Suite1" thread-count="1" > -->
		<classes>
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Body_On_YescartaHCP_HomePage" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy_HomePage" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy2L_Page" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Efficacy3L_Page" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Footer_On_YescartaHCP" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Headers_On_YescartaHCP" />	
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Manufacturing_And_Process_page" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_PatientID_Site" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Safety_Site" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_The_Resource_Page" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Treatment_Page" />
			<class
				name="com.gilead.testscripts.YescartaHCP.To_Verify_Urgency_To_Treat_Page" />													
		</classes>

	</test>
</suite>