<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Tecartus_Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="79a2a1">  <td>25/04/22 11:48:10</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PDF_Download_Verification_for_tecartus_Website.PDFverification()[pri:2, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">PDFverification</td> 
  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="b0fd6f">  <td>25/04/22 11:48:29</td>   <td>18373</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PI_Integration_and_Navigation_Verification_for_tecartus_Website.PIandNavigationVerification()[pri:2, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">PIandNavigationVerification</td> 
  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:57:42</td>   <td>572187</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@900879357</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:55</td>   <td>-14785</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:53:09</td>   <td>299028</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:15</td>   <td>4509</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:39</td>   <td>28714</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:54:11</td>   <td>360471</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:56:22</td>   <td>492032</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread9@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:40</td>   <td>-30389</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:50:21</td>   <td>130560</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:53:07</td>   <td>296914</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:13</td>   <td>-57347</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:48</td>   <td>-21696</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:48</td>   <td>-21696</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:50:19</td>   <td>129143</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:57:41</td>   <td>571027</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@900879357</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:54:08</td>   <td>357557</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:08</td>   <td>-2660</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:50:19</td>   <td>129143</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:13</td>   <td>-57347</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:53:07</td>   <td>296914</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:25</td>   <td>15125</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:56:21</td>   <td>490992</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread9@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:56:21</td>   <td>490992</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread9@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:54:08</td>   <td>357557</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:08</td>   <td>-2660</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:25</td>   <td>15125</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:57:41</td>   <td>571027</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@900879357</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:57:42</td>   <td>572224</td> <td title="&lt;&lt;BaseTest.afterSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&lt;&lt;afterSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:50:21</td>   <td>130563</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:40</td>   <td>-30380</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:55</td>   <td>-14782</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:45:32</td>   <td>-158670</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-3@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:53:09</td>   <td>299031</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:45:32</td>   <td>-158668</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@900879357</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:39</td>   <td>28729</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:15</td>   <td>4512</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:45:32</td>   <td>-158671</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-2@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:46:31</td>   <td>-99587</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@900879357</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:46:29</td>   <td>-101064</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:46:06</td>   <td>-123980</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:50:37</td>   <td>146780</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:59</td>   <td>49070</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread7@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:10</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:52</td>   <td>-18452</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:53:20</td>   <td>309958</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread9@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:46:31</td>   <td>-99587</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@900879357</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:10</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:29</td>   <td>18373</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:29</td>   <td>18373</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:47:52</td>   <td>-18452</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:50:37</td>   <td>146780</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:53:20</td>   <td>309958</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread9@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:46:06</td>   <td>-123980</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@194151854</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:48:59</td>   <td>49070</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread7@373245906</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/22 11:46:29</td>   <td>-101064</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/22 11:50:27</td>   <td>136934</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_tecartus_managing_side_effects_site.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">invokeURL</td> 
  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/22 11:45:55</td>   <td>-134957</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_tecartus_at_a_glance_site.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">invokeURL</td> 
  <td>Thread1@194151854</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/22 11:45:49</td>   <td>-141500</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_receiving_tecartus_site.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">invokeURL</td> 
  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="b0fd6f">  <td>25/04/22 11:48:21</td>   <td>10779</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PI_Integration_and_Navigation_Verification_for_tecartus_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">invokeURL</td> 
  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/22 11:45:50</td>   <td>-140495</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Navigation_and_Functionality_Verification_for_Tecartus_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">invokeURL</td> 
  <td>Thread3@900879357</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/22 11:47:47</td>   <td>-22990</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_tecartus_clinical_trial_results_site.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">invokeURL</td> 
  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="79a2a1">  <td>25/04/22 11:48:02</td>   <td>-8347</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PDF_Download_Verification_for_tecartus_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">invokeURL</td> 
  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/22 11:53:14</td>   <td>304159</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_tecartus_support_and_resources_site.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">invokeURL</td> 
  <td>Thread9@194151854</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/22 11:48:51</td>   <td>40569</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">invokeURL</td> 
  <td>Thread7@373245906</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/22 11:48:59</td>   <td>49070</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website.navigationVerification()[pri:2, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">navigationVerification</td> 
  <td>Thread7@373245906</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:45:32</td>   <td>-158670</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@900879357</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:45:32</td>   <td>-158671</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-3@194151854</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:45:32</td>   <td>-158671</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-2@373245906</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:47:40</td>   <td>-30380</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@194151854</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:53:09</td>   <td>299031</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:47:55</td>   <td>-14782</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:50:21</td>   <td>130562</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@194151854</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:48:39</td>   <td>28726</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@373245906</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:48:15</td>   <td>4511</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:45:31</td>   <td>-158905</td> <td title="&gt;&gt;CRAFTLiteTestCase.setUpTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&gt;&gt;setUpTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/22 11:57:43</td>   <td>572455</td> <td title="&lt;&lt;CRAFTLiteTestCase.tearDownTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">&lt;&lt;tearDownTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/22 11:47:52</td>   <td>-18452</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_tecartus_clinical_trial_results_site.verifyClinicalTrialResults()[pri:2, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096]">verifyClinicalTrialResults</td> 
  <td>Thread4@194151854</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/22 11:50:37</td>   <td>146780</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_tecartus_managing_side_effects_site.verifyManagingSideEffectsSite()[pri:2, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3]">verifyManagingSideEffectsSite</td> 
  <td>Thread8@194151854</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/22 11:46:31</td>   <td>-99587</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Navigation_and_Functionality_Verification_for_Tecartus_Website.verifyNavigationHeaders()[pri:2, instance:com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be]">verifyNavigationHeaders</td> 
  <td>Thread3@900879357</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/22 11:46:29</td>   <td>-101064</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_receiving_tecartus_site.verifyReceivingYescarta()[pri:2, instance:com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a]">verifyReceivingYescarta</td> 
  <td>Thread2@373245906</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/22 11:53:20</td>   <td>309958</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_tecartus_support_and_resources_site.verifySupportAndResourcesSite()[pri:2, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df]">verifySupportAndResourcesSite</td> 
  <td>Thread9@194151854</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/22 11:46:06</td>   <td>-123980</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_tecartus_at_a_glance_site.verifyYescartaSite()[pri:2, instance:com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b]">verifyYescartaSite</td> 
  <td>Thread1@194151854</td>   <td></td> </tr>
</table>
