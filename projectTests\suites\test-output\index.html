<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports1.css" rel="stylesheet" id="ultra" />
    <link type="text/css" href="testng-reports.css" rel="stylesheet" id="retro" disabled="false"/>
    <script type="text/javascript" src="jquery.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>
    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <button class="button" id="button">Switch Retro Theme</button> <!-- button -->
      <br/>
      <span class="top-banner-font-1">1 suite, 3 failed tests</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" class="collapse-all-link" title="Collapse/expand all the suites">
          <img class="collapse-all-icon" src="collapseall.gif">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" class="navigator-link" panel-name="suite-FunctionalRegression_Suite">
              <span class="suite-name border-failed">FunctionalRegression_Suite</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" class="navigator-link " panel-name="test-xml-FunctionalRegression_Suite">
                    <span>C:\Users\<USER>\git\DX\DigitalExperience\projectTests\suites\FunctionalRegression.xml</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="testlist-FunctionalRegression_Suite">
                    <span class="test-stats">1 test</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="group-FunctionalRegression_Suite">
                    <span>0 groups</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="times-FunctionalRegression_Suite">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="reporter-FunctionalRegression_Suite">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="ignored-methods-FunctionalRegression_Suite">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="chronological-FunctionalRegression_Suite">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">12 methods, 3 failed,   9 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title failed">Failed methods</span>
                    <span class="show-or-hide-methods failed">
                      <a href="#" panel-name="suite-FunctionalRegression_Suite" class="hide-methods failed suite-FunctionalRegression_Suite"> (hide)</a> <!-- hide-methods failed suite-FunctionalRegression_Suite -->
                      <a href="#" panel-name="suite-FunctionalRegression_Suite" class="show-methods failed suite-FunctionalRegression_Suite"> (show)</a> <!-- show-methods failed suite-FunctionalRegression_Suite -->
                    </span>
                    <div class="method-list-content failed suite-FunctionalRegression_Suite">
                      <span>
                        <img width="3%" src="failed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003" hash-for-method="invokeURL">invokeURL</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="failed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002" hash-for-method="invokeURL">invokeURL</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="failed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004" hash-for-method="verifyCriticalComponents">verifyCriticalComponents</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content failed suite-FunctionalRegression_Suite -->
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-FunctionalRegression_Suite" class="hide-methods passed suite-FunctionalRegression_Suite"> (hide)</a> <!-- hide-methods passed suite-FunctionalRegression_Suite -->
                      <a href="#" panel-name="suite-FunctionalRegression_Suite" class="show-methods passed suite-FunctionalRegression_Suite"> (show)</a> <!-- show-methods passed suite-FunctionalRegression_Suite -->
                    </span>
                    <div class="method-list-content passed suite-FunctionalRegression_Suite">
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001" hash-for-method="invokeURL">invokeURL</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004" hash-for-method="invokeURL">invokeURL</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006" hash-for-method="invokeURL">invokeURL</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005" hash-for-method="invokeURL">invokeURL</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003" hash-for-method="verifyCriticalComponents">verifyCriticalComponents</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005" hash-for-method="verifyCriticalComponents">verifyCriticalComponents</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006" hash-for-method="verifyCriticalComponents">verifyCriticalComponents</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002" hash-for-method="verifyCriticalComponents">verifyCriticalComponents</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-FunctionalRegression_Suite" title="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001" hash-for-method="verifyCriticalComponents">verifyCriticalComponents</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-FunctionalRegression_Suite -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-FunctionalRegression_Suite" class="panel FunctionalRegression_Suite">
          <div class="suite-FunctionalRegression_Suite-class-failed">
            <div class="main-panel-header rounded-window-top">
              <img src="failed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="invokeURL">
                  </a> <!-- invokeURL -->
                  <span class="method-name">invokeURL</span>
                  <div class="stack-trace">com.gilead.config.FrameworkException: The specified sheet &quot;SmokeCICD&quot;does not exist within the workbook &quot;Thread4FunctionalRegression_Gileadadvancingaccess_HCP_PharmacyFinder_TC002_Instance1.xlsx&quot;
	at com.gilead.maintenance.ExcelDataAccess.getWorkSheet(ExcelDataAccess.java:139)
	at com.gilead.maintenance.ExcelDataAccess.getRowNum(ExcelDataAccess.java:163)
	at com.gilead.maintenance.CraftDataTable.getData(CraftDataTable.java:120)
	at businesscomponents.CommonFunctions.preCondition(CommonFunctions.java:785)
	at businesscomponents.CommonFunctions.launchApplication(CommonFunctions.java:1117)
	at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002.invokeURL(Gileadadvancingaccess_HCP_PharmacyFinder_TC002.java:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames
</div> <!-- stack-trace -->
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-failed -->
          <div class="suite-FunctionalRegression_Suite-class-failed">
            <div class="main-panel-header rounded-window-top">
              <img src="failed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="verifyCriticalComponents">
                  </a> <!-- verifyCriticalComponents -->
                  <span class="method-name">verifyCriticalComponents</span>
                  <div class="stack-trace">org.openqa.selenium.NoSuchElementException: no such element: Unable to locate element: {&quot;method&quot;:&quot;xpath&quot;,&quot;selector&quot;:&quot; //div[@class=&apos;navbar-collapse collapse&apos;]//a[contains(text(),&apos;Activate&apos;)]&quot;}
  (Session info: chrome=133.0.6943.99)
For documentation on this error, please visit: https://selenium.dev/exceptions/#no_such_element
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [fbcabfabcaf3acee2648df9c79f95c44, findElement {using=xpath, value= //div[@class=&apos;navbar-collapse collapse&apos;]//a[contains(text(),&apos;Activate&apos;)]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:54986}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:54986/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: fbcabfabcaf3acee2648df9c79f95c44
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElement(ElementLocation.java:162)
	at org.openqa.selenium.remote.ElementLocation.findElement(ElementLocation.java:60)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:382)
	at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:374)
	at com.gilead.reports.CraftDriver.findElement(CraftDriver.java:164)
	at businesscomponents.CommonFunctions.verifyLinksInWebPage(CommonFunctions.java:1137)
	at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004.verifyCriticalComponents(Gileadadvancingaccess_Patient_CopayCouponpage_TC004.java:29)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 16 stack frames
</div> <!-- stack-trace -->
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-failed -->
          <div class="suite-FunctionalRegression_Suite-class-failed">
            <div class="main-panel-header rounded-window-top">
              <img src="failed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="invokeURL">
                  </a> <!-- invokeURL -->
                  <span class="method-name">invokeURL</span>
                  <div class="stack-trace">com.gilead.config.FrameworkException: The specified sheet &quot;SmokeCICD&quot;does not exist within the workbook &quot;Thread5FunctionalRegression_Gileadadvancingaccess_HCP_FAQs_TC003_Instance1.xlsx&quot;
	at com.gilead.maintenance.ExcelDataAccess.getWorkSheet(ExcelDataAccess.java:139)
	at com.gilead.maintenance.ExcelDataAccess.getRowNum(ExcelDataAccess.java:163)
	at com.gilead.maintenance.CraftDataTable.getData(CraftDataTable.java:120)
	at businesscomponents.CommonFunctions.preCondition(CommonFunctions.java:785)
	at businesscomponents.CommonFunctions.launchApplication(CommonFunctions.java:1117)
	at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003.invokeURL(Gileadadvancingaccess_HCP_FAQs_TC003.java:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames
</div> <!-- stack-trace -->
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-failed -->
          <div class="suite-FunctionalRegression_Suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="verifyCriticalComponents">
                  </a> <!-- verifyCriticalComponents -->
                  <span class="method-name">verifyCriticalComponents</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-passed -->
          <div class="suite-FunctionalRegression_Suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="invokeURL">
                  </a> <!-- invokeURL -->
                  <span class="method-name">invokeURL</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="verifyCriticalComponents">
                  </a> <!-- verifyCriticalComponents -->
                  <span class="method-name">verifyCriticalComponents</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-passed -->
          <div class="suite-FunctionalRegression_Suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="invokeURL">
                  </a> <!-- invokeURL -->
                  <span class="method-name">invokeURL</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="verifyCriticalComponents">
                  </a> <!-- verifyCriticalComponents -->
                  <span class="method-name">verifyCriticalComponents</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-passed -->
          <div class="suite-FunctionalRegression_Suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="invokeURL">
                  </a> <!-- invokeURL -->
                  <span class="method-name">invokeURL</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-passed -->
          <div class="suite-FunctionalRegression_Suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="verifyCriticalComponents">
                  </a> <!-- verifyCriticalComponents -->
                  <span class="method-name">verifyCriticalComponents</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-passed -->
          <div class="suite-FunctionalRegression_Suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="invokeURL">
                  </a> <!-- invokeURL -->
                  <span class="method-name">invokeURL</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="verifyCriticalComponents">
                  </a> <!-- verifyCriticalComponents -->
                  <span class="method-name">verifyCriticalComponents</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-FunctionalRegression_Suite-class-passed -->
        </div> <!-- panel FunctionalRegression_Suite -->
        <div panel-name="test-xml-FunctionalRegression_Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">C:\Users\<USER>\git\DX\DigitalExperience\projectTests\suites\FunctionalRegression.xml</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;https://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite guice-stage=&quot;DEVELOPMENT&quot; name=&quot;FunctionalRegression_Suite&quot;&gt;
  &lt;test thread-count=&quot;1&quot; name=&quot;Test under EntireSuite_Suite1&quot; parallel=&quot;classes&quot;&gt;
    &lt;parameter name=&quot;RunID&quot; value=&quot;0&quot;/&gt;
    &lt;classes&gt;
      &lt;class name=&quot;com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005&quot;/&gt;
      &lt;class name=&quot;com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006&quot;/&gt;
      &lt;class name=&quot;com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004&quot;/&gt;
      &lt;class name=&quot;com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003&quot;/&gt;
      &lt;class name=&quot;com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002&quot;/&gt;
      &lt;class name=&quot;com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- Test under EntireSuite_Suite1 --&gt;
&lt;/suite&gt; &lt;!-- FunctionalRegression_Suite --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-FunctionalRegression_Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for FunctionalRegression_Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">Test under EntireSuite_Suite1 (6 classes)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-FunctionalRegression_Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for FunctionalRegression_Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-FunctionalRegression_Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for FunctionalRegression_Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_FunctionalRegression_Suite');
function tableData_FunctionalRegression_Suite() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(12);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'verifyCriticalComponents')
data.setCell(0, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001')
data.setCell(0, 3, 15703);
data.setCell(1, 0, 1)
data.setCell(1, 1, 'verifyCriticalComponents')
data.setCell(1, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002')
data.setCell(1, 3, 15341);
data.setCell(2, 0, 2)
data.setCell(2, 1, 'verifyCriticalComponents')
data.setCell(2, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004')
data.setCell(2, 3, 13697);
data.setCell(3, 0, 3)
data.setCell(3, 1, 'verifyCriticalComponents')
data.setCell(3, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005')
data.setCell(3, 3, 9532);
data.setCell(4, 0, 4)
data.setCell(4, 1, 'verifyCriticalComponents')
data.setCell(4, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003')
data.setCell(4, 3, 8167);
data.setCell(5, 0, 5)
data.setCell(5, 1, 'invokeURL')
data.setCell(5, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005')
data.setCell(5, 3, 6898);
data.setCell(6, 0, 6)
data.setCell(6, 1, 'verifyCriticalComponents')
data.setCell(6, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006')
data.setCell(6, 3, 6048);
data.setCell(7, 0, 7)
data.setCell(7, 1, 'invokeURL')
data.setCell(7, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001')
data.setCell(7, 3, 5674);
data.setCell(8, 0, 8)
data.setCell(8, 1, 'invokeURL')
data.setCell(8, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002')
data.setCell(8, 3, 5431);
data.setCell(9, 0, 9)
data.setCell(9, 1, 'invokeURL')
data.setCell(9, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004')
data.setCell(9, 3, 5227);
data.setCell(10, 0, 10)
data.setCell(10, 1, 'invokeURL')
data.setCell(10, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006')
data.setCell(10, 3, 5212);
data.setCell(11, 0, 11)
data.setCell(11, 1, 'invokeURL')
data.setCell(11, 2, 'com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003')
data.setCell(11, 3, 4829);
window.suiteTableData['FunctionalRegression_Suite']= { tableData: data, tableDiv: 'times-div-FunctionalRegression_Suite'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 1 minutes</span>
              <div id="times-div-FunctionalRegression_Suite">
              </div> <!-- times-div-FunctionalRegression_Suite -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-FunctionalRegression_Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for FunctionalRegression_Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-FunctionalRegression_Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">15 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="ignored-class-div">
              <span class="ignored-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001</span>
              <div class="ignored-methods-div">
                <span class="ignored-method-name">afterSuite</span>
                <br/>
                <span class="ignored-method-name">tearDownTestSuite</span>
                <br/>
                <span class="ignored-method-name">setUpTestSuite</span>
                <br/>
              </div> <!-- ignored-methods-div -->
            </div> <!-- ignored-class-div -->
            <div class="ignored-class-div">
              <span class="ignored-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002</span>
              <div class="ignored-methods-div">
                <span class="ignored-method-name">afterSuite</span>
                <br/>
                <span class="ignored-method-name">tearDownTestSuite</span>
                <br/>
                <span class="ignored-method-name">setUpTestSuite</span>
                <br/>
              </div> <!-- ignored-methods-div -->
            </div> <!-- ignored-class-div -->
            <div class="ignored-class-div">
              <span class="ignored-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006</span>
              <div class="ignored-methods-div">
                <span class="ignored-method-name">afterSuite</span>
                <br/>
                <span class="ignored-method-name">tearDownTestSuite</span>
                <br/>
                <span class="ignored-method-name">setUpTestSuite</span>
                <br/>
              </div> <!-- ignored-methods-div -->
            </div> <!-- ignored-class-div -->
            <div class="ignored-class-div">
              <span class="ignored-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004</span>
              <div class="ignored-methods-div">
                <span class="ignored-method-name">afterSuite</span>
                <br/>
                <span class="ignored-method-name">tearDownTestSuite</span>
                <br/>
                <span class="ignored-method-name">setUpTestSuite</span>
                <br/>
              </div> <!-- ignored-methods-div -->
            </div> <!-- ignored-class-div -->
            <div class="ignored-class-div">
              <span class="ignored-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003</span>
              <div class="ignored-methods-div">
                <span class="ignored-method-name">afterSuite</span>
                <br/>
                <span class="ignored-method-name">tearDownTestSuite</span>
                <br/>
                <span class="ignored-method-name">setUpTestSuite</span>
                <br/>
              </div> <!-- ignored-methods-div -->
            </div> <!-- ignored-class-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-FunctionalRegression_Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005</div> <!-- chronological-class-name -->
              <div class="configuration-suite before">
                <span class="method-name">setUpTestSuite(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">0 ms</span>
              </div> <!-- configuration-suite before -->
              <div class="configuration-class before">
                <span class="method-name">setUpTestRunner(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">362 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-class before">
                <span class="method-name">beforeClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">362 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">16318 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">invokeURL</span>
                <span class="method-start">16319 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=invokeURL status=SUCCESS method=Gileadadvancingaccess_PatientPharmacyfinder_TC005.i...)</span>
                <span class="method-start">23221 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">23224 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">verifyCriticalComponents</span>
                <span class="method-start">23224 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=verifyCriticalComponents status=SUCCESS method=Gileadadvancingaccess_PatientPharmac...)</span>
                <span class="method-start">32762 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-class after">
                <span class="method-name">afterClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">35662 ms</span>
              </div> <!-- configuration-class after -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006</div> <!-- chronological-class-name -->
              <div class="configuration-class before">
                <span class="method-name">beforeClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">35680 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-class before">
                <span class="method-name">setUpTestRunner(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">35680 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">45393 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">invokeURL</span>
                <span class="method-start">45394 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=invokeURL status=SUCCESS method=Gileadadvancingaccess_Patient_FAQs_TC006.invokeURL(...)</span>
                <span class="method-start">50612 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">50613 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">verifyCriticalComponents</span>
                <span class="method-start">50613 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=verifyCriticalComponents status=SUCCESS method=Gileadadvancingaccess_Patient_FAQs_T...)</span>
                <span class="method-start">56663 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-class after">
                <span class="method-name">afterClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">59484 ms</span>
              </div> <!-- configuration-class after -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001</div> <!-- chronological-class-name -->
              <div class="configuration-class before">
                <span class="method-name">setUpTestRunner(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">59487 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-class before">
                <span class="method-name">beforeClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">59488 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">64868 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">invokeURL</span>
                <span class="method-start">64868 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=invokeURL status=SUCCESS method=Gileadadvancingaccess_HCP_CopayCoupon_TC001.invokeU...)</span>
                <span class="method-start">70543 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">70545 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">verifyCriticalComponents</span>
                <span class="method-start">70545 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=verifyCriticalComponents status=SUCCESS method=Gileadadvancingaccess_HCP_CopayCoupo...)</span>
                <span class="method-start">86249 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-class after">
                <span class="method-name">afterClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">87551 ms</span>
              </div> <!-- configuration-class after -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002</div> <!-- chronological-class-name -->
              <div class="configuration-class before">
                <span class="method-name">beforeClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">87554 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-class before">
                <span class="method-name">setUpTestRunner(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">87554 ms</span>
              </div> <!-- configuration-class before -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">invokeURL</span>
                <span class="method-start">96698 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">96698 ms</span>
              </div> <!-- configuration-method before -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=invokeURL status=FAILURE method=Gileadadvancingaccess_HCP_PharmacyFinder_TC002.invo...)</span>
                <span class="method-start">102158 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">102160 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">verifyCriticalComponents</span>
                <span class="method-start">102160 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=verifyCriticalComponents status=SUCCESS method=Gileadadvancingaccess_HCP_PharmacyFi...)</span>
                <span class="method-start">117514 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-class after">
                <span class="method-name">afterClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">118539 ms</span>
              </div> <!-- configuration-class after -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003</div> <!-- chronological-class-name -->
              <div class="configuration-class before">
                <span class="method-name">setUpTestRunner(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">118543 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-class before">
                <span class="method-name">beforeClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">118543 ms</span>
              </div> <!-- configuration-class before -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">invokeURL</span>
                <span class="method-start">128195 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">128195 ms</span>
              </div> <!-- configuration-method before -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=invokeURL status=FAILURE method=Gileadadvancingaccess_HCP_FAQs_TC003.invokeURL()[pr...)</span>
                <span class="method-start">133052 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">133053 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">verifyCriticalComponents</span>
                <span class="method-start">133053 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=verifyCriticalComponents status=SUCCESS method=Gileadadvancingaccess_HCP_FAQs_TC003...)</span>
                <span class="method-start">141224 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-class after">
                <span class="method-name">afterClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">144458 ms</span>
              </div> <!-- configuration-class after -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004</div> <!-- chronological-class-name -->
              <div class="configuration-class before">
                <span class="method-name">beforeClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">144464 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-class before">
                <span class="method-name">setUpTestRunner(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">144464 ms</span>
              </div> <!-- configuration-class before -->
              <div class="test-method">
                <span class="method-name">invokeURL</span>
                <span class="method-start">149188 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">149188 ms</span>
              </div> <!-- configuration-method before -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=invokeURL status=SUCCESS method=Gileadadvancingaccess_Patient_CopayCouponpage_TC004...)</span>
                <span class="method-start">154417 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(org.testng.TestRunner@2b9ed6da, public void com.gilead.testscripts.FunctionalRegression.Gileadadvanc...)</span>
                <span class="method-start">154418 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">verifyCriticalComponents</span>
                <span class="method-start">154418 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">afterMethod([TestResult name=verifyCriticalComponents status=FAILURE method=Gileadadvancingaccess_Patient_CopayC...)</span>
                <span class="method-start">168120 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-class after">
                <span class="method-name">afterClass(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">169117 ms</span>
              </div> <!-- configuration-class after -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005</div> <!-- chronological-class-name -->
              <div class="configuration-suite after">
                <span class="method-name">afterSuite(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">169188 ms</span>
              </div> <!-- configuration-suite after -->
              <div class="configuration-suite after">
                <span class="method-name">tearDownTestSuite(org.testng.TestRunner@2b9ed6da)</span>
                <span class="method-start">169739 ms</span>
              </div> <!-- configuration-suite after -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
<script type="text/javascript" src="testng-reports2.js"></script>
</html>
