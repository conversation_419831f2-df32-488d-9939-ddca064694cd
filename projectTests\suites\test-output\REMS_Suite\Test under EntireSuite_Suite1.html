<html>
<head>
<title>TestNG:  Test under EntireSuite_Suite1</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Test under EntireSuite_Suite1</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>3/3/0</td>
</tr><tr>
<td>Started on:</td><td>Tue May 13 20:32:55 IST 2025</td>
</tr>
<tr><td>Total time:</td><td>177 seconds (177077 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED CONFIGURATIONS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS.afterMethod()'><b>afterMethod</b><br>Test class: com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS<br>Parameters: [TestResult name=toVerifyYescartaTecartusREMSSite status=FAILURE method=To_Download_Resources_From_YescartaTecartusREMS.toVerifyYescartaTecartusREMSSite()[pri:2, instance:com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738] output={null}], public void com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS.toVerifyYescartaTecartusREMSSite() throws java.lang.Exception</td>
<td><div><pre>org.openqa.selenium.WebDriverException: Timed out waiting for driver server to stop.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [ef0e09041baa69c443d80b1ea80ee2fe, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:63219}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:63219/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: ef0e09041baa69c443d80b1ea80ee2fe
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:132)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
	at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
	at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
	at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
	at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
	at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.ExecutionException: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: driver.version: unknown
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:128)
	... 29 more
Caused by: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: driver.version: unknown
	at org.openqa.selenium.os.OsProcess.waitFor(OsProcess.java:174)
	at org.openqa.selenium.os.CommandLine.waitFor(CommandLine.java:127)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.lambda$execute$2(DriverCommandExecutor.java:122)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)
	... 3 more
... Removed 17 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace339226305", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace339226305'><pre>org.openqa.selenium.WebDriverException: Timed out waiting for driver server to stop.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [ef0e09041baa69c443d80b1ea80ee2fe, quit {}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:63219}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:63219/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: ef0e09041baa69c443d80b1ea80ee2fe
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:132)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:613)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:617)
	at org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:454)
	at org.openqa.selenium.chromium.ChromiumDriver.quit(ChromiumDriver.java:293)
	at com.gilead.reports.CraftDriver.quit(CraftDriver.java:315)
	at com.gilead.base.DriverScript.quitWebDriver(DriverScript.java:349)
	at com.gilead.base.DriverScript.wrapUp(DriverScript.java:362)
	at com.gilead.base.BaseTest.afterMethod(BaseTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:62)
	at org.testng.internal.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:385)
	at org.testng.internal.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:321)
	at org.testng.internal.TestInvoker.runConfigMethods(TestInvoker.java:700)
	at org.testng.internal.TestInvoker.runAfterGroupsConfigurations(TestInvoker.java:676)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:660)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.ExecutionException: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: driver.version: unknown
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:128)
	... 29 more
Caused by: org.openqa.selenium.TimeoutException: Process timed out after waiting for 20000 ms.
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: driver.version: unknown
	at org.openqa.selenium.os.OsProcess.waitFor(OsProcess.java:174)
	at org.openqa.selenium.os.CommandLine.waitFor(CommandLine.java:127)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.lambda$execute$2(DriverCommandExecutor.java:122)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)
	... 3 more
</pre></div></td>
<td>25</td>
<td>com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site.toVerifyYescartaTecartusREMSSite()'><b>toVerifyYescartaTecartusREMSSite</b><br>Test class: com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site</td>
<td><div><pre>com.gilead.config.FrameworkAssertion: &apos;1747148740249.pdf&apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
	at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
	at com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site.toVerifyYescartaTecartusREMSSite(To_Verify_YescartaTecartusREMS_Site.java:31)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace946237908", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace946237908'><pre>com.gilead.config.FrameworkAssertion: &apos;1747148740249.pdf&apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
	at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
	at com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site.toVerifyYescartaTecartusREMSSite(To_Verify_YescartaTecartusREMS_Site.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>128</td>
<td>com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418</td></tr>
<tr>
<td title='com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.toVerifyCriticalComponentsYescartaTecartusREMSSite()'><b>toVerifyCriticalComponentsYescartaTecartusREMSSite</b><br>Test class: com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site</td>
<td><div><pre>com.gilead.config.FrameworkAssertion: &apos;1747148646482.pdf&apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
	at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
	at com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.toVerifyCriticalComponentsYescartaTecartusREMSSite(To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.java:31)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1078030181", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1078030181'><pre>com.gilead.config.FrameworkAssertion: &apos;1747148646482.pdf&apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
	at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
	at com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.toVerifyCriticalComponentsYescartaTecartusREMSSite(To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>39</td>
<td>com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c</td></tr>
<tr>
<td title='com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS.toVerifyYescartaTecartusREMSSite()'><b>toVerifyYescartaTecartusREMSSite</b><br>Test class: com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS</td>
<td><div><pre>java.lang.RuntimeException: Error while verifying file download &apos;yescarta-pi&apos; on page &apos;REMS&apos;: Downloaded file &apos;yescarta-pi&apos; not found in path &apos;C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles&apos;
	at businesscomponents.CommonFunctions.handleFileDownload(CommonFunctions.java:4156)
	at businesscomponents.CommonFunctions.verifyYescartaTecartusREMSSite(CommonFunctions.java:5732)
	at com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS.toVerifyYescartaTecartusREMSSite(To_Download_Resources_From_YescartaTecartusREMS.java:29)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.RuntimeException: Downloaded file &apos;yescarta-pi&apos; not found in path &apos;C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles&apos;
	at businesscomponents.CommonFunctions.handleFileDownload(CommonFunctions.java:4152)
	... 17 more
... Removed 12 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1845542504", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1845542504'><pre>java.lang.RuntimeException: Error while verifying file download &apos;yescarta-pi&apos; on page &apos;REMS&apos;: Downloaded file &apos;yescarta-pi&apos; not found in path &apos;C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles&apos;
	at businesscomponents.CommonFunctions.handleFileDownload(CommonFunctions.java:4156)
	at businesscomponents.CommonFunctions.verifyYescartaTecartusREMSSite(CommonFunctions.java:5732)
	at com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS.toVerifyYescartaTecartusREMSSite(To_Download_Resources_From_YescartaTecartusREMS.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.RuntimeException: Downloaded file &apos;yescarta-pi&apos; not found in path &apos;C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles&apos;
	at businesscomponents.CommonFunctions.handleFileDownload(CommonFunctions.java:4152)
	... 17 more
</pre></div></td>
<td>30</td>
<td>com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site</td>
<td></td>
<td>17</td>
<td>com.gilead.testscripts.REMS.To_Verify_YescartaTecartusREMS_Site@1ca3b418</td></tr>
<tr>
<td title='com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS</td>
<td></td>
<td>18</td>
<td>com.gilead.testscripts.REMS.To_Download_Resources_From_YescartaTecartusREMS@5149d738</td></tr>
<tr>
<td title='com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site</td>
<td></td>
<td>13</td>
<td>com.gilead.testscripts.REMS.To_Verify_Critical_Components_From_YescartaTecartusREMS_Site@19e4653c</td></tr>
</table><p>
</body>
</html>