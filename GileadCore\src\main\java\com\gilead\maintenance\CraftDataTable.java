package com.gilead.maintenance;

import java.lang.invoke.MethodHandles;
import java.nio.channels.FileLock;
import java.util.Map;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.gilead.base.RunContext;
import com.gilead.config.FrameworkException;
import com.gilead.utils.ServiceRegister;

/**
 * Class to encapsulate the datatable related functions of the framework
 * 
 * <AUTHOR>
 */
public class CraftDataTable {
	public final String datatablePath, datatableName;
	private String dataReferenceIdentifier = "#";

	private String currentTestcase;
	public int currentIteration = 0, currentSubIteration = 0;
	private Properties properties = Settings.getInstance();
	// private static final Logger logger = LogManager.getLogger("loggers");
	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());
	private FileLockMechanism objFileLockMechanism = new FileLockMechanism(3);
	private ServiceRegister register = ServiceRegister.getInstance();
	/**
	 * Constructor to initialize the {@link CraftDataTable} object
	 * 
	 * @param datatablePath The path where the datatable is stored
	 * @param datatableName The name of the datatable file
	 */
	public CraftDataTable(String datatablePath, String datatableName) {
		this.datatablePath = datatablePath;

		String currentSuite = register.getService(Thread.currentThread().getName(), "CurrentSuite").toString(); 
		String suiteExecution = properties.getProperty("SequenceSuites");
		if (!suiteExecution.contains(currentSuite)) {
			this.datatableName = Thread.currentThread().getName() + datatableName;
			logger.info(String.format("Created excel file %s in  the path %s", datatablePath, datatableName));
		} else {
			this.datatableName = datatableName;
			logger.info(String.format("Current excel file %s in  the path %s", datatablePath, datatableName));
		}
	}

	/**
	 * Function to set the data reference identifier character
	 * 
	 * @param dataReferenceIdentifier The data reference identifier character
	 */
	public void setDataReferenceIdentifier(String dataReferenceIdentifier) {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		if (dataReferenceIdentifier.length() != 1) {
			almFunction.ThrowException("Validate data reference identifier",
					"The data reference identifier should be single character",
					"The data reference identifier is not single character!", false);
		}

		this.dataReferenceIdentifier = dataReferenceIdentifier;
	}

	/**
	 * Function to set the variables required to uniquely identify the exact row of
	 * data under consideration
	 * 
	 * @param currentTestcase     The ID of the current test case
	 * @param currentIteration    The Iteration being executed currently
	 * @param currentSubIteration The Sub-Iteration being executed currently
	 */
	public void setCurrentRow(String currentTestcase, int currentIteration, int currentSubIteration) {
		this.currentTestcase = currentTestcase;
		this.currentIteration = currentIteration;
		this.currentSubIteration = currentSubIteration;
	}

	private void checkPreRequisites() {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		if (currentTestcase == null) {
			almFunction.ThrowException("Validate CraftDataTable.currentTestCase",
					"CraftDataTable.currentTestCase should be set", "CraftDataTable.currentTestCase is not set!",
					false);
		}
		if (currentIteration == 0) {
			almFunction.ThrowException("Validate CraftDataTable.currentIteration",
					"CraftDataTable.currentIteration should be set", "CraftDataTable.currentIteration is not set!",
					false);
		}
		if (properties.getProperty("Approach").equalsIgnoreCase("KeywordDriven")) {
			if (currentSubIteration == 0) {
				almFunction.ThrowException("Validate CraftDataTable.currentSubIteration",
						"CraftDataTable.currentSubIteration should be set",
						"CraftDataTable.currentSubIteration is not set!", false);
			}
		}
		logger.info(String.format("Pre requisites validated for current test case %s,  current iteration is %s"
				+ " and currentSubIteration is %s", currentTestcase, currentIteration, currentSubIteration));
	}

	/**
	 * Function to return the test data value corresponding to the sheet name and
	 * field name passed
	 * 
	 * @param datasheetName The name of the sheet in which the data is present
	 * @param fieldName     The name of the field whose value is required
	 * @return The test data present in the field name specified
	 * @see #putData(String, String, String)
	 * @see #getExpectedResult(String)
	 */
	public String getData(String datasheetName, String fieldName) {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		checkPreRequisites();
		ExcelDataAccess testDataAccess = new ExcelDataAccess(datatablePath,datatableName);
		testDataAccess.setDatasheetName(datasheetName);
		logger.info(String.format("Get data from %s for thread - %s ", datatableName, Thread.currentThread().getName()));
		int rowNum = testDataAccess.getRowNum(currentTestcase, 0, 1); // Start
																		// at
																		// row
																		// 1,
																		// skipping
																		// the
																		// header
																		// row
		if (rowNum == -1) {
			logger.error(String.format(
					"Not able to fetch value from row number %s of field %s in datasheet %s for test case %s", rowNum,
					datasheetName, fieldName, currentTestcase));

			almFunction.ThrowException("Validate testcase in datasheet", "Testcase should be available in datasheet",
					String.format("Testcase %s is not available in %s", currentTestcase, datasheetName), false);
		}
		rowNum = testDataAccess.getRowNum(Integer.toString(currentIteration), 1, rowNum);
		if (rowNum == -1) {
			logger.error(String.format("Iteration number %s not found in datasheet %s for test case %s",
					currentIteration, datasheetName, currentTestcase));

			almFunction.ThrowException("Validate iteration of testcase in datasheet",
					"Iteration number in testcase should be available in datasheet",
					String.format("Iteration number %s in %s is not available in %s", currentIteration, currentTestcase,
							datasheetName),
					false);
		}
		if (properties.getProperty("Approach").equalsIgnoreCase("KeywordDriven")) {
			rowNum = testDataAccess.getRowNum(Integer.toString(currentSubIteration), 2, rowNum);
			if (rowNum == -1) {
				logger.error(String.format("Sub Iteration number %s not found in datasheet %s for test case %s",
						currentSubIteration, datasheetName, currentTestcase));
				almFunction.ThrowException("Validate sub iteration of testcase in datasheet",
						"Sub iteration number in testcase should be available in datasheet",
						String.format("Sub iteration number %s in %s is not available in %s", currentSubIteration,
								currentTestcase, datasheetName),
						false);
			}
		}

		String dataValue = testDataAccess.getValue(rowNum, fieldName);

		if (fieldName.equalsIgnoreCase("password")) {
			// logger.info(String.format("Fetched value %s from row number %s of field %s
			// from datasheet %s ", "*********", rowNum, fieldName, datasheetName));
		} else {
			logger.info(String.format("Fetched value %s from row number %s of field %s from datasheet %s  ", dataValue,
					rowNum, fieldName, datasheetName));
		}
		if (dataValue.startsWith(dataReferenceIdentifier)) {
			dataValue = getCommonData(fieldName, dataValue);

		}

		return dataValue;
	}

	private String getCommonData(String fieldName, String dataValue) {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		ExcelDataAccess commonDataAccess = new ExcelDataAccess(datatablePath, "Common Testdata");
		commonDataAccess.setDatasheetName("Common_Testdata");

		String dataReferenceId = dataValue.split(dataReferenceIdentifier)[1];

		int rowNum = commonDataAccess.getRowNum(dataReferenceId, 0, 1); // Start
																		// at
																		// row
																		// 1,
																		// skipping
																		// the
																		// header
																		// row
		if (rowNum == -1) {
			logger.error(
					String.format("Data not found in data sheet %s for row id %s", "Common_Testdata", dataReferenceId));

			almFunction.ThrowException("Validate dataReferenceId in Common_Testdata",
					"dataReferenceId should be available in Common_Testdata",
					String.format("dataReferenceId %s is not available in %s", dataReferenceId, "Common_Testdata"),
					false);
		}
		
		String commonData = "";
		synchronized (objFileLockMechanism) {
			FileLock objFileLock = objFileLockMechanism.SetLockOnFile("GetData");
			
			if (objFileLock != null) {
				commonData = commonDataAccess.getValue(rowNum, fieldName);
				logger.info(String.format("Fetched value %s row number %s of field %s from datasheet %s  ", commonData, rowNum,
						fieldName, "Common_Testdata"));
				objFileLockMechanism.ReleaseLockOnFile(objFileLock, "GetData");
			} else {
				throw new FrameworkException("Error", "Error in getting data from excel due to file lock exception");
			}
		}
		
		//String commonData = commonDataAccess.getValue(rowNum, fieldName);
//		logger.info(String.format("Fetched value %s row number %s of field %s from datasheet %s  ", commonData, rowNum,
//				fieldName, "Common_Testdata"));
		return commonData;
	}

	public String getBumbleebeeConfigValues(String dataValue) {
		ExcelDataAccess commonDataAccess = new ExcelDataAccess(datatablePath, "Bumblebee Config");
		commonDataAccess.setDatasheetName("Login_Info");

		int rowNum = commonDataAccess.getRowNum(dataValue, 0, 1); // Start
																	// at
																	// row
																	// 1,
																	// skipping
																	// the
																	// header
																	// row
		if (rowNum == -1) {
			throw new FrameworkException("Alm logged in user details for \"" + dataValue + "\""
					+ "is not found in the Bumblebee Config data sheet!");
		}

		return commonDataAccess.getValue(rowNum, "Encrypted_Password");
	}

	/**
	 * Function to output intermediate data (output values) into the specified sheet
	 * 
	 * @param datasheetName The name of the sheet into which the data is to be
	 *                      written
	 * @param fieldName     The name of the field into which the data is to be
	 *                      written
	 * @param dataValue     The value to be written into the field specified
	 * @see #getData(String, String)
	 */
	public void putData(String datasheetName, String fieldName, String dataValue) {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		String strLockFile = "Excel_Data";
		logger.info(String.format("PutData for Thread %s in file path", Thread.currentThread().getName(), datatableName));
		checkPreRequisites();

		ExcelDataAccess testDataAccess = new ExcelDataAccess(datatablePath, datatableName);
		testDataAccess.setDatasheetName(datasheetName);

		int rowNum = testDataAccess.getRowNum(currentTestcase, 0, 1); // Start
																		// at
																		// row
																		// 1,
																		// skipping
																		// the
																		// header
																		// row
		if (rowNum == -1) {

			almFunction.ThrowException("Validate testcase in datasheet", "Testcase should be available in datasheet",
					String.format("Testcase %s is not available in %s", currentTestcase, datasheetName), false);
		}
		rowNum = testDataAccess.getRowNum(Integer.toString(currentIteration), 1, rowNum);
		if (rowNum == -1) {

			almFunction.ThrowException("Validate iteration of testcase in datasheet",
					"Iteration number in testcase should be available in datasheet",
					String.format("Iteration number %s in %s is not available in %s", currentIteration, currentTestcase,
							datasheetName),
					false);
		}
		if (properties.getProperty("Approach").equalsIgnoreCase("KeywordDriven")) {
			rowNum = testDataAccess.getRowNum(Integer.toString(currentSubIteration), 2, rowNum);
			if (rowNum == -1) {

				almFunction.ThrowException("Validate sub iteration of testcase in datasheet",
						"Sub iteration number in testcase should be available in datasheet",
						String.format("Sub iteration number %s in %s is not available in %s", currentSubIteration,
								currentTestcase, datasheetName),
						false);
			}
		}

//		FileLockMechanism objFileLockMechanism = new FileLockMechanism(
//				Long.valueOf(properties.getProperty("FileLockTimeOut")));
		synchronized (objFileLockMechanism) {
			FileLock objFileLock = objFileLockMechanism.SetLockOnFile(strLockFile);
			if (objFileLock != null) {
				// synchronized (this) {
				testDataAccess.setValue(rowNum, fieldName, dataValue);
				// }
				objFileLockMechanism.ReleaseLockOnFile(objFileLock, strLockFile);
			} else {
				throw new FrameworkException("Error", "Error in getting data from excel due to file lock exception");
			}
		}

	}

	/**
	 * Function to get the expected result corresponding to the field name passed
	 * 
	 * @param fieldName The name of the field which contains the expected results
	 * @return The expected result present in the field name specified
	 * @see #getData(String, String)
	 */
	public String getExpectedResult(String fieldName) {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		checkPreRequisites();

		ExcelDataAccess expectedResultsAccess = new ExcelDataAccess(datatablePath, datatableName);
		expectedResultsAccess.setDatasheetName("Parametrized_Checkpoints");

		int rowNum = expectedResultsAccess.getRowNum(currentTestcase, 0, 1); // Start
																				// at
																				// row
																				// 1,
																				// skipping
																				// the
																				// header
																				// row
		if (rowNum == -1) {

			almFunction.ThrowException("Validate testcase in Parametrized_Checkpoints",
					"Testcase should be available in Parametrized_Checkpoints",
					String.format("Testcase %s is not available in %s", currentTestcase, "Parametrized_Checkpoints"),
					false);
		}
		rowNum = expectedResultsAccess.getRowNum(Integer.toString(currentIteration), 1, rowNum);
		if (rowNum == -1) {

			almFunction.ThrowException("Validate iteration of testcase in Parametrized_Checkpoints",
					"Iteration number in testcase should be available in Parametrized_Checkpoints",
					String.format("Iteration number %s in %s is not available in %s", currentIteration, currentTestcase,
							"Parametrized_Checkpoints"),
					false);
		}
		if (properties.getProperty("Approach").equalsIgnoreCase("KeywordDriven")) {
			rowNum = expectedResultsAccess.getRowNum(Integer.toString(currentSubIteration), 2, rowNum);
			if (rowNum == -1) {
				almFunction.ThrowException("Validate sub iteration of testcase in Parametrized_Checkpoints",
						"Sub iteration number in testcase should be available in Parametrized_Checkpoints",
						String.format("Sub iteration number %s in %s is not available in %s", currentSubIteration,
								currentTestcase, "Parametrized_Checkpoints"),
						false);
			}
		}

		return expectedResultsAccess.getValue(rowNum, fieldName);
	}

	/**
	 * Function to return the test data value corresponding to the sheet name and
	 * field name passed
	 * 
	 * @param datasheetName The name of the sheet in which the data is present
	 * @param fieldName     The name of the field whose value is required
	 * @param subIteration  The SubIteration which is required to access the
	 *                      respective data
	 * @return The test data present in the field name specified
	 * @see #putData(String, String, String)
	 * @see #getExpectedResult(String)
	 */
	public String getDataWithSubIteration(String datasheetName, String fieldName, String subIteration) {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		checkPreRequisites();

		ExcelDataAccess testDataAccess = new ExcelDataAccess(datatablePath, datatableName);
		testDataAccess.setDatasheetName(datasheetName);

		int rowNum = testDataAccess.getRowNum(currentTestcase, 0, 1); // Start
																		// at
																		// row
																		// 1,
																		// skipping
																		// the
																		// header
																		// row
		if (rowNum == -1) {

			almFunction.ThrowException("Validate testcase in datasheet", "Testcase should be available in datasheet",
					String.format("Testcase %s is not available in %s", currentTestcase, datasheetName), false);
		}
		rowNum = testDataAccess.getRowNum(Integer.toString(currentIteration), 1, rowNum);
		if (rowNum == -1) {

			almFunction.ThrowException("Validate iteration of testcase in datasheet",
					"Iteration number in testcase should be available in datasheet",
					String.format("Iteration number %s in %s is not available in %s", currentIteration, currentTestcase,
							datasheetName),
					false);
		}

		rowNum = testDataAccess.getRowNum(subIteration, 2, rowNum);
		if (rowNum == -1) {
			almFunction.ThrowException("Validate sub iteration of testcase in datasheet",
					"Sub iteration number in testcase should be available in datasheet",
					String.format("Sub iteration number %s in %s is not available in %s", currentSubIteration,
							currentTestcase, datasheetName),
					false);
		}

		String dataValue = testDataAccess.getValue(rowNum, fieldName);

		if (dataValue.startsWith(dataReferenceIdentifier)) {
			dataValue = getCommonData(fieldName, dataValue);
		}

		return dataValue;
	}

	/**
	 * Function to return the test data value corresponding to the sheet name and
	 * field name passed
	 * 
	 * @param datasheetName The name of the sheet in which the data is present
	 * @param keys          The name of the fields whose values are required
	 * @return The Map of Column Names with values
	 */
	public Map<String, String> getData(String datasheetName, String[] keys) {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		checkPreRequisites();

		ExcelDataAccess testDataAccess = new ExcelDataAccess(datatablePath, datatableName);
		testDataAccess.setDatasheetName(datasheetName);

		int rowNum = testDataAccess.getRowNum(currentTestcase, 0, 1); // Start
																		// at
																		// row
																		// 1,
																		// skipping
																		// the
																		// header
																		// row
		if (rowNum == -1) {

			almFunction.ThrowException("Validate testcase in datasheet", "Testcase should be available in datasheet",
					String.format("Testcase %s is not available in %s", currentTestcase, datasheetName), false);
		}
		rowNum = testDataAccess.getRowNum(Integer.toString(currentIteration), 1, rowNum);
		if (rowNum == -1) {

			almFunction.ThrowException("Validate iteration of testcase in datasheet",
					"Iteration number in testcase should be available in datasheet",
					String.format("Iteration number %s in %s is not available in %s", currentIteration, currentTestcase,
							datasheetName),
					false);
		}

		rowNum = testDataAccess.getRowNum(Integer.toString(currentSubIteration), 2, rowNum);
		if (rowNum == -1) {
			almFunction.ThrowException("Validate sub iteration of testcase in datasheet",
					"Sub iteration number in testcase should be available in datasheet",
					String.format("Sub iteration number %s in %s is not available in %s", currentSubIteration,
							currentTestcase, datasheetName),
					false);
		}

		Map<String, String> values = testDataAccess.getValuesForSpecificRow(keys, rowNum);

		return values;
	}

	/**
	 * Function to return the test data value corresponding to the sheet name and
	 * field name passed
	 * 
	 * @param testcase      Case ID The name of Test Case ID
	 * @param datasheetName The name of the sheet in which the data is present
	 * @param fieldName     The name of the field whose value is required
	 * @param subIteration  The SubIteration which is required to access the
	 *                      respective data
	 * @return The test data present in the field name specified
	 * @see #putData(String, String, String)
	 * @see #getExpectedResult(String)
	 */
	public String getDataWithSubIterationTDID(String testcase, String datasheetName, String fieldName,
			String subIteration) {
		ALMFunctions almFunction = RunContext.getRunContext().getALMFunctions();
		checkPreRequisites();

		ExcelDataAccess testDataAccess = new ExcelDataAccess(datatablePath, datatableName);
		testDataAccess.setDatasheetName(datasheetName);

		int rowNum = testDataAccess.getRowNum(testcase, 0, 1); // Start
																// at
																// row
																// 1,
																// skipping
																// the
																// header
																// row
		if (rowNum == -1) {

			almFunction.ThrowException("Validate testcase in datasheet", "Testcase should be available in datasheet",
					String.format("Testcase %s is not available in %s", currentTestcase, datasheetName), false);
		}
		rowNum = testDataAccess.getRowNum(Integer.toString(currentIteration), 1, rowNum);
		if (rowNum == -1) {
			almFunction.ThrowException("Validate iteration of testcase in datasheet",
					"Iteration number in testcase should be available in datasheet",
					String.format("Iteration number %s in %s is not available in %s", currentIteration, currentTestcase,
							datasheetName),
					false);
		}

		rowNum = testDataAccess.getRowNum(subIteration, 2, rowNum);
		if (rowNum == -1) {
			almFunction.ThrowException("Validate sub iteration of testcase in datasheet",
					"Sub iteration number in testcase should be available in datasheet",
					String.format("Sub iteration number %s in %s is not available in %s", currentSubIteration,
							currentTestcase, datasheetName),
					false);
		}

		String dataValue = testDataAccess.getValue(rowNum, fieldName);

		if (dataValue.startsWith(dataReferenceIdentifier)) {
			dataValue = getCommonData(fieldName, dataValue);
		}

		return dataValue;
	}
}