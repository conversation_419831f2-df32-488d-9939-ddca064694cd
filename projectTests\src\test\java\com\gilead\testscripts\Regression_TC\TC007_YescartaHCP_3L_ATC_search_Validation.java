package com.gilead.testscripts.Regression_TC;

import org.testng.annotations.AfterClass;
import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class TC007_YescartaHCP_3L_ATC_search_Validation extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);

			objCommonFunctions.invokeUrl();
			objCommonFunctions.selectHCP("Yes, I am");
			objCommonFunctions.closeCookies();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void validateATCSearch() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.removeOverlay("div.cmp-html-content._2col-layout-sp84.bottom-floating-tray");
			objCommonFunctions.validateATC_Search("validateATC_Search");
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 3)
	public void validateLinks() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.validateLinks("validateLinks");
		} finally {
			checkErrors();
		}
	}

	@AfterClass
	public void resetHashMap() {
		CommonFunctions.resetHashMap();
	}

}
