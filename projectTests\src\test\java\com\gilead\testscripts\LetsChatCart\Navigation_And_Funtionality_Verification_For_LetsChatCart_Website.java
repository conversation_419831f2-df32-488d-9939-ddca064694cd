package com.gilead.testscripts.LetsChatCart;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class Navigation_And_Funtionality_Verification_For_LetsChatCart_Website extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.invokeUrl();
			objCommonFunctions.allowAllCookies();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 2)
	public void letsChatartNavigationAndFunctionalityVerification() throws Exception {
		try {
			objCommonFunctions.letsChatCartNavigationAndFunctionality();
		} finally {
			checkErrors();
		}
	}
	
}
