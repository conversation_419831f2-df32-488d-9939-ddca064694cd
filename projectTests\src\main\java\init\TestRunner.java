package init;

import java.lang.invoke.MethodHandles;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.gilead.base.TestJob;
import com.gilead.maintenance.ResultSummaryManager;
import com.gilead.maintenance.Settings;
import com.gilead.reports.SeleniumTestParameters;
import com.gilead.utils.ServiceRegister;

/**
 * class to invoke test by java application
 * 
 *
 */
public class TestRunner {
	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());

	public static void main(String[] args)
			throws ClassNotFoundException, InstantiationException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException {

		ResultSummaryManager.getInstance().setRelativePath();

		Properties properties = Settings.getInstance();
		int failExecutionCount = Integer.parseInt(properties.getProperty("RetryFailExecutionCount"));
		int totalThreads = Integer.parseInt(properties.getProperty("TestRunnerThreads"));

		int currentFailCount = 1;

		ExecutorService service = Executors.newFixedThreadPool(1);

		ServiceRegister register = null;
		List<SeleniumTestParameters> failedParams = new ArrayList<SeleniumTestParameters>();

		try {

			Future<ServiceRegister> executionStatus = service.submit(new TestJob(null, totalThreads));
			register = (ServiceRegister) executionStatus.get();

			int failedTestCases = register.getFailedCases().size();
			if (failedTestCases > 0) {

				for (Map.Entry<String, List<SeleniumTestParameters>> param : register.getFailedCases().entrySet()) {
					System.out.println(
							"Found failed test case(s) " + param.getValue().size() + " in suite " + param.getKey());
					failedParams.addAll(param.getValue());
				}
			}

			System.out.println("Execution completed...");

		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (ExecutionException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			service.shutdown();
		}

		int minThread = 1;
		int failedTestCases = register.getFailedCases().size();
		if (failExecutionCount == 0) {
			
			if (register != null && failedTestCases > 0) {
				for (Map.Entry<String, List<SeleniumTestParameters>> param : register.getFailedCases().entrySet()) {
					logger.info(String.format("Found failed test case(s) %s in suite %s ", param.getValue().size(),
							param.getKey()));
				}
			}
		} else {

			for (Map.Entry<String, List<SeleniumTestParameters>> param : register.getFailedCases().entrySet()) {
				if (param.getValue().size() > minThread) {
					minThread = param.getValue().size();
				}
				logger.info(String.format("Found failed test case(s) %s in suite %s ", param.getValue().size(),
						param.getKey()));
			}
		}
		

		while (currentFailCount <= failExecutionCount) {

			if (failedTestCases > 0) {

				for (Map.Entry<String, List<SeleniumTestParameters>> param : register.getFailedCases().entrySet()) {
					if (param.getValue().size() > minThread) {
						minThread = param.getValue().size();
					}
					System.out.println(
							"Started to execute failed test case(s) " + param.getValue().size() + " in suite " + param.getKey());
					//failedParams.addAll(param.getValue());

				}
			}
			
			register.removeAll(Thread.currentThread().getName());

			ExecutorService failedTestService = Executors.newFixedThreadPool(1);
			Future<ServiceRegister> executionStatus = failedTestService.submit(new TestJob(failedParams, minThread));

			try {
				register = (ServiceRegister) executionStatus.get();

				currentFailCount++;

			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (ExecutionException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} finally {
				failedTestService.shutdown();
			}
			minThread = 1;
			failedParams.clear();
		}

	}

}
