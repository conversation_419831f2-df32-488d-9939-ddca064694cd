<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="z1vd7sthprdn255" name="com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website" tests="2" failures="1" timestamp="2025-05-20T17:47:00 IST" time="209.135" errors="0">
  <testcase name="invokeURL" time="77.846" classname="com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website"/>
  <system-out/>
  <testcase name="PDFVerificationForCarthopeWebsite" time="131.289" classname="com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website">
    <failure type="com.gilead.config.FrameworkAssertion" message="&#039;1747743093785.pdf&#039; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles">
      <![CDATA[com.gilead.config.FrameworkAssertion: '1747743093785.pdf' is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
at com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website.PDFVerificationForCarthopeWebsite(PDF_Download_Verification_for_Carthope_Website.java:32)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
]]>
    </failure>
  </testcase> <!-- PDFVerificationForCarthopeWebsite -->
  <system-out/>
</testsuite> <!-- com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website -->
