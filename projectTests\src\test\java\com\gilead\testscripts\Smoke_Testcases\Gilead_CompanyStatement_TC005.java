package com.gilead.testscripts.Smoke_Testcases;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;


public class Gilead_CompanyStatement_TC005 extends BaseTest {

	CommonFunctions objCommonFunctions;


@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
//			objCommonFunctions.zoomOutBrowser();
		} finally {
			checkErrors();
		}
	}

@Test(priority = 2)
	public void verifyCompanyStatement() throws Exception {
		try {
			objCommonFunctions.verifyCompanyStatement();
		} finally {
			checkErrors();
		}
	}
}
