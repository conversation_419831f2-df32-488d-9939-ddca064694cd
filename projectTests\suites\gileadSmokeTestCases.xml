<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="Gilead.com_Suite">

	<test name="Test under EntireSuite_Suite1" thread-count="2"
		parallel="classes">
		<parameter name="RunID" value="0" />
		<classes>
			<class
				name="com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001" />
			<class
				name="com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002" />
			<!--<class
				name="com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003" />	
			<class
				name="com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004" />	
			<class
				name="com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005" />
			<class
				name="com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006" />
			<class
				name="com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007" />	
			<class
				name="com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008" />		-->
		</classes>

	</test>
</suite>