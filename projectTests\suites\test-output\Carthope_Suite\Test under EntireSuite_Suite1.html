<html>
<head>
<title>TestNG:  Test under EntireSuite_Suite1</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Test under EntireSuite_Suite1</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>11/1/0</td>
</tr><tr>
<td>Started on:</td><td>Tue May 20 17:37:54 IST 2025</td>
</tr>
<tr><td>Total time:</td><td>544 seconds (544949 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website.PDFVerificationForCarthopeWebsite()'><b>PDFVerificationForCarthopeWebsite</b><br>Test class: com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website</td>
<td><div><pre>com.gilead.config.FrameworkAssertion: &apos;1747743093785.pdf&apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
	at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
	at com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website.PDFVerificationForCarthopeWebsite(PDF_Download_Verification_for_Carthope_Website.java:32)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace839391712", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace839391712'><pre>com.gilead.config.FrameworkAssertion: &apos;1747743093785.pdf&apos; is not downloaded in the file path: C:\Users\<USER>\git\DX\DigitalExperience\projectTests/externalFiles
	at com.gilead.base.BaseTest.checkErrors(BaseTest.java:567)
	at com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website.PDFVerificationForCarthopeWebsite(PDF_Download_Verification_for_Carthope_Website.java:32)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
</pre></div></td>
<td>125</td>
<td>com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website@3a7442c7</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site</td>
<td></td>
<td>77</td>
<td>com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site@53fb3dab</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site.toVerifyCarTConsultationSite()'><b>toVerifyCarTConsultationSite</b><br>Test class: com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site</td>
<td></td>
<td>111</td>
<td>com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site@53fb3dab</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site</td>
<td></td>
<td>84</td>
<td>com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site@75437611</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site.toVerifySafetySite()'><b>toVerifySafetySite</b><br>Test class: com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site</td>
<td></td>
<td>81</td>
<td>com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site@712625fd</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website</td>
<td></td>
<td>140</td>
<td>com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website@53045c6c</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website.carthopeNaviationVerification()'><b>carthopeNaviationVerification</b><br>Test class: com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website</td>
<td></td>
<td>228</td>
<td>com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website@53045c6c</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website</td>
<td></td>
<td>147</td>
<td>com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website@2034b64c</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website.carthopeNavigationAndFunctionalityVerification()'><b>carthopeNavigationAndFunctionalityVerification</b><br>Test class: com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website</td>
<td></td>
<td>81</td>
<td>com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website@2034b64c</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website</td>
<td></td>
<td>77</td>
<td>com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website@3a7442c7</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site</td>
<td></td>
<td>50</td>
<td>com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site@712625fd</td></tr>
<tr>
<td title='com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site.toVerifyPatientEligibiltySite()'><b>toVerifyPatientEligibiltySite</b><br>Test class: com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site</td>
<td></td>
<td>113</td>
<td>com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site@75437611</td></tr>
</table><p>
</body>
</html>