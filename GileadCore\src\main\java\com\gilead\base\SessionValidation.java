package com.gilead.base;

import java.lang.invoke.MethodHandles;
import java.util.Timer;
import java.util.TimerTask;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import rest.connector.Dao;

public class SessionValidation {
	
	public static TimerTask timerTask;
	public static Timer timer;
	
	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());
	
	public static void validateTimeout(long minutes) {
		
		timerTask = new TimerTask() {

			@Override
			public void run() {
				try {
					Dao.continueSession();
//					int statusCode = Dao.continueSession();
//					if(!(RestConnector.instance().isStatusCodeOK(statusCode))) {
//						System.out.println("++++++++++++++++++++++++");
//						RestConnector.instance().init(Util.getAlmProperty("host"), Util.getAlmProperty("port"),
//								Util.getAlmProperty("domain"), Util.getAlmProperty("project"));
//						logger.info(String.format("Connected to Host %s, Domain %s, Project %s successfully", 
//								Util.getAlmProperty("host"),
//								Util.getAlmProperty("domain"),
//								Util.getAlmProperty("project")));
//						
//						if(Util.getAlmProperty("authtype").equalsIgnoreCase(AuthType.BASIC.toString())) {
//							Dao.login(Util.getAlmProperty("username"),
//									Util.getAlmProperty("password"), AuthType.BASIC);
//						} else if(Util.getAlmProperty("authtype").equalsIgnoreCase(AuthType.OAUTH2.toString())) {
//							Dao.login(Util.getAlmProperty("clientid"),
//									Util.getAlmProperty("secret"), AuthType.OAUTH2);
//						}
//						
//					}
					logger.info(String.format("Session continued successfully"));
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
		};
		
		timer = new Timer("Session Validation", true);
		long pollingFrequency = 1000 * 60 * minutes;
		timer.schedule(timerTask, 1000 * 60 * 10, pollingFrequency);
		
	}
	
	

}
