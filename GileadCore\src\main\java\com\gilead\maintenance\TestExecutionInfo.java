package com.gilead.maintenance;



import org.openqa.selenium.Platform;

import com.gilead.reports.Browser;
import com.gilead.reports.ExecutionMode;

public class TestExecutionInfo extends TestConfiguration {
	
	private String TestConfigurationId;
	private ExecutionMode executionMode;
	private String toolName;
	private String mobileExecutionPlatform;
	private String mobileOsVersion;
	private String deviceName;
	private Browser browser;
	private String browserVersion;
	private Platform platform;
	private String platformVersion;
	private String seeTestPort;
	public String getTestConfigurationId() {
		return TestConfigurationId;
	}
	public void setTestConfigurationId(String testConfigurationId) {
		TestConfigurationId = testConfigurationId;
	}
	public ExecutionMode getExecutionMode() {
		return executionMode;
	}
	public void setExecutionMode(ExecutionMode executionMode) {
		this.executionMode = executionMode;
	}
	public String getToolName() {
		return toolName;
	}
	public void setToolName(String toolName) {
		this.toolName = toolName;
	}
	public String getMobileExecutionPlatform() {
		return mobileExecutionPlatform;
	}
	public void setMobileExecutionPlatform(String mobileExecutionPlatform) {
		this.mobileExecutionPlatform = mobileExecutionPlatform;
	}
	public String getMobileOsVersion() {
		return mobileOsVersion;
	}
	public void setMobileOsVersion(String mobileOsVersion) {
		this.mobileOsVersion = mobileOsVersion;
	}
	public String getDeviceName() {
		return deviceName;
	}
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	public Browser getBrowser() {
		return browser;
	}
	public void setBrowser(Browser browser) {
		this.browser = browser;
	}
	public String getBrowserVersion() {
		return browserVersion;
	}
	public void setBrowserVersion(String browserVersion) {
		this.browserVersion = browserVersion;
	}
	public Platform getPlatform() {
		return platform;
	}
	public void setPlatform(Platform platform) {
		this.platform = platform;
	}
	public String getPlatformVersion() {
		return platformVersion;
	}
	public void setPlatformVersion(String platformVersion) {
		this.platformVersion = platformVersion;
	}
	public String getSeeTestPort() {
		return seeTestPort;
	}
	public void setSeeTestPort(String seeTestPort) {
		this.seeTestPort = seeTestPort;
	}
	
	
	
}
