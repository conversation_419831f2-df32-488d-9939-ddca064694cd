id: NoHardcodeWait_objectExists
message: "Don't use hardcoded wait time for objectExists"
language: java
severity: warning
rule:
  pattern: objectExists($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 3
      kind: decimal_integer_literal
---
id: NoHardcodeWait_dragAndDrop
message: "Don't use hardcoded wait time for dragAndDrop"
language: java
severity: warning
rule:
  pattern: dragAndDrop($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 3
      kind: decimal_integer_literal
---
id: NoHardcodeWait_isAlertPresent
message: "Don't use hardcoded wait time for isAlertPresent"
language: java
severity: warning
rule:
  pattern: isAlertPresent($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal
---
id: NoHardcodeWait_acceptAlert
message: "Don't use hardcoded wait time for acceptAlert"
language: java
severity: warning
rule:
  pattern: acceptAlert($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal
---
id: NoHardcodeWait_denyAlert
message: "Don't use hardcoded wait time for denyAlert"
language: java
severity: warning
rule:
  pattern: denyAlert($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal
---
id: NoHardcodeWait_getAlertText
message: "Don't use hardcoded wait time for getAlertText"
language: java
severity: warning
rule:
  pattern: getAlertText($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal
---
id: NoHardcodeWait_switchToNewWindow
message: "Don't use hardcoded wait time for switchToNewWindow"
language: java
severity: warning
rule:
  pattern: switchToNewWindow($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 1
      kind: decimal_integer_literal
---
id: NoHardcodeWait_sendkeys
message: "Don't use hardcoded wait time for sendkeys"
language: java
severity: warning
rule:
  pattern: sendkeys($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_getValue
message: "Don't use hardcoded wait time for getValue"
language: java
severity: warning
rule:
  pattern: getValue($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_getText
message: "Don't use hardcoded wait time for getText"
language: java
severity: warning
rule:
  pattern: getText($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_getAttributeValue
message: "Don't use hardcoded wait time for getAttributeValue"
language: java
severity: warning
rule:
  pattern: getAttributeValue($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_click
message: "Don't use hardcoded wait time for click"
language: java
severity: warning
rule:
  pattern: click($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_clickByJsExec
message: "Don't use hardcoded wait time for clickByJsExec"
language: java
severity: warning
rule:
  pattern: clickByJsExec($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_clickByJS
message: "Don't use hardcoded wait time for clickByJS"
language: java
severity: warning
rule:
  pattern: clickByJS($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_doubleClick
message: "Don't use hardcoded wait time for doubleClick"
language: java
severity: warning
rule:
  pattern: doubleClick($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_rightClick
message: "Don't use hardcoded wait time for rightClick"
language: java
severity: warning
rule:
  pattern: rightClick($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_clear
message: "Don't use hardcoded wait time for clear"
language: java
severity: warning
rule:
  pattern: clear($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_selectListItem
message: "Don't use hardcoded wait time for selectListItem"
language: java
severity: warning
rule:
  pattern: selectListItem($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_mouseOver
message: "Don't use hardcoded wait time for mouseOver"
language: java
severity: warning
rule:
  pattern: mouseOver($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_mouseOverandSendKeys
message: "Don't use hardcoded wait time for mouseOverandSendKeys"
language: java
severity: warning
rule:
  pattern: mouseOverandSendKeys($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_mouseOverandClick
message: "Don't use hardcoded wait time for mouseOverandClick"
language: java
severity: warning
rule:
  pattern: mouseOverandClick($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_dragANDDropUsingAxis
message: "Don't use hardcoded wait time for dragANDDropUsingAxis"
language: java
severity: warning
rule:
  pattern: dragANDDropUsingAxis($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_mouseOverandDoubleClick
message: "Don't use hardcoded wait time for mouseOverandDoubleClick"
language: java
severity: warning
rule:
  pattern: mouseOverandDoubleClick($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_sendKeysByJsExec
message: "Don't use hardcoded wait time for sendKeysByJsExec"
language: java
severity: warning
rule:
  pattern: sendKeysByJsExec($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal
---
id: NoHardcodeWait_mouseOverandRightClick
message: "Don't use hardcoded wait time for mouseOverandRightClick"
language: java
severity: warning
rule:
  pattern: mouseOverandRightClick($$$A)
  has:
    kind: argument_list
    has:
      nthChild: 2
      kind: decimal_integer_literal