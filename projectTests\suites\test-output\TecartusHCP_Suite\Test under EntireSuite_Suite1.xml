<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="18" name="Test under EntireSuite_Suite1" tests="14" failures="0" timestamp="2025-04-23T18:13:29 IST" time="328.374" errors="0">
  <testcase name="invokeURL" time="7.52" classname="com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website"/>
  <testcase name="accessNavigationMenuOnYescartaHCP" time="17.271" classname="com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website"/>
  <testcase name="invokeURL" time="5.818" classname="com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site"/>
  <testcase name="verifyAdditionalEfficacyDataSite" time="25.246" classname="com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site"/>
  <testcase name="invokeURL" time="6.03" classname="com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website"/>
  <testcase name="tecartuHCPSearchFuntionalityVerification" time="23.086" classname="com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website"/>
  <testcase name="invokeURL" time="5.517" classname="com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website"/>
  <testcase name="tecartusHCPNavigationAndAPI" time="40.167" classname="com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website"/>
  <testcase name="invokeURL" time="6.711" classname="com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site"/>
  <testcase name="verifySafetyProfileSite" time="32.33" classname="com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site"/>
  <testcase name="invokeURL" time="12.258" classname="com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site"/>
  <testcase name="verifyLukemiaTreatmentSite" time="35.914" classname="com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site"/>
  <testcase name="invokeURL" time="12.907" classname="com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site"/>
  <testcase name="verifyLukemiaResponseSite" time="26.398" classname="com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site"/>
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
