<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>ATC_Search_Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2430</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2438</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>-163</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-748</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@464783135</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/02 12:56:32</td>   <td>-2706</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Navigation_and_Functionality_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">invokeURL</td> 
  <td>Thread2@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2430</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-3105</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:33</td>   <td>-1669</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2681</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@464783135</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/02 12:56:35</td>   <td>4</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PDF_Download_Verification_for_YesCARTA_Website.PDFverification()[pri:2, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">PDFverification</td> 
  <td>Thread9@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:35</td>   <td>781</td> <td title="&lt;&lt;CRAFTLiteTestCase.tearDownTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;tearDownTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-583</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-371</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1905</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:35</td>   <td>-137</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread8@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:34</td>   <td>-375</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2691</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@464783135</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/02 12:56:34</td>   <td>-395</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_clinical_trial_results_site.verifyClinicalTrialResults()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">verifyClinicalTrialResults</td> 
  <td>Thread7@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>24</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread9@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-781</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/02 12:56:34</td>   <td>-776</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_managing_side_effects_site.verifyManagingSideEffectsSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">verifyManagingSideEffectsSite</td> 
  <td>Thread6@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2427</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1906</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-595</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>9</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread9@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2438</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:28</td>   <td>-6423</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>-150</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread8@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>-137</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread8@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2426</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1673</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@464783135</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/02 12:56:32</td>   <td>-3166</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">invokeURL</td> 
  <td>Thread1@464783135</td>   <td></td> </tr>
<tr bgcolor="b0fd6f">  <td>25/04/02 12:56:33</td>   <td>-1908</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_support_and_resources_site.verifySupportAndResourcesSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">verifySupportAndResourcesSite</td> 
  <td>Thread4@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:32</td>   <td>-2426</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-3121</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2698</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1908</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1908</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/02 12:56:33</td>   <td>-1678</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_receiving_yescarta_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">invokeURL</td> 
  <td>Thread5@464783135</td>   <td></td> </tr>
<tr bgcolor="79a2a1">  <td>25/04/02 12:56:32</td>   <td>-2443</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_at_a_glance_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">invokeURL</td> 
  <td>Thread3@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1672</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-383</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread7@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-3109</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1675</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2698</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1673</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:34</td>   <td>-583</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1675</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/04/02 12:56:35</td>   <td>-15</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PDF_Download_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">invokeURL</td> 
  <td>Thread9@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>129</td> <td title="&lt;&lt;BaseTest.afterSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;afterSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1669</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:32</td>   <td>-2681</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@464783135</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/04/02 12:56:32</td>   <td>-2697</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Navigation_and_Functionality_Verification_for_YesCARTA_Website.verifyNavigationHeaders()[pri:2, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">verifyNavigationHeaders</td> 
  <td>Thread2@464783135</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/04/02 12:56:34</td>   <td>-413</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_clinical_trial_results_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">invokeURL</td> 
  <td>Thread7@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:33</td>   <td>-1904</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-3131</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-781</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>-163</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="b0fd6f">  <td>25/04/02 12:56:33</td>   <td>-1912</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_support_and_resources_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">invokeURL</td> 
  <td>Thread4@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2691</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1904</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-390</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@464783135</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/04/02 12:56:32</td>   <td>-3127</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website.navigationVerification()[pri:2, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">navigationVerification</td> 
  <td>Thread1@464783135</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/02 12:56:35</td>   <td>-160</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.PIandNavigationVerification()[pri:2, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">PIandNavigationVerification</td> 
  <td>Thread8@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:32</td>   <td>-3105</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:28</td>   <td>-6679</td> <td title="&gt;&gt;CRAFTLiteTestCase.setUpTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;setUpTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@1062186835</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-399</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>-141</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread8@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-3131</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>-150</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread8@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-390</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread7@464783135</td>   <td></td> </tr>
<tr bgcolor="79a2a1">  <td>25/04/02 12:56:32</td>   <td>-2437</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_yescarta_at_a_glance_site.verifyYescartaSite()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site@8519cb4]">verifyYescartaSite</td> 
  <td>Thread3@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:33</td>   <td>-1906</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site@7f485fda]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-399</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site@1c5920df]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-2690</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website@548d708a]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td></td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/04/02 12:56:34</td>   <td>-843</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_managing_side_effects_site.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">invokeURL</td> 
  <td>Thread6@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:34</td>   <td>-748</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site@196a42c3]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@464783135</td>   <td></td> </tr>
<tr bgcolor="9cfc96">  <td>25/04/02 12:56:28</td>   <td>-6424</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@464783135</td>   <td></td> </tr>
<tr bgcolor="8dbaee">  <td>25/04/02 12:56:33</td>   <td>-1675</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_receiving_yescarta_site.verifyReceivingYescarta()[pri:2, instance:com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site@4a83a74a]">verifyReceivingYescarta</td> 
  <td>Thread5@464783135</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/04/02 12:56:35</td>   <td>-176</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="PI_Integration_and_Navigation_Verification_for_YesCARTA_Website.invokeURL()[pri:1, instance:com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website@742ff096]">invokeURL</td> 
  <td>Thread8@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:32</td>   <td>-3121</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website@1cf6d1be]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@464783135</td>   <td></td> </tr>
<tr bgcolor="cd83d9">  <td>25/04/02 12:56:35</td>   <td>9</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website@cb0755b]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread9@464783135</td>   <td></td> </tr>
</table>
