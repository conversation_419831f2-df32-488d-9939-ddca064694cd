<table border='1'>
<tr>
<th>Class name</th>
<th>Method name</th>
<th>Groups</th>
</tr><tr>
<td>com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>toVerifyCarTConsultationSite</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>carthopeNaviationVerification</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>PDFVerificationForCarthopeWebsite</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>toVerifyPatientEligibiltySite</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>toVerifySafetySite</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>carthopeNavigationAndFunctionalityVerification</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
</table>
