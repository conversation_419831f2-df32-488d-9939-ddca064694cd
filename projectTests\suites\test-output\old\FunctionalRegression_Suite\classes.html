<table border='1'>
<tr>
<th>Class name</th>
<th>Method name</th>
<th>Groups</th>
</tr><tr>
<td>com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>verifyCriticalComponents</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>verifyCriticalComponents</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>verifyCriticalComponents</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>verifyCriticalComponents</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>verifyCriticalComponents</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
<tr>
<td>com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>verifyCriticalComponents</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>invokeURL</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>setUpTestRunner</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>beforeClass</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>beforeMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterMethod</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>afterClass</td>
<td>&nbsp;</td></tr>
</table>
