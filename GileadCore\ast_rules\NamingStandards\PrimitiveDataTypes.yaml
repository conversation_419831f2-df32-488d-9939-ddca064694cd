id: naming_convention_boolean
message: "Ensure all boolean variable naming should starts with 'bool'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: boolean_type
        regex: '^boolean$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^bln'
---
id: naming_convention_int
message: "Ensure all integer variable naming should starts with 'int'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: integral_type
        regex: '^int$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^int'
---
id: naming_convention_byte
message: "Ensure all byte variable naming should starts with 'byte'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: integral_type
        regex: '^byte$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^byte'
---
id: naming_convention_char
message: "Ensure all char variable naming should starts with 'chr'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: integral_type
        regex: '^char$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^chr'
---
id: naming_convention_float
message: "Ensure all float variable naming should starts with 'flt'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: floating_point_type
        regex: '^float$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^flt'
---
id: naming_convention_double
message: "Ensure all double variable naming should starts with 'dbl'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: floating_point_type
        regex: '^double$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^dbl'
---
id: naming_convention_long
message: "Ensure all long variable naming should starts with 'lng'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: integral_type
        regex: '^long$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^lng'
---
id: naming_convention_short
message: "Ensure all short variable naming should starts with 'sht'"
language: java
severity: warning
rule:
  kind: local_variable_declaration
  all:
    - has:
        kind: integral_type
        regex: '^short$'
    - not: 
        has:
          stopBy: end
          kind: identifier
          regex: '^sht'
