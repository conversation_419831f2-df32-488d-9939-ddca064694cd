package com.gilead.testscripts.Regression_TC;

import org.testng.annotations.AfterClass;
import org.testng.annotations.Test;

import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class TC002_TecartusHCP_ALL_Popup extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.invokeUrl();
			objCommonFunctions.closeCookies();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyPopup() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.clickTab("Kite Konnect", driver.getTitle());
			objCommonFunctions.verifyDeclineButton("Cancel");
			objCommonFunctions.clickTab("REMS", driver.getTitle());
			objCommonFunctions.verifyDeclineButton("Cancel");
			objCommonFunctions.clickTab("Kite Konnect", driver.getTitle());
			objCommonFunctions.verifyAcceptButton("OK");
			objCommonFunctions.clickTab("REMS", driver.getTitle());
			objCommonFunctions.verifyAcceptButton("OK");
		} finally {
			checkErrors();
		}
	}

	
	@AfterClass
	public void resetHashMap() {
		CommonFunctions.resetHashMap();
	}

}
