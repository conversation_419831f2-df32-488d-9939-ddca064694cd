package com.gilead.testscripts.ParlonscartCA;

import org.testng.annotations.Test;
import com.gilead.base.BaseTest;
import businesscomponents.CommonFunctions;
import rest.annotations.ALMTarget;

@ALMTarget(testplan = "${Sprint1_testplan}", testname = "TC01_HCP_Patient_Enroll_CM_POPending", testlab = "${testlab}", testset = "${testset}")

public class To_Verify_ParlonscartCA_Finding_Support_Site extends BaseTest {

	CommonFunctions objCommonFunctions;

	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}
	
	@Test(priority = 2)
	public void toVerifyParlonscartFindingSupportSite() throws Exception {
		try {
			objCommonFunctions.verifyMenuLinks();
		} finally {
			checkErrors();
		}
	}
	
}
