id: pass-report-statements-usage
message: "Reporting statements for PASS should be followed by conditional check"
severity: warning
language: java
rule:
    any:
        - pattern: report.updateTestLog($ARG1, $ARG2, $ARG3, Status.PASS)
    not:
        inside:
          kind: if_statement
          stopBy: end
---
id: fail-report-statements-usage
message: "Reporting statements for FAIL should be followed by conditional check"
severity: warning
language: java
rule:
    any:
        - pattern: report.updateTestLog($ARG1, $ARG2, $ARG3, Status.FAIL)
    not:
        inside:
          kind: if_statement
          stopBy: end
---
id: report-statements-usage
message: "Reporting statements should have Description, Expected Result and Actual Result"
severity: warning
language: java
rule:
    any:
        - pattern: report.updateTestLog($ARG1, $ARG2, $ARG3)