<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Default suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="646db9">  <td>25/05/20 19:18:54</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@609656250</td>   <td></td> </tr>
<tr bgcolor="646db9">  <td>25/05/20 19:18:53</td>   <td>-1058</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@609656250</td>   <td></td> </tr>
<tr bgcolor="646db9">  <td>25/05/20 19:18:53</td>   <td>-1058</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@609656250</td>   <td></td> </tr>
<tr bgcolor="646db9">  <td>25/05/20 19:18:54</td>   <td>46</td> <td title="&lt;&lt;BaseTest.afterSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&lt;&lt;afterSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@609656250</td>   <td></td> </tr>
<tr bgcolor="646db9">  <td>25/05/20 19:15:49</td>   <td>-185465</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@609656250</td>   <td></td> </tr>
<tr bgcolor="646db9">  <td>25/05/20 19:16:20</td>   <td>-154712</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@609656250</td>   <td></td> </tr>
<tr bgcolor="646db9">  <td>25/05/20 19:16:20</td>   <td>-154712</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@609656250</td>   <td></td> </tr>
<tr bgcolor="60b491">  <td>25/05/20 19:16:03</td>   <td>-171603</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_ParlonscartCA_Cart_Therapy_Process_Site.invokeURL()[pri:1, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">invokeURL</td> 
  <td>Thread1@609656250</td>   <td></td> </tr>
<tr bgcolor="97aabe">  <td>25/05/20 19:15:49</td>   <td>-185466</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@609656250</td>   <td></td> </tr>
<tr bgcolor="97aabe">  <td>25/05/20 19:15:49</td>   <td>-185709</td> <td title="&gt;&gt;CRAFTLiteTestCase.setUpTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&gt;&gt;setUpTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@609656250</td>   <td></td> </tr>
<tr bgcolor="97aabe">  <td>25/05/20 19:18:55</td>   <td>722</td> <td title="&lt;&lt;CRAFTLiteTestCase.tearDownTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">&lt;&lt;tearDownTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@609656250</td>   <td></td> </tr>
<tr bgcolor="60b491">  <td>25/05/20 19:16:20</td>   <td>-154711</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="To_Verify_ParlonscartCA_Cart_Therapy_Process_Site.toVerifyParlonsCartTherapyProcessSite()[pri:2, instance:com.gilead.testscripts.ParlonscartCA.To_Verify_ParlonscartCA_Cart_Therapy_Process_Site@7ef82753]">toVerifyParlonsCartTherapyProcessSite</td> 
  <td>Thread1@609656250</td>   <td></td> </tr>
</table>
