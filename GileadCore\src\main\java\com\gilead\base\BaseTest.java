package com.gilead.base;

import static rest.utils.Util.getAlmProperty;

import java.io.File;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.ITestContext;
import org.testng.ITestNGMethod;
import org.testng.ITestResult;
import org.testng.annotations.AfterClass;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.AfterSuite;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;

import com.gilead.config.FrameworkAssertion;
import com.gilead.config.FrameworkException;
import com.gilead.maintenance.CRAFTLiteTestCase;
import com.gilead.maintenance.ResultSummaryManager;
import com.gilead.maintenance.WhitelistingPath;
import com.gilead.reports.AzureIntegration;
import com.gilead.reports.FrameworkParameters;
import com.gilead.reports.SeleniumTestParameters;
import com.gilead.utils.Util;

import rest.annotations.ALMStep;
import rest.annotations.ALMTarget;
import rest.connector.ALMContext;
import rest.constants.RestConstants;
import rest.models.Run;
import rest.models.ServerTime;
import rest.utils.EntityParser;
import rest.utils.EntityUtil;
import rest.xml.model.Entity;

@Listeners({ ALMExecutionListener.class })
public class BaseTest extends CRAFTLiteTestCase {

	/**
	 * current test case for any test execution
	 */
	protected String currentTestCase;

	/**
	 * current test suite or scenario for current test execution
	 */
	protected String currentTestScenario;

	/**
	 * Driverscript object for current test execution
	 */
	protected DriverScript driverScript;

	/**
	 * SeleniumTestParameters object for current test execution
	 */
	protected SeleniumTestParameters testParameters;

	/**
	 * iteration and sub iteration details for current test execution
	 */
	protected int currentIteration, currentSubIteration = 1;

	/**
	 * formatter of date pattern to be used in log
	 */
	private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("YYYY_MM_dd_HH_mm_ss");

	private String currentWorker;

	private long startMillis, endMillis;
	
	private int intALMRUNID;

	/**
	 * logger for current test execution
	 */
	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());

	@BeforeClass
	public void beforeClass(ITestContext context) throws FrameworkException {

		/**
		 * Basic setup Initial boolean value for StopExecutionFlag and it should be
		 * initialized within @beforeClass hook
		 */
		synchronized (this) {

			// ThreadLocal to store runtime shared objects
			RunContext runContext = RunContext.getRunContext();
			runContext.setFrameworkParameters(frameworkParameters);
			startMillis = System.currentTimeMillis();

			// Get thread name
			currentWorker = register.getWorkerName();
			Thread.currentThread().setName(currentWorker);

			// Map to store test case objects of each thread
			register.putService(Thread.currentThread().getName() + "StopExecutionFlag", "False");
			currentTestScenario = context.getCurrentXmlTest().getSuite().getName();
			currentTestScenario = context.getCurrentXmlTest().getSuite().getName();
			register.putService(Thread.currentThread().getName() + "CurrentSuite", currentTestScenario);
			// validating currentTestScenario for Failed suites while re-executing
			// and remove '[Failed & Retried]' portion in currentTestScenario
			if (currentTestScenario.contains("[Failed")) {
				FrameworkParameters.getInstance().setRunConfiguration(currentTestScenario);
				currentTestScenario = currentTestScenario.substring(0, currentTestScenario.lastIndexOf("[")).trim();
			}

			String tcName = this.getClass().getName();
			String pkgName = tcName.substring(0, tcName.lastIndexOf("."));
			currentTestCase = tcName.substring(tcName.lastIndexOf(".") + 1);
			register.putService(Thread.currentThread().getName() + "CurrentTestCase", currentTestCase);
			logger.info(String.format("assigned to execute %s", currentTestCase));
			testConfigurationInfo = new TestConfigurationInfo();

			if(context.getCurrentXmlTest().getParameter("RunID")!=null)
			{
				intALMRUNID = Integer.parseInt(context.getCurrentXmlTest().getParameter("RunID"));
			}
			else {
				intALMRUNID = 0;
			}
			
			// logger to display title of test case
			logger.info(Util.drawLineOfChars("=", 120));
			logger.info(StringUtils
					.center("Executing " + currentTestCase + " on " + formatter.format(LocalDateTime.now()), 120));
			logger.info(Util.drawLineOfChars("=", 120));

			// getting configuration details from in 'Regression' sheet of RunManager excel
			// file
			testParameters = testConfigurationInfo.getRunInfo(currentTestScenario, currentTestCase, pkgName,
					"Regression");
			if (testParameters != null) {
				register.putService(Thread.currentThread().getName() + "TempSuite", testParameters.getTempSuite());
				runContext.setSeleniumTestParameters(testParameters);

				List<ITestNGMethod> currentMethods = Arrays.asList(context.getAllTestMethods()).stream()
						.filter((m) -> m.getRealClass() == this.getClass()).collect(Collectors.toList());

				Optional<ITestNGMethod> topPriorityMethod = currentMethods.stream()
						.max(Comparator.comparingInt((m) -> m.getPriority()));

				// context.setAttribute("LastMethod", topPriorityMethod.get());
				register.putService(currentTestCase + "LastMethod", topPriorityMethod.get());
				context.setAttribute("RunContext", runContext);
				setupTestConfiguration(context);

				driverScript.setCurrentIteration(testParameters.getStartIteration());
				logger.info(String.format("Starting current iteration is %s and current sub iteration is %s",
						testParameters.getStartIteration(), currentSubIteration));
				/** VALIDATE ALM INTEGRATION **/
				if (Boolean.valueOf(properties.getProperty("AlmIntegration"))) {
					ALMTarget almTarget = this.getClass().getAnnotation(ALMTarget.class);

					if (almTarget != null) {
						createAlmConfiguration(almTarget);
					}
				}

			} else {
				/**
				 * Having configuration issue so throwing exception
				 */
				@SuppressWarnings("unchecked")
				List<Map<String, String>> configurationErrors = (List<Map<String, String>>) context
						.getAttribute("ConfigurationErrors");
				configurationErrors.add(testConfigurationInfo.getConfigErrors());
				context.setAttribute("ConfigurationErrors", configurationErrors);
				throw new FrameworkException("Configuration error");
			}
		}
	}

	@BeforeMethod
	public void beforeMethod(ITestContext context, Method method) {

		/** VALIDATE ALM INTEGRATION **/
		if (Boolean.valueOf(properties.getProperty("AlmIntegration"))) {
			ALMStep stepAnnotation = method.getAnnotation(ALMStep.class);
			if (stepAnnotation != null) {
				if (stepAnnotation.skipped()) {
					ALMContext.getALMContext().addAlmData(Thread.currentThread().getName() + "Step", null);
				} else {
					List<Entity> allSteps = ALMContext.getALMContext().getSteps(Thread.currentThread().getName());
					Entity currentStep = EntityParser.getEntity(allSteps, "name", stepAnnotation.name());
					ALMContext.getALMContext().addAlmData(Thread.currentThread().getName() + "Step", currentStep);
				}
			} else {
				org.testng.annotations.Test test = method.getAnnotation(org.testng.annotations.Test.class);
				int priority = test.priority();
				if (priority < 0) {
					ALMContext.getALMContext().addAlmData(Thread.currentThread().getName() + "Step", null);
				} else {
					List<Entity> allSteps = ALMContext.getALMContext().getSteps(Thread.currentThread().getName());
					Entity currentStep = EntityParser.getEntity(allSteps, "step-order", String.valueOf(priority));
					ALMContext.getALMContext().addAlmData(Thread.currentThread().getName() + "Step", currentStep);
				}
			}
		}

	}

	/**
	 * Method to handle all variables at test method level
	 * 
	 * @param result
	 * @param method
	 * @throws FrameworkException
	 */
	@AfterMethod(alwaysRun = true)
	public void afterMethod(ITestResult result, Method method) throws FrameworkException {
		synchronized (this) {
			ITestContext tContext = result.getTestContext();
			RunContext runContext = (RunContext) tContext.getAttribute("RunContext");

			String mName = result.getMethod().getMethodName();
			String ctc = register.getService(Thread.currentThread().getName(), "CurrentTestCase").toString();
			ITestNGMethod lastMethod = (ITestNGMethod) register.getService(ctc, "LastMethod");

			if (Boolean.valueOf(properties.getProperty("AlmIntegration"))) {
				try {
					Entity currentStep = (Entity) ALMContext.getALMContext()
							.getAlmData(Thread.currentThread().getName() + "Step");

					if (currentStep != null) {

						currentStep.fieldValue("status", getALMStatus(result.getStatus()));
						ServerTime serverTime = EntityUtil.getServerTime();
						String[] executionDateTime = serverTime.getServerDateTime().split(" ");
						currentStep.fieldValue("execution-date", executionDateTime[0]);
						currentStep.fieldValue("execution-time", executionDateTime[1]);

						EntityUtil.updateRunStep(currentStep);

					}
				} catch (Exception e) {
					// TODO Auto-generated catch block
					logger.error(String.format("ALM assertion error %s", e.getMessage()));
				}
			}

			if (mName.equalsIgnoreCase(lastMethod.getMethodName())) {
				DriverScript driverScript = runContext.getDriverScript();

				// closing browser for current test execution
				driverScript.wrapUp();

				/** VALIDATE ALM INTEGRATION **/
				if (Boolean.valueOf(properties.getProperty("AlmIntegration"))) {
					String currentSuite = register.getService(Thread.currentThread().getName(), "CurrentSuite")
							.toString();
					String reportPath = register.getService(currentSuite, "ReportPath").toString();

					File htmlFile = new File(
							reportPath + Util.getFileSeparator() + "HTML Results" + Util.getFileSeparator()
									+ RunContext.getRunContext().getReportSettings().getReportName() + ".html");
					
					Entity run = (Entity) ALMContext.getALMContext()
							.getAlmData(Thread.currentThread().getName() + "Run");
					//byte[] fileData;
					try {
						EntityUtil.createMultiPartAttachment(RestConstants.RUN, run.id(), htmlFile.getName(), htmlFile);
						logger.info(String.format("Attached html %s to alm Run", htmlFile));
					} catch (Exception e) {
						// TODO Auto-generated catch block
						logger.error(String.format("Html report attachment failure %s", e.getMessage()));
						logger.error(String.format("Attachment failure of html %s to alm Run", htmlFile));
					}

					@SuppressWarnings("unused")
					String strSuitePath = "";
					if (currentTestScenario.contains("Suite")) {
						strSuitePath = currentTestScenario.substring(0, currentTestScenario.lastIndexOf("_"));
					}
					
					File dataSheet = null;
					
					String suiteExecution = properties.getProperty("SequenceSuites");
					if (!suiteExecution.contains(currentSuite)) {
						dataSheet = new File(reportPath + Util.getFileSeparator() + "Data Sheets"
								+ Util.getFileSeparator() + Thread.currentThread().getName()
								+ RunContext.getRunContext().getReportSettings().getReportName() + ".xlsx");
					} else {
						String datatablePath = RunContext.getRunContext().getFrameworkParameters().getRelativePath() + Util.getFileSeparator() + "src"
								+ Util.getFileSeparator() + "test" + Util.getFileSeparator() + "resources" + Util.getFileSeparator()
								+ "Datatables";
						String tempScenario = register.getService(Thread.currentThread().getName(), "TempSuite").toString();
						tempScenario = tempScenario.substring(0, tempScenario.lastIndexOf("_"));
					
						String encryptedDatatablePath = WhitelistingPath.cleanStringForFilePath(
								datatablePath + Util.getFileSeparator() + tempScenario + ".xlsx");
						
						dataSheet = new File(encryptedDatatablePath);
					}
					
					try {

						EntityUtil.createMultiPartAttachment(RestConstants.RUN, run.id(), dataSheet.getName(),
								dataSheet);
						logger.info(String.format("Attached data sheet file %s to alm run instance",
								dataSheet.getAbsolutePath()));
					} catch (Exception e) {
						// TODO Auto-generated catch block
						logger.error(String.format("Data sheet attachment error %s", e.getMessage()));
						logger.error(String.format("Attachment data sheet failure %s to alm run",
								dataSheet.getAbsolutePath()));
					}
					
					File commonDataSheet = null;
					
					if (!suiteExecution.contains(currentSuite)) {
						commonDataSheet = new File(reportPath + Util.getFileSeparator() + "Data Sheets"
								+ Util.getFileSeparator() + "Common Testdata.xlsx");
					} else {
						
						String commonDataPath = RunContext.getRunContext().getFrameworkParameters().getRelativePath() + Util.getFileSeparator() + "src"
								+ Util.getFileSeparator() + "test" + Util.getFileSeparator() + "resources" + Util.getFileSeparator()
								+ "Datatables";
					
						String encryptedDatatablePath = WhitelistingPath.cleanStringForFilePath(
								commonDataPath + Util.getFileSeparator() + "Common Testdata.xlsx");
						
						commonDataSheet = new File(encryptedDatatablePath);
					}
					
					try {
						EntityUtil.createMultiPartAttachment(RestConstants.RUN, run.id(), commonDataSheet.getName(),
								commonDataSheet);
						logger.info(String.format("Attached common sheet file %s to alm run instance",
								dataSheet.getAbsolutePath()));
					} catch (Exception e) {
						// TODO Auto-generated catch block
						logger.error(String.format("Common sheet attachment error %s", e.getMessage()));
						logger.error(String.format("Attachment common sheet failure %s to alm run",
								dataSheet.getAbsolutePath()));
					}
				}

			}

		}

	}

	/**
	 * Method to clear all test class level scope variables and objects
	 * 
	 * @param context
	 */
	@AfterClass(alwaysRun = true)
	public void afterClass(ITestContext context) {
		synchronized (this) {

			RunContext runContext = (RunContext) context.getAttribute("RunContext");

			SeleniumTestParameters testParameters = runContext.getSeleniumTestParameters();
			logger.info(String.format("Executing After class configuration for %s ",
					register.getService(Thread.currentThread().getName(), "CurrentTestCase").toString()));

			tearDownTestRunner(testParameters, runContext.getDriverScript(), context);
			logger.info(Util.drawLineOfChars("=", 120));
			logger.info(StringUtils.center(
					"Completed " + register.getService(Thread.currentThread().getName(), "CurrentTestCase").toString()
							+ " on " + formatter.format(LocalDateTime.now()),
					120));
			logger.info(Util.drawLineOfChars("=", 120));
				
			if(Boolean.valueOf(properties.getProperty("ResultIntegration").equals("AZURE")))
			{
				AzureIntegration azureIntegration = new AzureIntegration();
				List<ITestNGMethod> currentMethods = Arrays.asList(context.getAllTestMethods()).stream()
						.filter((m) -> m.getRealClass() == this.getClass()).collect(Collectors.toList());
				boolean isFailed = context.getFailedTests().getAllMethods().stream()
						.anyMatch(m -> currentMethods.contains(m));
				String testCaseName = testParameters.getCurrentTestcase();
				String testReportName = driverScript.getReportName();
				String testStatus = (!isFailed)?"Passed":"Failed";
				String currentSuite = register.getService(Thread.currentThread().getName(), "CurrentSuite")
						.toString();
				String reportPath = register.getService(currentSuite, "ReportPath").toString();
				String htmlFile = 
						reportPath + Util.getFileSeparator() + "HTML Results" + Util.getFileSeparator()
								+ RunContext.getRunContext().getReportSettings().getReportName() + ".html";
				String strManualTcID = dataTable.getDataWithSubIteration("AzureTestCaseMapping", "ManualTC_ID","1");
				String strConfiguration = dataTable.getDataWithSubIteration("AzureTestCaseMapping", "Configuration","1");
				azureIntegration.updateStatusToAzureDevOps(testCaseName, testStatus, htmlFile, testReportName+".html",strManualTcID,strConfiguration);
				
			}

			register.removeService(Thread.currentThread().getName());
			
			/** Validate ALM INTEGRATION **/
			if (Boolean.valueOf(properties.getProperty("AlmIntegration"))) {
				try {
					Entity oldRun = (Entity) ALMContext.getALMContext()
							.getAlmData(Thread.currentThread().getName() + "Run");

					Entity runEntity = new Entity();
					runEntity.type("run");
					runEntity.id(oldRun.id());

					List<ITestNGMethod> currentMethods = Arrays.asList(context.getAllTestMethods()).stream()
							.filter((m) -> m.getRealClass() == this.getClass()).collect(Collectors.toList());

					boolean isFailed = context.getFailedTests().getAllMethods().stream()
							.anyMatch(m -> currentMethods.contains(m));
					
					List<Entity> entities = ALMContext.getALMContext().getSteps(Thread.currentThread().getName());
					
					boolean isNoRun = false;
							
					for(Entity e : entities) {
						String status = e.fieldValue("status");
						if(null == status || status.equalsIgnoreCase(Run.STATUS_NO_RUN)) {
							isNoRun = true;
							break;
						}
					}

					if (isFailed || isNoRun) {
						runEntity.fieldValue("status", Run.STATUS_FAILED);
					} else {
						runEntity.fieldValue("status", Run.STATUS_PASSED);
					}
					
					ServerTime serverTime = EntityUtil.getServerTime();
					String[] executionDateTime = serverTime.getServerDateTime().split(" ");

					runEntity.fieldValue("execution-date", executionDateTime[0]);
					runEntity.fieldValue("execution-time", executionDateTime[1]);

					endMillis = System.currentTimeMillis();

					long duration = endMillis - startMillis;
					long minutes = (duration / 1000) / 60;

					runEntity.fieldValue("duration", String.format("%s", minutes));
										EntityUtil.updateRun(runEntity);
				} catch (Exception e) {
					logger.error(String.format("ALM assertion error %s", e.getMessage()));
				}
			}
		}
	}

	/**
	 * Transfer parameters to temp parameters Since seleniumTestParameter has final
	 * parameters test scenario and test case we use this method
	 * 
	 * @param tParam
	 * @param testParameters
	 * @return
	 */
	@SuppressWarnings("unused")
	private SeleniumTestParameters getParams(SeleniumTestParameters tParam, SeleniumTestParameters testParameters) {

		synchronized (this) {

			tParam.setCurrentTestDescription(testParameters.getCurrentTestDescription());
			tParam.setCurrentTestInstance(testParameters.getCurrentTestInstance());

			tParam.setIterationMode(testParameters.getIterationMode());
			tParam.setStartIteration(testParameters.getStartIteration());
			tParam.setEndIteration(testParameters.getEndIteration());
			tParam.setExecutionMode(testParameters.getExecutionMode());
			tParam.setBrowser(testParameters.getBrowser());
			tParam.setBrowserVersion(testParameters.getBrowserVersion());
			tParam.setPlatform(testParameters.getPlatform());
			tParam.setPlatformVersion(testParameters.getPlatformVersion());
			tParam.setTestCasePackage(testParameters.getTestCasePackage());
			tParam.setDesktopAppIdentifier(testParameters.getDesktopAppIdentifier());

			return tParam;
		}

	}

	@AfterSuite
	public void afterSuite(ITestContext context) {
		synchronized (this) {
			logger.info(String.format("Executing After suite configuration for %s ", currentTestScenario));
					
			if (intALMRUNID ==0) {
				ResultSummaryManager.getInstance().launchResultSummary();
			}
			@SuppressWarnings("unchecked")
			List<Map<String, String>> configurationErrors = (List<Map<String, String>>) context
					.getAttribute("ConfigurationErrors");
			if (configurationErrors.size() > 0) {
				for (Map<String, String> err : configurationErrors) {
					SeleniumTestParameters params = new SeleniumTestParameters(err.get("TestScenario"),
							err.get("TestCase"));
					params.setCurrentTestInstance(err.getOrDefault("TestInstance", ""));
					params.setAdditionalDetails(err.get("AdditionalDetails"));
					ResultSummaryManager rsm = (ResultSummaryManager) context.getAttribute("RSM");
					rsm.updateResultSummary(params, "Error report", "", "failed");									
				}
			}

		}
	}

	private void setupTestConfiguration(ITestContext context) throws FrameworkException {
		synchronized (this) {

			logger.info(String.format("Fetching settings for Current Scenario is %s and current testcase is %s",
					currentTestScenario, currentTestCase));
			RunContext runContext = (RunContext) context.getAttribute("RunContext");

			testParameters = (SeleniumTestParameters) runContext.getSeleniumTestParameters();
			driverScript = new DriverScript(runContext);

			runContext.setDriverScript(driverScript);
			driverScript.driveTestExecution(context);

			scriptHelper = driverScript.getScriptHelper();
			runContext.setScriptHelper(scriptHelper);

			initialize(runContext.getScriptHelper());
			report.addTestLogSection("Iteration: " + Integer.toString(testParameters.getStartIteration()));
		}
	}

	/**
	 * Function to validate any soft assert in current test execution
	 */
	public void checkErrors() {

		synchronized (this) {

			if (report.getFailList().size() > 0) {

				String lastFail = report.getFailList().get(report.getFailList().size() - 1);
				lastFail = lastFail.replaceAll("<br>", "\n");
				lastFail = lastFail.replaceAll("<[^>]*>", "");
				
				/** VALIDATE ALM INTEGRATION **/
				if (Boolean.valueOf(properties.getProperty("AlmIntegration"))) {
					Entity stepEntity = (Entity) ALMContext.getALMContext()
							.getAlmData(Thread.currentThread().getName() + "Step");
					if(stepEntity != null) { 
						stepEntity.fieldValue("actual", lastFail);
					}
					ALMContext.getALMContext().addAlmData(Thread.currentThread().getName() + "Step", stepEntity);
				}
				
				register.putService(Thread.currentThread().getName() + "Failure", "True");
				if (!(Boolean.parseBoolean(
						register.getService(Thread.currentThread().getName(), "StopExecutionFlag").toString()))) {
					report.clearPassFail();
					throw new FrameworkAssertion(lastFail);
				}
				report.clearPassFail();
			} else {
				if (report.getPassList().size() > 0) {
					String lastPass = report.getPassList().get(report.getPassList().size() - 1);
					lastPass = lastPass.replaceAll("<br>", "\n");
					lastPass = lastPass.replaceAll("<[^>]*>", "");
					/** VALIDATE ALM INTEGRATION **/
					if (Boolean.valueOf(properties.getProperty("AlmIntegration"))) {
						Entity stepEntity = (Entity) ALMContext.getALMContext()
								.getAlmData(Thread.currentThread().getName() + "Step");
						if(stepEntity != null) { 
							stepEntity.fieldValue("actual", lastPass);
						}
						ALMContext.getALMContext().addAlmData(Thread.currentThread().getName() + "Step", stepEntity);
					}

					String isFailedAlready = (String) register.getService(Thread.currentThread().getName(), "Failure");
					if (null == isFailedAlready) {
						register.putService(Thread.currentThread().getName() + "Failure", "False");
					} else {
						if (!(Boolean.valueOf(isFailedAlready))) {
							register.putService(Thread.currentThread().getName() + "Failure", "False");
						} else {
							register.putService(Thread.currentThread().getName() + "Failure", "True");
						}
					}
					report.clearPassFail();
				}
			}

		}
	}

	private void createAlmConfiguration(ALMTarget almTarget) {
		ALMContext almContext = ALMContext.getALMContext();
		try {

			String planPath = getAlmProperty(almTarget.testplan());
			String labPath = getAlmProperty(almTarget.testlab());
			String testName = almTarget.testname();
			String setName = getAlmProperty(almTarget.testset());

			almContext.addAlmData(Thread.currentThread().getName() + "TestPlanPath", planPath);
			almContext.addAlmData(Thread.currentThread().getName() + "TestLabPath", labPath);
			almContext.addAlmData(Thread.currentThread().getName() + "TestName", testName);
			almContext.addAlmData(Thread.currentThread().getName() + "TestSetName", setName);

			String testId = EntityUtil.getTestId(planPath, testName);
			String testSetId = EntityUtil.getTestSetId(labPath, setName);

			almContext.addAlmData(Thread.currentThread().getName() + "TestId", testId);
			almContext.addAlmData(Thread.currentThread().getName() + "TestSetId", testSetId);

			Entity runEntity;
			if (intALMRUNID ==0) {
				runEntity = EntityUtil.createRun(planPath, labPath, testName, setName, Run.TEST_TYPE_MANUAL);
			}
			else {
				runEntity = EntityUtil.getRunById(Integer.toString(intALMRUNID));
			}
			almContext.addAlmData(Thread.currentThread().getName() + "Run", runEntity);
			
			Entity runEntityToUpdate = (Entity) ALMContext.getALMContext()
					.getAlmData(Thread.currentThread().getName() + "Run");

			Entity updateRunEntity = new Entity();
			updateRunEntity.type("run");
			updateRunEntity.id(runEntityToUpdate.id());
			updateRunEntity.fieldValue("user-01", "Draft"); 
			updateRunEntity.fieldValue("user-07", EntityUtil.getTestPhase(getAlmProperty(almTarget.testplan()), almTarget.testname())); 
			EntityUtil.updateRun(updateRunEntity);


			List<Entity> stepEntities = EntityUtil.createFilledRunSteps(planPath, testName, runEntity.id());

			stepEntities.forEach(e -> {
				almContext.addStep(Thread.currentThread().getName(), e);
			});

		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.error(String.format("ALM assertion error %s", e.getMessage()));
		}
	}

	private String getALMStatus(int status) {

		if (status == 1) {
			return Run.STATUS_PASSED;
		} else if (status == 2) {
			return Run.STATUS_FAILED;
		} else {
			return Run.STATUS_NO_RUN;
		}
	}

	@Override
	public void setUp() {
		// TODO Auto-generated method stub

	}

	@Override
	public void executeTest() {
		// TODO Auto-generated method stub

	}

	@Override
	public void tearDown() {
		// TODO Auto-generated method stub

	}

}
