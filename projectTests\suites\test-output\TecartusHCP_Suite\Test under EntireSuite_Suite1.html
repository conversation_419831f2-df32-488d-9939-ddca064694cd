<html>
<head>
<title>TestNG:  Test under EntireSuite_Suite1</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Test under EntireSuite_Suite1</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>14/0/0</td>
</tr><tr>
<td>Started on:</td><td>Wed Apr 23 18:08:00 IST 2025</td>
</tr>
<tr><td>Total time:</td><td>328 seconds (328374 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site</td>
<td></td>
<td>12</td>
<td>com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website.accessNavigationMenuOnYescartaHCP()'><b>accessNavigationMenuOnYescartaHCP</b><br>Test class: com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website</td>
<td></td>
<td>17</td>
<td>com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site.verifyLukemiaTreatmentSite()'><b>verifyLukemiaTreatmentSite</b><br>Test class: com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site</td>
<td></td>
<td>35</td>
<td>com.gilead.testscripts.TecartusHCP.To_Verify_Treatment_Site@712625fd</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website.tecartusHCPNavigationAndAPI()'><b>tecartusHCPNavigationAndAPI</b><br>Test class: com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website</td>
<td></td>
<td>40</td>
<td>com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website</td>
<td></td>
<td>7</td>
<td>com.gilead.testscripts.TecartusHCP.Navigation_and_Functionality_Verification_for_TecartusHCP_Website@7161d8d1</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website</td>
<td></td>
<td>6</td>
<td>com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site.verifyLukemiaResponseSite()'><b>verifyLukemiaResponseSite</b><br>Test class: com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site</td>
<td></td>
<td>26</td>
<td>com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website.tecartuHCPSearchFuntionalityVerification()'><b>tecartuHCPSearchFuntionalityVerification</b><br>Test class: com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website</td>
<td></td>
<td>23</td>
<td>com.gilead.testscripts.TecartusHCP.Search_Functionality_Verification_For_TecartusHCP_Website@4c60d6e9</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website</td>
<td></td>
<td>5</td>
<td>com.gilead.testscripts.TecartusHCP.API_Integration_And_Navigation_Verification_For_TecartusHCP_Website@17f9d882</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site</td>
<td></td>
<td>12</td>
<td>com.gilead.testscripts.TecartusHCP.To_Verify_Response_Site@4b013c76</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site.verifySafetyProfileSite()'><b>verifySafetyProfileSite</b><br>Test class: com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site</td>
<td></td>
<td>32</td>
<td>com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site</td>
<td></td>
<td>5</td>
<td>com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site</td>
<td></td>
<td>6</td>
<td>com.gilead.testscripts.TecartusHCP.To_Verify_Safety_Profile_Site@75437611</td></tr>
<tr>
<td title='com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site.verifyAdditionalEfficacyDataSite()'><b>verifyAdditionalEfficacyDataSite</b><br>Test class: com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site</td>
<td></td>
<td>25</td>
<td>com.gilead.testscripts.TecartusHCP.To_Verify_Additional_Efficacy_Data_Site@3a7442c7</td></tr>
</table><p>
</body>
</html>