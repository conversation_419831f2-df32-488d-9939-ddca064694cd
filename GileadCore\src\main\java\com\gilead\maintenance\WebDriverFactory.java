package com.gilead.maintenance;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.Platform;
import org.openqa.selenium.Proxy;
import org.openqa.selenium.Proxy.ProxyType;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.edge.EdgeDriver;
import org.openqa.selenium.edge.EdgeOptions;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.ie.InternetExplorerDriver;
//import org.openqa.selenium.phantomjs.PhantomJSDriver;
import org.openqa.selenium.remote.CapabilityType;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.safari.SafariDriver;
import com.gilead.utils.Util;
import com.gilead.config.FrameworkException;
import com.gilead.reports.Browser;
import com.gilead.utils.ServiceRegister;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.windows.WindowsDriver;
import io.github.bonigarcia.wdm.WebDriverManager;

/**
 * Factory class for creating the {@link WebDriver} object as required
 * 
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class WebDriverFactory {
	private static Properties properties;
	private static final Logger logger = LogManager.getLogger("loggers");
	//private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());
	private FileLockMechanism webdriverLock = new FileLockMechanism(15);

	private WebDriverFactory() {
		// To prevent external instantiation of this class
	}
	
	public static String getChromeBrowserVersion(WebDriver driver) {
		return ((ChromeDriver) driver).getCapabilities().getBrowserVersion();
	}

	public static String getEdgeBrowserVersion(WebDriver driver) {
		return ((EdgeDriver) driver).getCapabilities().getBrowserVersion();
	}

	/**
	 * Function to return the appropriate {@link WebDriver} object based on the
	 * parameters passed
	 * 
	 * @param browser The {@link Browser} to be used for the test execution
	 * @return The corresponding {@link WebDriver} object
	 */

	public static WebDriver getWebDriver(Browser browser) {
		WebDriver driver = null;
		properties = Settings.getInstance();
		switch (browser) {
		case CHROME:
			WebDriverManager.chromedriver().clearDriverCache().setup();
			Map<String, Object> prefs = new HashMap<String, Object>();
			if (properties.getProperty("ChangeDefaultDownloadPath").equalsIgnoreCase("Yes")) {
				prefs.put("download.default_directory", System.getProperty("user.dir") + Util.getFileSeparator()
						+ properties.getProperty("DownloadPath"));
			}
			if (properties.getProperty("DisablePromptForDownload").equalsIgnoreCase("Yes")) {
				prefs.put("download.prompt_for_download", false);
			}
			if (properties.getProperty("DisableChromePDFViewer").equalsIgnoreCase("Yes")) {
				prefs.put("plugins.plugins_disabled", "Chrome PDF Viewer");
			}
			if (properties.getProperty("EnableOpenPDFExternally").equalsIgnoreCase("Yes")) {
				prefs.put("plugins.always_open_pdf_externally", true);
			}
			if (properties.getProperty("DisbaleNotificationsANDPopUps").equalsIgnoreCase("Yes")) {
				prefs.put("profile.default_content_setting_values.notifications", 2);
				prefs.put("profile.default_content_settings.popups", 0);
			}			
			ChromeOptions options = new ChromeOptions();
			options.setExperimentalOption("prefs", prefs);
			if (properties.getProperty("DefaultLanguageEnglish").equalsIgnoreCase("Yes")) {
				options.addArguments("--lang=en");
			}
			if (properties.getProperty("MuteAudio").equalsIgnoreCase("Yes")) {
				options.addArguments("--mute-audio");
			}
			if (properties.getProperty("Incognito").equalsIgnoreCase("Yes")) {
				options.addArguments("--incognito");
			}
			if (properties.getProperty("EnableClipboard").equalsIgnoreCase("Yes")) {
				options.addArguments("--enable-clipboard");
			}
			options.addArguments("--remote-allow-origins=*");
			if (properties.getProperty("ManualBrowserRequired").equalsIgnoreCase("Yes")) {
				String strUserName = System.getProperty("user.name");
				options.addArguments(
						"user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data");
			}
			driver = new ChromeDriver(options);
			break;

		case FIREFOX:
			// Takes the system proxy settings automatically
			System.setProperty("webdriver.gecko.driver", properties.getProperty("GeckoDriverPath"));
			driver = new FirefoxDriver();
			break;

		case GHOST_DRIVER:
			// Takes the system proxy settings automatically (I think!)

			/*
			 * System.setProperty("phantomjs.binary.path",
			 * properties.getProperty("PhantomJSPath")); driver = new PhantomJSDriver();
			 */
			break;

		case INTERNET_EXPLORER:
			// Takes the system proxy settings automatically

			System.setProperty("webdriver.ie.driver", properties.getProperty("InternetExplorerDriverPath"));
			driver = new InternetExplorerDriver();
			break;

		case EDGE:
			// Takes the system proxy settings automatically

			WebDriverManager.edgedriver().clearDriverCache().setup();
			Map<String, Object> edgePrefs = new HashMap<String, Object>();
			if (properties.getProperty("ChangeDefaultDownloadPath").equalsIgnoreCase("Yes")) {
				edgePrefs.put("download.default_directory", System.getProperty("user.dir") + Util.getFileSeparator()
				+ properties.getProperty("DownloadPath"));
			}
			if (properties.getProperty("EnableOpenPDFExternally").equalsIgnoreCase("Yes")) {
				edgePrefs.put("plugins.always_open_pdf_externally", true);
			}
			EdgeOptions edgeOptions = new EdgeOptions();
			edgeOptions.addArguments("--remote-allow-origins=*");
			edgeOptions.setCapability("prefs", edgePrefs);
			if (properties.getProperty("Incognito").equalsIgnoreCase("Yes")) {
				edgeOptions.addArguments("--inprivate");
			}
			driver = new EdgeDriver(edgeOptions);
			break;

		case SAFARI:
			// Takes the system proxy settings automatically

			driver = new SafariDriver();
			break;

		default:
			throw new FrameworkException("Unhandled browser!");
		}

		logger.info(String.format("Created driver for %s with session id %s", browser,
				((RemoteWebDriver) driver).getSessionId()));
		ServiceRegister register = ServiceRegister.getInstance();
		String sessionId = ((RemoteWebDriver) driver).getSessionId().toString();
		register.putService(Thread.currentThread().getName() + "Session", sessionId);
		register.putService(sessionId + "WD", driver);
		return driver;
	}

	private static DesiredCapabilities getProxyCapabilities() {
		properties = Settings.getInstance();
		String proxyUrl = properties.getProperty("ProxyHost") + ":" + properties.getProperty("ProxyPort");

		Proxy proxy = new Proxy();
		proxy.setProxyType(ProxyType.MANUAL);
		proxy.setHttpProxy(proxyUrl);
		proxy.setFtpProxy(proxyUrl);
		proxy.setSslProxy(proxyUrl);

		DesiredCapabilities desiredCapabilities = new DesiredCapabilities();
		desiredCapabilities.setCapability(CapabilityType.PROXY, proxy);

		return desiredCapabilities;
	}
	
	public static WindowsDriver getWindowsDriver(String strDestopAppPath) {
		DesiredCapabilities capabilities = new DesiredCapabilities();        
        capabilities.setCapability("automationName", "windows");
        capabilities.setCapability("app", strDestopAppPath);
        capabilities.setCapability("ms:experimental-webdriver", true);
        capabilities.setCapability("ms:waitForAppLaunch", "25");
		try {
			return new WindowsDriver(new URL("http://127.0.0.1:4723"), capabilities);
		} catch (MalformedURLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}
	
	public static AppiumDriver getMac2Driver(String strDestopAppPath) {
		DesiredCapabilities capabilities = new DesiredCapabilities();
		capabilities.setCapability("platformName", "Mac");
		capabilities.setCapability("appium:automationName", "Mac2");
		capabilities.setCapability("appium:bundleId", strDestopAppPath);
		try{
			return new AppiumDriver(new URL("http://localhost:4723/wd/hub"), capabilities);
		}
		catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * Function to return the {@link RemoteWebDriver} object based on the parameters
	 * passed
	 * 
	 * @param browser        The {@link Browser} to be used for the test execution
	 * @param browserVersion The browser version to be used for the test execution
	 * @param platform       The {@link Platform} to be used for the test execution
	 * @param remoteUrl      The URL of the remote machine to be used for the test
	 *                       execution
	 * @return The corresponding {@link RemoteWebDriver} object
	 */
	public static WebDriver getRemoteWebDriver(Browser browser, String browserVersion, Platform platform,
			String remoteUrl) {
		// For running RemoteWebDriver tests in Chrome and IE:
		// The ChromeDriver and IEDriver executables needs to be in the PATH of
		// the remote machine
		// To set the executable path manually, use:
		// java -Dwebdriver.chrome.driver=/path/to/driver -jar
		// selenium-server-standalone.jar
		// java -Dwebdriver.ie.driver=/path/to/driver -jar
		// selenium-server-standalone.jar

//		properties = Settings.getInstance();
//
//		boolean proxyRequired = Boolean.parseBoolean(properties.getProperty("ProxyRequired"));
//
//		DesiredCapabilities desiredCapabilities = null;
//		if (proxyRequired) {
//			desiredCapabilities = getProxyCapabilities();
//		} else {
//			desiredCapabilities = new DesiredCapabilities();
//		}
//
//		desiredCapabilities.setBrowserName(browser.getValue());
//
//		if (browserVersion != null) {
//			desiredCapabilities.setVersion(browserVersion);
//		}
//		if (platform != null) {
//			desiredCapabilities.setPlatform(platform);
//		}
//	
//
//		HashMap<String, Object> prefs = new HashMap<String, Object>();
//		prefs.put("download.default_directory", "./");
//		prefs.put("safebrowsing.enabled", "false");
//		ChromeOptions co = new ChromeOptions();
//		co.setExperimentalOption("prefs", prefs);
//		co.addArguments("--disable-notifications");
//		desiredCapabilities.setCapability(ChromeOptions.CAPABILITY, co);
//		desiredCapabilities.setJavascriptEnabled(true); // Pre-requisite for
//														// remote execution
//
//		URL url = getUrl(remoteUrl);
//		
//		return new RemoteWebDriver(url, desiredCapabilities);

		properties = Settings.getInstance();

		boolean proxyRequired = Boolean.parseBoolean(properties.getProperty("ProxyRequired"));

		DesiredCapabilities desiredCapabilities = null;
		if (proxyRequired) {
			desiredCapabilities = getProxyCapabilities();
		} else {
			desiredCapabilities = new DesiredCapabilities();
		}

		desiredCapabilities.setBrowserName(browser.getValue());

		if (browserVersion != null) {
			desiredCapabilities.setVersion(browserVersion);
		}
		if (platform != null) {
			desiredCapabilities.setPlatform(platform);
		}

		HashMap<String, Object> prefs = new HashMap<String, Object>();
		prefs.put("download.default_directory", "./");
		prefs.put("safebrowsing.enabled", "false");
		ChromeOptions co = new ChromeOptions();
		co.setExperimentalOption("prefs", prefs);
		co.addArguments("--disable-notifications");
		co.addArguments("--no-sandbox");
		co.addArguments("--disable-dev-shm-usage");
		co.addArguments("--disable-gpu");
		// Shurender added below
		co.addArguments("--start-maximized"); 
		desiredCapabilities.setCapability(ChromeOptions.CAPABILITY, co);
		desiredCapabilities.setJavascriptEnabled(true); // Pre-requisite for remote execution

		synchronized (WebDriverFactory.class) {
			//logger.info(String.format("trying to connect remote url %s",remoteUrl));
			URL url = getUrl(remoteUrl);
			WebDriver driver = new RemoteWebDriver(url, desiredCapabilities);
			logger.info(String.format("Created remote web driver for %s with session id %s", browser.getValue(),
					((RemoteWebDriver) driver).getSessionId()));
			ServiceRegister register = ServiceRegister.getInstance();
			String sessionId = ((RemoteWebDriver) driver).getSessionId().toString();
			register.putService(Thread.currentThread().getName() + "Session", sessionId);
			register.putService(sessionId + "WD", driver);
			return driver;
		}
		
	}

	private static URL getUrl(String remoteUrl) {
		URL url;
		try {
			url = new URL(remoteUrl);
		} catch (MalformedURLException e) {
			e.printStackTrace();
			throw new FrameworkException("The specified remote URL is malformed");
		}
		return url;
	}

	/**
	 * Function to return the {@link RemoteWebDriver} object based on the parameters
	 * passed
	 * 
	 * @param browser   The {@link Browser} to be used for the test execution
	 * @param remoteUrl The URL of the remote machine to be used for the test
	 *                  execution
	 * @return The corresponding {@link RemoteWebDriver} object
	 */
	public static WebDriver getRemoteWebDriver(Browser browser, String remoteUrl) {
		return getRemoteWebDriver(browser, null, null, remoteUrl);
	}

	/**
	 * Function to return the {@link ChromeDriver} object emulating the device
	 * specified by the user
	 * 
	 * @param deviceName The name of the device to be emulated (check Chrome Dev
	 *                   Tools for a list of available devices)
	 * @return The corresponding {@link ChromeDriver} object
	 */
	public static WebDriver getEmulatedWebDriver(String deviceName) {
		ChromeOptions cOptions = getEmulatedChromeDriverCapabilities(deviceName);

		properties = Settings.getInstance();
		System.setProperty("webdriver.chrome.driver", properties.getProperty("ChromeDriverPath"));

		return new ChromeDriver(cOptions);
	}

	private static ChromeOptions getEmulatedChromeDriverCapabilities(String deviceName) {
		Map<String, String> mobileEmulation = new HashMap<String, String>();
		mobileEmulation.put("deviceName", deviceName);

		Map<String, Object> chromeOptions = new HashMap<String, Object>();
		chromeOptions.put("mobileEmulation", mobileEmulation);

		ChromeOptions cOptions = new ChromeOptions();
		cOptions.setCapability("mobileEmulation", mobileEmulation);
		/*
		 * DesiredCapabilities desiredCapabilities = DesiredCapabilities.chrome();
		 * desiredCapabilities.setCapability(ChromeOptions.CAPABILITY, chromeOptions);
		 */

		return cOptions;
	}

	/**
	 * Function to return the {@link RemoteWebDriver} object emulating the device
	 * specified by the user
	 * 
	 * @param deviceName The name of the device to be emulated (check Chrome Dev
	 *                   Tools for a list of available devices)
	 * @param remoteUrl  The URL of the remote machine to be used for the test
	 *                   execution
	 * @return The corresponding {@link RemoteWebDriver} object
	 */
	public static WebDriver getEmulatedRemoteWebDriver(String deviceName, String remoteUrl) {
		ChromeOptions cOptions = getEmulatedChromeDriverCapabilities(deviceName);
		// desiredCapabilities.setJavascriptEnabled(true); // Pre-requisite for
		// remote execution
		cOptions.addArguments("--enable-javascript");

		URL url = getUrl(remoteUrl);

		return new RemoteWebDriver(url, cOptions);
	}

	/**
	 * Function to return the {@link ChromeDriver} object emulating the device
	 * attributes specified by the user
	 * 
	 * @param deviceWidth      The width of the device to be emulated (in pixels)
	 * @param deviceHeight     The height of the device to be emulated (in pixels)
	 * @param devicePixelRatio The device's pixel ratio
	 * @param userAgent        The user agent string
	 * @return The corresponding {@link ChromeDriver} object
	 */
	public static WebDriver getEmulatedWebDriver(int deviceWidth, int deviceHeight, float devicePixelRatio,
			String userAgent) {
		ChromeOptions cOptions = getEmulatedChromeDriverCapabilities(deviceWidth, deviceHeight, devicePixelRatio,
				userAgent);

		properties = Settings.getInstance();
		System.setProperty("webdriver.chrome.driver", properties.getProperty("ChromeDriverPath"));

		return new ChromeDriver(cOptions);
	}

	private static ChromeOptions getEmulatedChromeDriverCapabilities(int deviceWidth, int deviceHeight,
			float devicePixelRatio, String userAgent) {
		Map<String, Object> deviceMetrics = new HashMap<String, Object>();
		deviceMetrics.put("width", deviceWidth);
		deviceMetrics.put("height", deviceHeight);
		deviceMetrics.put("pixelRatio", devicePixelRatio);

		Map<String, Object> mobileEmulation = new HashMap<String, Object>();
		mobileEmulation.put("deviceMetrics", deviceMetrics);
		// mobileEmulation.put("userAgent", "Mozilla/5.0 (Linux; Android 4.2.1;
		// en-us; Nexus 5 Build/JOP40D) AppleWebKit/535.19 (KHTML, like Gecko)
		// Chrome/18.0.1025.166 Mobile Safari/535.19");
		mobileEmulation.put("userAgent", userAgent);

		Map<String, Object> chromeOptions = new HashMap<String, Object>();
		chromeOptions.put("mobileEmulation", mobileEmulation);

		ChromeOptions cOptions = new ChromeOptions();
		cOptions.setCapability("mobileEmulation", mobileEmulation);
		/*
		 * DesiredCapabilities desiredCapabilities = DesiredCapabilities.chrome();
		 * desiredCapabilities.setCapability(ChromeOptions.CAPABILITY, chromeOptions);
		 */
		return cOptions;
	}

	/**
	 * Function to return the {@link RemoteWebDriver} object emulating the device
	 * attributes specified by the user
	 * 
	 * @param deviceWidth      The width of the device to be emulated (in pixels)
	 * @param deviceHeight     The height of the device to be emulated (in pixels)
	 * @param devicePixelRatio The device's pixel ratio
	 * @param userAgent        The user agent string
	 * @param remoteUrl        The URL of the remote machine to be used for the test
	 *                         execution
	 * @return The corresponding {@link RemoteWebDriver} object
	 */
	public static WebDriver getEmulatedRemoteWebDriver(int deviceWidth, int deviceHeight, float devicePixelRatio,
			String userAgent, String remoteUrl) {
		/*
		 * DesiredCapabilities desiredCapabilities =
		 * getEmulatedChromeDriverCapabilities(deviceWidth, deviceHeight,
		 * devicePixelRatio, userAgent);
		 */
		// desiredCapabilities.setJavascriptEnabled(true); // Pre-requisite for
		// remote execution
		ChromeOptions cOptions = getEmulatedChromeDriverCapabilities(deviceWidth, deviceHeight, devicePixelRatio,
				userAgent);
		cOptions.addArguments("--enable-javascript");
		cOptions.setCapability("javascript.enabled", true);
		URL url = getUrl(remoteUrl);

		return new RemoteWebDriver(url, cOptions);
	}

}