# Set to debug or trace if log4j initialization is failing
status = info

# Name of the configuration
name = ConsoleLogConfigDemo

# Console appender configuration
appender.console.type = Console
appender.console.name = consoleLogger
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = [%d{yyyy-MM-dd HH:mm:ss}] [%-5p] %-12t is %m%n

# Root logger level
rootLogger.level = debug

# Root logger referring to console appender
rootLogger.appenderRef.stdout.ref = consoleLogger

#status = info
name= RollingFileLogConfigDemo

# Log files location
property.basePath = src\\test\\resources\\logs

# RollingFileAppender name, pattern, path and rollover policy
appender.rolling.type = RollingFile
appender.rolling.name = fileLogger
#appender.rolling.fileName= ${basePath}/app.log
appender.rolling.fileName= ${basePath}/app_${date:yyyy-MM-dd-HHmmss}.log
appender.rolling.filePattern= ${basePath}/app_%d{yyyyMMdd}.log.gz
appender.rolling.layout.type = PatternLayout
appender.rolling.layout.pattern = [%d{yyyy-MM-dd HH:mm:ss}] [%-5p] %-12t is %m%n
appender.rolling.policies.type = Policies

# RollingFileAppender rotation policy
appender.rolling.policies.size.type = SizeBasedTriggeringPolicy
appender.rolling.policies.size.size = 20MB
appender.rolling.policies.time.type = TimeBasedTriggeringPolicy
appender.rolling.policies.time.interval = 1
appender.rolling.policies.time.modulate = true
appender.rolling.strategy.type = DefaultRolloverStrategy
appender.rolling.strategy.delete.type = Delete
appender.rolling.strategy.delete.basePath = ${basePath}
appender.rolling.strategy.delete.maxDepth = 10
appender.rolling.strategy.delete.ifLastModified.type = IfLastModified

# Delete all files older than 7 days
appender.rolling.strategy.delete.ifLastModified.age = 7d

# Configure root logger
rootLogger.level = debug
rootLogger.appenderRef.rolling.ref = fileLogger