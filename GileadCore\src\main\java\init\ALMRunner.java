package init;

import java.lang.invoke.MethodHandles;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.testng.TestNG;
import org.testng.xml.XmlClass;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlTest;

import com.gilead.config.FrameworkAssertion;
import com.gilead.config.FrameworkException;
import com.gilead.maintenance.ResultSummaryManager;

public class ALMRunner {

	@SuppressWarnings("unused")
	private static final Logger logger = LogManager.getLogger(MethodHandles.lookup().lookupClass());

	public static void main(String[] args)
			throws ClassNotFoundException, InstantiationException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException {

		if (args.length != 3) {
			throw new FrameworkAssertion(
					"Required arguments (TestScenario, TestCase, PackageName) are not passed.");
		}

		String currentTestScenario = args[0];
		String currentTestCase = args[1];
		String packageName = args[2];

		ResultSummaryManager.getInstance().setRelativePath();

		try {

			
			TestNG testng = new TestNG();			
			List<XmlSuite> suites = new ArrayList<XmlSuite>();
			List<XmlTest> tests = new ArrayList<XmlTest>();
			List<String> listeners = new ArrayList<String>();
			

			XmlSuite suite = new XmlSuite();
			suite.setName(currentTestScenario);
			suite.setListeners(listeners);
			XmlTest test = new XmlTest(suite);
			test.setName("Test under " + currentTestScenario);
			List<XmlClass> xmlClasses = new ArrayList<XmlClass>();
			xmlClasses.add(new XmlClass(packageName + "." + currentTestCase));
			test.setXmlClasses(xmlClasses);
			tests.add(test);
			suites.add(suite);
			
			testng.setXmlSuites(suites);
			testng.run();

		} catch (Exception e) {
			throw new FrameworkException("TestNG class execution error", "Error occured : "+e.getMessage());
		}
	}

}
