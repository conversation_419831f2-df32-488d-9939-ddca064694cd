id: testng_annotations_priority
message: "Ensure TestNG classes must have Test annotation with priority as integer starts from 1"
language: java
severity: warning
rule:
  kind: method_declaration
  has:
    stopBy: end
    kind: annotation
    has:
      kind: annotation_argument_list
      not:
        all:
        - {has: {stopBy: end, kind: identifier, regex: priority}}
        - {has: {stopBy: end, kind: decimal_integer_literal}}
        - {not: {has: {stopBy: end, kind: unary_expression}}}
  inside:
    stopBy: end
    kind: program
    has:
      stopBy: end
      kind: package_declaration
      has:
        kind: scoped_identifier
        regex: com.gilead.testscripts
---
id: testng_annotations
message: "Ensure TestNG classes must have Test annotation with priority"
language: java
severity: warning
rule:
  kind: method_declaration
  has:
    stopBy: end
    kind: marker_annotation
