<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite guice-stage="DEVELOPMENT" name="Failed suite [FunctionalRegression_Suite]">
  <test thread-count="5" name="Test under EntireSuite_Suite1(failed)" parallel="classes">
    <parameter name="RunID" value="0"/>
    <classes>
      <class name="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002">
        <methods>
          <include name="afterSuite"/>
          <include name="invokeURL"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002 -->
      <class name="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="verifyCriticalComponents"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004 -->
      <class name="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003">
        <methods>
          <include name="afterSuite"/>
          <include name="setUpTestRunner"/>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="invokeURL"/>
          <include name="tearDownTestSuite"/>
          <include name="afterMethod"/>
          <include name="setUpTestSuite"/>
        </methods>
      </class> <!-- com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003 -->
    </classes>
  </test> <!-- Test under EntireSuite_Suite1(failed) -->
</suite> <!-- Failed suite [FunctionalRegression_Suite] -->
