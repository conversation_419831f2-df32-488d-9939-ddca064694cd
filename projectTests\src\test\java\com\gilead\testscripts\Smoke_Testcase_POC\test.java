package com.gilead.testscripts.Smoke_Testcase_POC;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;


public class test extends BaseTest {

	CommonFunctions objCommonFunctions;


	@Test(priority = 1)
	public void invokeURL() {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void checkLinksInWebPage() {
		try {
			objCommonFunctions.verifyLinksInWebPageLoc();
		} finally {
			checkErrors();
		}
	}
	@Test(priority = 3)
	public void validatePopup() {
		try {
			objCommonFunctions.verifyConfirmationPopupLoc();
		} finally {
			checkErrors();
		}
	}
	@Test(priority = 4)
	public void invokeSecondURL() {
		try {
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 5)
	public void checkComponentPresents() {
		try {
			objCommonFunctions.validateComponentExists();
		} finally {
			checkErrors();
		}
	}
}
