<html>
<head>
<title>TestNG:  Test under EntireSuite_Suite1</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Test under EntireSuite_Suite1</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>17/1/0</td>
</tr><tr>
<td>Started on:</td><td>Tue Apr 22 11:45:31 IST 2025</td>
</tr>
<tr><td>Total time:</td><td>730 seconds (730883 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site.verifyReceivingYescarta()'><b>verifyReceivingYescarta</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site</td>
<td><div><pre>java.lang.RuntimeException: Error handling video playback for &apos;What&apos;s the CAR T treatment process like? from Gilead Sciences on Vimeo&apos; on page &apos;Tecartus&apos;: javascript error: Cannot read properties of null (reading &apos;pause&apos;)
  (Session info: chrome=131.0.6778.265)
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector(&apos;video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video&apos;).pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad
	at businesscomponents.CommonFunctions.handleVideoPlayAndClose(CommonFunctions.java:4038)
	at businesscomponents.CommonFunctions.verifyReceivingSite(CommonFunctions.java:4416)
	at com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site.verifyReceivingYescarta(To_Verify_receiving_tecartus_site.java:28)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.openqa.selenium.JavascriptException: javascript error: Cannot read properties of null (reading &apos;pause&apos;)
  (Session info: chrome=131.0.6778.265)
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector(&apos;video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video&apos;).pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.RemoteWebDriver.executeScript(RemoteWebDriver.java:492)
	at businesscomponents.CommonFunctions.videoPlay(CommonFunctions.java:1077)
	at businesscomponents.CommonFunctions.handleVideoPlayAndClose(CommonFunctions.java:4034)
	... 17 more
... Removed 16 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1395875045", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1395875045'><pre>java.lang.RuntimeException: Error handling video playback for &apos;What&apos;s the CAR T treatment process like? from Gilead Sciences on Vimeo&apos; on page &apos;Tecartus&apos;: javascript error: Cannot read properties of null (reading &apos;pause&apos;)
  (Session info: chrome=131.0.6778.265)
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector(&apos;video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video&apos;).pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad
	at businesscomponents.CommonFunctions.handleVideoPlayAndClose(CommonFunctions.java:4038)
	at businesscomponents.CommonFunctions.verifyReceivingSite(CommonFunctions.java:4416)
	at com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site.verifyReceivingYescarta(To_Verify_receiving_tecartus_site.java:28)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:133)
	at org.testng.internal.TestInvoker.invokeMethod(TestInvoker.java:598)
	at org.testng.internal.TestInvoker.invokeTestMethod(TestInvoker.java:173)
	at org.testng.internal.MethodRunner.runInSequence(MethodRunner.java:46)
	at org.testng.internal.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:824)
	at org.testng.internal.TestInvoker.invokeTestMethods(TestInvoker.java:146)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:146)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.openqa.selenium.JavascriptException: javascript error: Cannot read properties of null (reading &apos;pause&apos;)
  (Session info: chrome=131.0.6778.265)
Build info: version: &apos;4.1.2&apos;, revision: &apos;9a5a329c5a&apos;
System info: host: &apos;Z1VD7STHPRDN255&apos;, ip: &apos;************&apos;, os.name: &apos;Windows 10&apos;, os.arch: &apos;amd64&apos;, os.version: &apos;10.0&apos;, java.version: &apos;1.8.0_291&apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector(&apos;video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video&apos;).pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
	at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
	at org.openqa.selenium.remote.RemoteWebDriver.executeScript(RemoteWebDriver.java:492)
	at businesscomponents.CommonFunctions.videoPlay(CommonFunctions.java:1077)
	at businesscomponents.CommonFunctions.handleVideoPlayAndClose(CommonFunctions.java:4034)
	... 17 more
</pre></div></td>
<td>79</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website.PIandNavigationVerification()'><b>PIandNavigationVerification</b><br>Test class: com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website</td>
<td></td>
<td>110</td>
<td>com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site</td>
<td></td>
<td>4</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site</td>
<td></td>
<td>9</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site.verifySupportAndResourcesSite()'><b>verifySupportAndResourcesSite</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site</td>
<td></td>
<td>181</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site.verifyYescartaSite()'><b>verifyYescartaSite</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site</td>
<td></td>
<td>66</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website.navigationVerification()'><b>navigationVerification</b><br>Test class: com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website</td>
<td></td>
<td>308</td>
<td>com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site.verifyClinicalTrialResults()'><b>verifyClinicalTrialResults</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site</td>
<td></td>
<td>15</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site@742ff096</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website</td>
<td></td>
<td>8</td>
<td>com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website.verifyNavigationHeaders()'><b>verifyNavigationHeaders</b><br>Test class: com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website</td>
<td></td>
<td>670</td>
<td>com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website.PDFverification()'><b>PDFverification</b><br>Test class: com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website</td>
<td></td>
<td>15</td>
<td>com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website@8519cb4</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site</td>
<td></td>
<td>5</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site@1c5920df</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website</td>
<td></td>
<td>40</td>
<td>com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website@1cf6d1be</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site</td>
<td></td>
<td>10</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site@cb0755b</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site</td>
<td></td>
<td>40</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site@548d708a</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website</td>
<td></td>
<td>7</td>
<td>com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website@7f485fda</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website.invokeURL()'><b>invokeURL</b><br>Test class: com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website</td>
<td></td>
<td>8</td>
<td>com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website@4a83a74a</td></tr>
<tr>
<td title='com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site.verifyManagingSideEffectsSite()'><b>verifyManagingSideEffectsSite</b><br>Test class: com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site</td>
<td></td>
<td>150</td>
<td>com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site@196a42c3</td></tr>
</table><p>
</body>
</html>