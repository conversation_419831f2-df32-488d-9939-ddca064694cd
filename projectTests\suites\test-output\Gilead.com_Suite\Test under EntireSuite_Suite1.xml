<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="21" name="Test under EntireSuite_Suite1" tests="18" failures="0" timestamp="2025-04-02T13:32:26 IST" time="315.152" errors="0">
  <testcase name="invokeURL" time="20.668" classname="com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001"/>
  <testcase name="verifyHeader" time="5.145" classname="com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001"/>
  <testcase name="verifyFooter" time="3.901" classname="com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001"/>
  <testcase name="invokeURL" time="38.3" classname="com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002"/>
  <testcase name="verifyMenuComponents" time="5.626" classname="com.gilead.testscripts.FunctionalRegression.Gilead_HomePage_TC001"/>
  <testcase name="invokeURL" time="12.203" classname="com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003"/>
  <testcase name="globalSearchFuntionality" time="24.467" classname="com.gilead.testscripts.FunctionalRegression.Gilead_GlobalSearchFuntionality_TC002"/>
  <testcase name="invokeURL" time="20.31" classname="com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008"/>
  <testcase name="verifyMedicinesPage" time="36.415" classname="com.gilead.testscripts.FunctionalRegression.Gilead_Medicines_TC008"/>
  <testcase name="newsPressRelease" time="70.129" classname="com.gilead.testscripts.FunctionalRegression.Gilead_NewsPressRelease_TC003"/>
  <testcase name="invokeURL" time="14.697" classname="com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007"/>
  <testcase name="invokeURL" time="21.215" classname="com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006"/>
  <testcase name="verifyPipeline" time="30.738" classname="com.gilead.testscripts.FunctionalRegression.Gilead_SciencePipeline_TC006"/>
  <testcase name="verifySTA" time="75.568" classname="com.gilead.testscripts.FunctionalRegression.Gilead_ScienceTherapeuticAreas_TC007"/>
  <testcase name="invokeURL" time="14.837" classname="com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005"/>
  <testcase name="invokeURL" time="13.058" classname="com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004"/>
  <testcase name="verifyCompanyStatement" time="53.502" classname="com.gilead.testscripts.FunctionalRegression.Gilead_CompanyStatement_TC005"/>
  <testcase name="verifyStoryPage" time="44.235" classname="com.gilead.testscripts.FunctionalRegression.Gilead_StoryPage_TC004"/>
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
