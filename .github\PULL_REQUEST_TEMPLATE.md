> [!IMPORTANT]
> * **Do not change the template.**
> * **TestScript table given in Summary section given for reference. Must be edited with actual test script details**

> [!TIP]
> * Type X within the square brackets to checkmark an item
> * Use Preview tab to check on the format of the PR summary

# Summary

Please include the list of test scripts created / maintained / hot fixed. Copy the Test script name and the test description from the Run Manager sheet.

| TestScript  | Description |
| ------------- | ------------- |
| DataPrivacyIncident_001_PostiveFlowUptoLegalReviewerApproval      | To verify whether SPARC user is able to complete the flow of DPIR till Legal Reviewer Approval. |
| DataPrivacyIncident_003_IRManagerRejection | To verify whether SPARC user is able to see the IR Manager receiving notification when DPI is moved to Breach Notification state. |
| DataPrivacyIncident_002_IRManagerApprovalAndLegalReviewerRejection | To verify whether SPARC user is able to see the DPI getting moved to Breach Notification state when IR Manager approves and Legal Reviewer rejects the DPI record. 

## Type of change
- [ ] Maintenance / Enhancement (as part of Sustainment)
- [ ] New test cases (added to support the new release)
- [ ] Hot fix (fixed the script issue)

## How Has This Been Tested?
- [ ] Unit Tested
- [ ] Dry Run

**Tested on**:
- [ ] Docker container
- [ ] Developer VDI

## Has the datasheet been stored and version controlled in the sharepoint?
- [ ] Yes
- [ ] No (Please specify the reason)

## Have you included the latest datasheet in codebase from sharepoint?
- [ ] Yes
- [ ] No (Please specify the reason)

## Have you set the DatatableCoAuthoringEnabled flag as False in project properties?
- [ ] Yes
- [ ] No (Please specify the reason)

## Has the Submodule been tagged to the latest commit ?
- [ ] Yes
- [ ] No (Please specify the reason)

## Checklist:
- [ ] My code follows the approved design approach
- [ ] I have performed a self-review of my own code (use the code review checklist[^1])
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] My changes do not have any unused functions or variables or packages

## Additional details / Remarks:



<sub>*References*</sub>
[^1]: [Code Review Checklist](https://gileadconnect.sharepoint.com/:x:/r/teams/IDMAutomation/Shared%20Documents/Automation%20TCOE%20Templates%20and%20Documents/Coding%20Standard/CodeReviewChecklist_SelfAssessment.xlsx?d=w50f30ab6a9e34367a18650ef7fb5f22e&csf=1&web=1&e=TylXJx)

