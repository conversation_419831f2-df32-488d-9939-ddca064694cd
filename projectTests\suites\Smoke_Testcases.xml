<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="FunctionalRegression_Suite">


	<test name="Test under EntireSuite_Suite1" thread-count="3"
		parallel="classes">
		<parameter name="RunID" value="0" />
		<!--<test name="Test under EntireSuite_Suite1" thread-count="1" > -->
		<classes>
		<!-- Demmo-->
		<class
				name="com.gilead.testscripts.Smoke_Testcases.HstvPRO_PeerInsights_TC002" />
			<!--<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_at" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_fi" /> 
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_gr" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_ie" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_pl" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_es" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_cz" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_fr" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_de" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_it" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_nl" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_no" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_pt" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_se" />

			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_dk" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_uk" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_au" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_il" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_be" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_ch" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_sk" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_ca" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_sg" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_sa" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_lu" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_br" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_jp" />-->
			<!--<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_Out_Patient_Studies_TC005" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_InPatient_Studies_TC004" />-->
			<!--<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_Stay_Informed_Signup_Form_TC003" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_Dosing_Admin_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_RepForm_Register_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.TrodelvyPatient_mTNBC_TC003" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.TrodelvyPatient_HR_MTNBC_HomePage_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.TrodelvyPatient_HRPlus_MTNBC_SavingSupport_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Trodelvyhcp_NursesStation_TC003" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Trodelvyhcp_HomePage_HrPlusPage_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Trodelvyhcp_HomePage_MtnBC_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Gileadadvancingaccess_PatientPharmacyfinder_TC005" />-->
			<!--<class
				name="com.gilead.testscripts.Smoke_Testcases.Gileadadvancingaccess_Patient_FAQs_TC006" />-->
			<!--<class
				name="com.gilead.testscripts.Smoke_Testcases.Gileadadvancingaccess_Patient_CopayCouponpage_TC004" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Gileadadvancingaccess_HCP_FAQs_TC003" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Gileadadvancingaccess_HCP_PharmacyFinder_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Gileadadvancingaccess_HCP_CopayCoupon_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.GileadOncologySupport_HCP_Form_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.GileadOncologySupport_HCP_Main_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.GileadOncologySupport_Patient_Main_TC003" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.P2PHub_Descovy_Webinar_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.P2PHub_Biktarvy_Webinar_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Mysupportpath_HCP_Main_page_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Mysupportpath_Patient_Main_page_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_Out_Patient_Studies_TC005" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_InPatient_Studies_TC004" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_Stay_Informed_Signup_Form_TC003" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_Dosing_Admin_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vekluryhcp_RepForm_Register_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vemlidy_Home_Savings_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vemlidy_Saving_Support_Copay_Coupon_Form_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vemlidy_Hepb_Take_Action_Why_TC003" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.Vemlidy_Pdf_download_TC004" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_TC001" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_TC002" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_TC003" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_TC004" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_TC005" />
			<class
				name="com.gilead.testscripts.Smoke_Testcases.KiteKonnect_TC006" />-->

		</classes>

	</test>
</suite>
