<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="24" name="Test under EntireSuite_Suite1" tests="18" failures="0" timestamp="2025-04-02T12:56:35 IST" time="6.483" errors="0">
  <testcase name="@BeforeClass beforeClass" time="3.174" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578792039E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578792082E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="@BeforeClass beforeClass" time="0.393" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578792505E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578792514E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="@BeforeClass beforeClass" time="0.234" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578792769E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578792775E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="@BeforeClass beforeClass" time="0.511" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.7435787933E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578793305E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="@BeforeClass beforeClass" time="0.224" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578793535E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578793538E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="@BeforeClass beforeClass" time="0.81" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578794366E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578794432E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="@BeforeClass beforeClass" time="0.151" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578794793E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.002" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578794814E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="@BeforeClass beforeClass" time="0.18" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578795033E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.74357879505E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 17 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.001" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="@BeforeClass beforeClass" time="0.105" classname="com.gilead.base.BaseTest">
    <failure type="com.gilead.config.FrameworkException" message="Configuration error">
      <![CDATA[com.gilead.config.FrameworkException: Configuration error
at com.gilead.base.BaseTest.beforeClass(BaseTest.java:185)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @BeforeClass beforeClass -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578795194E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 16 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@BeforeMethod beforeMethod" time="-1.743578795212E9" classname="com.gilead.base.BaseTest">
    <skipped/>
  </testcase> <!-- @BeforeMethod beforeMethod -->
  <testcase name="@AfterMethod afterMethod" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterMethod(BaseTest.java:230)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 16 stack frames]]>
    </failure>
  </testcase> <!-- @AfterMethod afterMethod -->
  <testcase name="@AfterClass afterClass" time="0.0" classname="com.gilead.base.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.gilead.base.BaseTest.afterClass(BaseTest.java:372)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 10 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterClass -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="navigationVerification" time="0.0" classname="com.gilead.testscripts.YESCARTA.Comprehensive_Component_and_Navigation_Verification_for_YesCARTA_Website">
    <skipped/>
  </testcase> <!-- navigationVerification -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="verifyNavigationHeaders" time="0.0" classname="com.gilead.testscripts.YESCARTA.Navigation_and_Functionality_Verification_for_YesCARTA_Website">
    <skipped/>
  </testcase> <!-- verifyNavigationHeaders -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="verifyYescartaSite" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_yescarta_at_a_glance_site">
    <skipped/>
  </testcase> <!-- verifyYescartaSite -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="verifySupportAndResourcesSite" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_support_and_resources_site">
    <skipped/>
  </testcase> <!-- verifySupportAndResourcesSite -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="verifyReceivingYescarta" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_receiving_yescarta_site">
    <skipped/>
  </testcase> <!-- verifyReceivingYescarta -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="verifyManagingSideEffectsSite" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_managing_side_effects_site">
    <skipped/>
  </testcase> <!-- verifyManagingSideEffectsSite -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="verifyClinicalTrialResults" time="0.0" classname="com.gilead.testscripts.YESCARTA.To_Verify_clinical_trial_results_site">
    <skipped/>
  </testcase> <!-- verifyClinicalTrialResults -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="PIandNavigationVerification" time="0.0" classname="com.gilead.testscripts.YESCARTA.PI_Integration_and_Navigation_Verification_for_YesCARTA_Website">
    <skipped/>
  </testcase> <!-- PIandNavigationVerification -->
  <testcase name="invokeURL" time="0.0" classname="com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website">
    <skipped/>
  </testcase> <!-- invokeURL -->
  <testcase name="PDFverification" time="0.0" classname="com.gilead.testscripts.YESCARTA.PDF_Download_Verification_for_YesCARTA_Website">
    <skipped/>
  </testcase> <!-- PDFverification -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
