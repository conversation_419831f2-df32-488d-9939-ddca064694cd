<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>FunctionalRegression_Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:26</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:49</td>   <td>82877</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:40</td>   <td>133455</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread6@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:18</td>   <td>51889</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:15</td>   <td>108796</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:50</td>   <td>23822</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:47</td>   <td>21001</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:12</td>   <td>105563</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:17</td>   <td>50587</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:12</td>   <td>105563</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:39</td>   <td>132458</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:48</td>   <td>81853</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:39</td>   <td>132458</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread6@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:23</td>   <td>-2900</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:47</td>   <td>21001</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread2@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:48</td>   <td>81853</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:23</td>   <td>-2900</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:17</td>   <td>50587</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterMethod(org.testng.ITestResult,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td>  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:40</td>   <td>133526</td> <td title="&lt;&lt;BaseTest.afterSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&lt;&lt;afterSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:50</td>   <td>23826</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:18</td>   <td>51892</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:26</td>   <td>18</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:49</td>   <td>82882</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:15</td>   <td>108802</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:32:51</td>   <td>-35300</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:04</td>   <td>97391</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:33</td>   <td>66498</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:14</td>   <td>-12438</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:25</td>   <td>118756</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:33</td>   <td>66498</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:14</td>   <td>-12438</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:41</td>   <td>14951</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:33:41</td>   <td>14951</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread2@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:01</td>   <td>34883</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:04</td>   <td>97391</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:34:01</td>   <td>34883</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="f7b9d7">  <td>25/05/21 13:35:25</td>   <td>118756</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeMethod(org.testng.ITestContext,java.lang.reflect.Method)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>Thread6@**********</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/05/21 13:34:27</td>   <td>61036</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_HCP_PharmacyFinder_TC002.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">invokeURL</td> 
  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/05/21 13:33:07</td>   <td>-19343</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_PatientPharmacyfinder_TC005.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">invokeURL</td> 
  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/05/21 13:34:59</td>   <td>92533</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_HCP_FAQs_TC003.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">invokeURL</td> 
  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/05/21 13:35:20</td>   <td>113526</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_Patient_CopayCouponpage_TC004.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">invokeURL</td> 
  <td>Thread6@**********</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/05/21 13:33:36</td>   <td>9732</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_Patient_FAQs_TC006.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">invokeURL</td> 
  <td>Thread2@**********</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/05/21 13:33:56</td>   <td>29206</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_HCP_CopayCoupon_TC001.invokeURL()[pri:1, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">invokeURL</td> 
  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/05/21 13:32:51</td>   <td>-35300</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-test=Test under EntireSuite_Suite1-1@**********</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/05/21 13:34:49</td>   <td>82881</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/05/21 13:33:50</td>   <td>23826</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread2@**********</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/05/21 13:33:26</td>   <td>18</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/05/21 13:35:15</td>   <td>108802</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/05/21 13:34:18</td>   <td>51892</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;CRAFTLiteTestCase.setUpTestRunner(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">&gt;&gt;setUpTestRunner</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/05/21 13:32:51</td>   <td>-35642</td> <td title="&gt;&gt;CRAFTLiteTestCase.setUpTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&gt;&gt;setUpTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@**********</td>   <td></td> </tr>
<tr bgcolor="bd819e">  <td>25/05/21 13:35:40</td>   <td>134078</td> <td title="&lt;&lt;CRAFTLiteTestCase.tearDownTestSuite(org.testng.ITestContext)[pri:0, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">&lt;&lt;tearDownTestSuite</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>main@**********</td>   <td></td> </tr>
<tr bgcolor="ad7f7f">  <td>25/05/21 13:34:33</td>   <td>66498</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_HCP_PharmacyFinder_TC002.verifyCriticalComponents()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002@cb0755b]">verifyCriticalComponents</td> 
  <td>Thread4@**********</td>   <td></td> </tr>
<tr bgcolor="7cc5c9">  <td>25/05/21 13:35:04</td>   <td>97391</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_HCP_FAQs_TC003.verifyCriticalComponents()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003@548d708a]">verifyCriticalComponents</td> 
  <td>Thread5@**********</td>   <td></td> </tr>
<tr bgcolor="758f8a">  <td>25/05/21 13:33:14</td>   <td>-12438</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_PatientPharmacyfinder_TC005.verifyCriticalComponents()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005@1ca3b418]">verifyCriticalComponents</td> 
  <td>Thread1@**********</td>   <td></td> </tr>
<tr bgcolor="7aab78">  <td>25/05/21 13:34:01</td>   <td>34883</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_HCP_CopayCoupon_TC001.verifyCriticalComponents()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001@7bba5817]">verifyCriticalComponents</td> 
  <td>Thread3@**********</td>   <td></td> </tr>
<tr bgcolor="86f7b2">  <td>25/05/21 13:35:25</td>   <td>118756</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_Patient_CopayCouponpage_TC004.verifyCriticalComponents()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004@5149d738]">verifyCriticalComponents</td> 
  <td>Thread6@**********</td>   <td></td> </tr>
<tr bgcolor="726b71">  <td>25/05/21 13:33:41</td>   <td>14951</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="Gileadadvancingaccess_Patient_FAQs_TC006.verifyCriticalComponents()[pri:2, instance:com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006@19e4653c]">verifyCriticalComponents</td> 
  <td>Thread2@**********</td>   <td></td> </tr>
</table>
