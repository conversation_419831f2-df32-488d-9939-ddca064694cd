<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="24" name="Test under EntireSuite_Suite1" tests="18" failures="1" timestamp="2025-04-22T11:57:42 IST" time="730.883" errors="0">
  <testcase name="invokeURL" time="10.97" classname="com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site"/>
  <testcase name="invokeURL" time="40.426" classname="com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site"/>
  <testcase name="invokeURL" time="40.905" classname="com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website"/>
  <testcase name="verifyYescartaSite" time="66.624" classname="com.gilead.testscripts.Tecartus.To_Verify_tecartus_at_a_glance_site"/>
  <testcase name="verifyReceivingYescarta" time="79.355" classname="com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site">
    <failure type="java.lang.RuntimeException" message="Error handling video playback for &amp;apos;What&amp;apos;s the CAR T treatment process like? from Gilead Sciences on Vimeo&amp;apos; on page &amp;apos;Tecartus&amp;apos;: javascript error: Cannot read properties of null (reading &amp;apos;pause&amp;apos;)
  (Session info: chrome=131.0.6778.265)
Build info: version: &amp;apos;4.1.2&amp;apos;, revision: &amp;apos;9a5a329c5a&amp;apos;
System info: host: &amp;apos;Z1VD7STHPRDN255&amp;apos;, ip: &amp;apos;************&amp;apos;, os.name: &amp;apos;Windows 10&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;1.8.0_291&amp;apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector(&amp;apos;video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video&amp;apos;).pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad">
      <![CDATA[java.lang.RuntimeException: Error handling video playback for 'What's the CAR T treatment process like? from Gilead Sciences on Vimeo' on page 'Tecartus': javascript error: Cannot read properties of null (reading 'pause')
  (Session info: chrome=131.0.6778.265)
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector('video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video').pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad
at businesscomponents.CommonFunctions.handleVideoPlayAndClose(CommonFunctions.java:4038)
at businesscomponents.CommonFunctions.verifyReceivingSite(CommonFunctions.java:4416)
at com.gilead.testscripts.Tecartus.To_Verify_receiving_tecartus_site.verifyReceivingYescarta(To_Verify_receiving_tecartus_site.java:28)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
Caused by: org.openqa.selenium.JavascriptException: javascript error: Cannot read properties of null (reading 'pause')
  (Session info: chrome=131.0.6778.265)
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [b17241bb9b5bd90db7bdcbe2df800fad, executeScript {script=document.querySelector('video.w-100.active,video.gl-video-embeded,video.video-stream,div.video-window video,div.video video,video,div.vp-telecine video,div.video-player video,div.vp-video video').pause();, args=[]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 131.0.6778.265, chrome: {chromedriverVersion: 131.0.6778.264 (2d05e315153..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:59741}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:59741/devtoo..., se:cdpVersion: 131.0.6778.265, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: b17241bb9b5bd90db7bdcbe2df800fad
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.RemoteWebDriver.executeScript(RemoteWebDriver.java:492)
at businesscomponents.CommonFunctions.videoPlay(CommonFunctions.java:1077)
at businesscomponents.CommonFunctions.handleVideoPlayAndClose(CommonFunctions.java:4034)
... 17 more
... Removed 16 stack frames]]>
    </failure>
  </testcase> <!-- verifyReceivingYescarta -->
  <testcase name="invokeURL" time="4.535" classname="com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site"/>
  <testcase name="verifyClinicalTrialResults" time="15.783" classname="com.gilead.testscripts.Tecartus.To_Verify_tecartus_clinical_trial_results_site"/>
  <testcase name="invokeURL" time="8.343" classname="com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website"/>
  <testcase name="PDFverification" time="15.124" classname="com.gilead.testscripts.Tecartus.PDF_Download_Verification_for_tecartus_Website"/>
  <testcase name="invokeURL" time="7.582" classname="com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website"/>
  <testcase name="invokeURL" time="8.493" classname="com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website"/>
  <testcase name="PIandNavigationVerification" time="110.769" classname="com.gilead.testscripts.Tecartus.PI_Integration_and_Navigation_Verification_for_tecartus_Website"/>
  <testcase name="invokeURL" time="9.829" classname="com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site"/>
  <testcase name="verifyManagingSideEffectsSite" time="150.132" classname="com.gilead.testscripts.Tecartus.To_Verify_tecartus_managing_side_effects_site"/>
  <testcase name="invokeURL" time="5.793" classname="com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site"/>
  <testcase name="navigationVerification" time="308.482" classname="com.gilead.testscripts.Tecartus.Comprehensive_Component_and_Navigation_Verification_for_tecartus_Website"/>
  <testcase name="verifySupportAndResourcesSite" time="181.033" classname="com.gilead.testscripts.Tecartus.To_Verify_tecartus_support_and_resources_site"/>
  <testcase name="verifyNavigationHeaders" time="670.612" classname="com.gilead.testscripts.Tecartus.Navigation_and_Functionality_Verification_for_Tecartus_Website"/>
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
