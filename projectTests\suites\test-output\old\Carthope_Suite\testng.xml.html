<html><head><title>testng.xml for Carthope_Suite</title></head><body><tt>&lt;?xml&nbsp;version="1.0"&nbsp;encoding="UTF-8"?&gt;
<br/>&lt;!DOCTYPE&nbsp;suite&nbsp;SYSTEM&nbsp;"https://testng.org/testng-1.0.dtd"&gt;
<br/>&lt;suite&nbsp;guice-stage="DEVELOPMENT"&nbsp;name="Carthope_Suite"&gt;
<br/>&nbsp;&nbsp;&lt;test&nbsp;thread-count="3"&nbsp;name="Test&nbsp;under&nbsp;EntireSuite_Suite1"&nbsp;parallel="classes"&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;parameter&nbsp;name="RunID"&nbsp;value="0"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;classes&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.Carthope.Navigation_And_Funtionality_Verification_For_Carthope_Website"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.Carthope.Comprehensive_Component_and_Navigation_Verification_for_Carthope_Website"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.Carthope.PDF_Download_Verification_for_Carthope_Website"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.Carthope.To_Verify_Car_T_Consultation_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.Carthope.To_Verify_Carthope_Safety_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;class&nbsp;name="com.gilead.testscripts.Carthope.To_Verify_Patient_Eligibility_Site"/&gt;
<br/>&nbsp;&nbsp;&nbsp;&nbsp;&lt;/classes&gt;
<br/>&nbsp;&nbsp;&lt;/test&gt;&nbsp;&lt;!--&nbsp;Test&nbsp;under&nbsp;EntireSuite_Suite1&nbsp;--&gt;
<br/>&lt;/suite&gt;&nbsp;&lt;!--&nbsp;Carthope_Suite&nbsp;--&gt;
<br/></tt></body></html>