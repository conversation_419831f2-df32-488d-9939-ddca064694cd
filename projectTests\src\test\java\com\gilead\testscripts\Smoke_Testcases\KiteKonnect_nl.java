package com.gilead.testscripts.Smoke_Testcases;

import org.testng.annotations.Test;

import com.gilead.base.BaseTest;

import businesscomponents.CommonFunctions;


public class KiteKonnect_nl extends BaseTest {

	CommonFunctions objCommonFunctions;


	@Test(priority = 1)
	public void invokeURL() throws Exception {
		try {
			objCommonFunctions = new CommonFunctions(scriptHelper);
			objCommonFunctions.setDriverScript(driverScript);
			objCommonFunctions.launchApplication();
		} finally {
			checkErrors();
		}
	}

	@Test(priority = 2)
	public void verifyCriticalComponents() throws Exception {
		try {
			objCommonFunctions.verifyLinksInWebPage();
			
		} finally {
			checkErrors();
		}
	}
}
