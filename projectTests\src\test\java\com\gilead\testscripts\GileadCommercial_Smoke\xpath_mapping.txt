XPATH TO LABEL MAPPING DOCUMENTATION
====================================
Date: 2025-06-17
Project: GileadCommercial_Smoke Test Case Consolidation

ANALYSIS SUMMARY:
================
Based on analysis of source test files and existing consolidated files, the following mapping strategy is identified:

REFERENCE STRUCTURE (HealthySexuals.java):
==========================================
- invokeURL() - Standard URL invocation (priority 1)
- checkLinksInWebPage() - calls verifyLinksInWebPageLoc()
- validatePopup() - calls verifyConfirmationPopupLoc()
- invokeSecondURL() - calls launchApplication()
- checkComponentPresents() - calls validateComponentExists()

CONSOLIDATED FILE ANALYSIS:
===========================

GileadHIV (gileadhiv.java) - EXISTING METHODS:
- invokeURL() (priority 1)
- verifyAboutUsPage() (priority 2) - calls clickSubMenuLoc()
- verifyEventsPage() (priority 3) - calls clickSubMenuLoc()
- verifyHIVTestingPage() (priority 4) - calls clickSubMenuLoc()
- verifyHomePage() (priority 5) - calls verifyLinksInWebPageLoc()
- verifyHivCareContinuumPage() (priority 6) - calls clickSubMenuLoc()

SOURCE FILES METHODS MAPPING:
- Validate_GileadHIV_Home_page: invokeURL(), toVerifyMenuLinks() -> verifyHomePage()
- Validate_GileadHIV_About_US: invokeURL(), toVerifyYescartaREMSSitePDF() -> verifyAboutUsPage()
- Validate_GileadHIV_Events: invokeURL(), clickSubMenu() -> verifyEventsPage()
- Validate_GileadHIV_HIV_Testing: invokeURL(), clickSubMenu() -> verifyHIVTestingPage()
- Validate_GileadHIV_Hiv_Care_Continuum: invokeURL(), clickSubMenu() -> verifyHivCareContinuumPage()

P2PTalkPrep (p2ptalkprep.java) - NEEDS UPDATE:
SOURCE: Validate_P2P_Talk_Prep_Peer_insights
- invokeURL() (priority 1)
- logOnPeerInsights() (priority 2) - calls clickSubMenu()
- verifyPeerInsightsPage() (priority 3) - calls validateComponentExists()
- verifyRegisterLink() (priority 4) - calls verifyLinksInWebPage()

TalkPrep (talkprep.java) - NEEDS UPDATE:
SOURCE FILES:
- Validate_TalkPrep_Home_Page: invokeURL(), verifyPeerInsightsPage(), verifyRegisterLink(), verifyPDFDownload()
- Validate_TalkPrep_About_Prep_page: invokeURL(), clickSubMenu()
- Validate_TalkPrep_Prescribing_PreP: invokeURL(), verifyLinksInWebPage()
- Validate_TalkPrep_Resources_page: invokeURL(), clickSubMenu()
- Validate_TalkPrep_Stay_Informed_Page: invokeURL(), validateComponentExists(), verifyLinksInWebPage()
- Validate_TalkPrep_Who_is_prep_for_page: invokeURL(), verifyLinksInWebPage()

UKHCV (ukhcv.java) - NEEDS UPDATE:
SOURCE FILES:
- Validate_UKHCV_HomePage: invokeURL(), verifyLinksInWebPage()
- Validate_UKHCV_Gilead_And_Elimination: invokeURL(), clickSubMenu(), validateComponentExists()
- Validate_UKHCV_Be_Free_of_Hep_C: invokeURL(), clickSubMenu(), validateComponentExists()
- Validate_UKHCV_Elimination_Partner: invokeURL(), clickSubMenu(), verifyLinksInWebPage(), clickSubMenu()
- Validate_UKHCV_Elimination_Resources: invokeURL(), validateComponentExists()
- Validate_UKHCV_HepC_Ki: invokeURL(), clickSubMenu(), validateComponentExists()
- Validate_UKHCV_What_Is_Hep_C: invokeURL(), clickSubMenu(), validateComponentExists()

LABEL NAME MAPPING STRATEGY:
============================
Based on HealthySexuals.java reference and Common.java patterns:

1. Menu Navigation Labels:
   - "About Us" -> AboutUs
   - "Events" -> Events
   - "HIV Testing" -> HIVTesting
   - "HIV Care Continuum" -> HIVCareContinuum
   - "Peer Insights" -> PeerInsights
   - "About Prep" -> AboutPrep
   - "Prescribing PreP" -> PrescribingPrep
   - "Resources" -> Resources
   - "Stay Informed" -> StayInformed
   - "Who is prep for" -> WhoIsPrepFor
   - "Gilead And Elimination" -> GileadAndElimination
   - "Be Free of Hep C" -> BeFreeOfHepC
   - "Elimination Partners" -> EliminationPartners
   - "What Is Hep C" -> WhatIsHepC
   - "HepC Ki" -> HepCKi

2. Component Verification Labels:
   - Use "VerifyComponentPresent" as standard label for component existence checks
   - Specific components will be mapped in Excel data

3. Link Verification Labels:
   - Use existing patterns from Common.java for link verification
   - PDF download links use standard checkPdfFileDownload() method

COMMON.JAVA XPATH PATTERNS:
===========================
Existing patterns that support label-based XPath construction:
- subMenu = By.xpath("//a[@class='dropdown-item ']//*[contains(text(),'" + strLabel + "')]")
- menu = By.xpath("//a[contains(@id,'navbarDropdownMenuLink')]//*[contains(text(),'" + strLabel + "')]")
- button = By.xpath("(//button[contains(text(),'" + strLabel + "')])|(//a[contains(text(),'" + strLabel + "')])...")
- verifyLinks = By.xpath("(//li[@class='menu-level-1']//*[contains(text(),'" + strLabel + "')])...")
- componetentExists = By.xpath("(//div[contains(@class,'row justify-content-center')]//*[contains(text(),'" + strLabel + "')])...")

EXCEL UPDATE REQUIREMENTS:
==========================
1. Automated Scripts_SmokeTestcases.xlsx:
   - Replace XPath values with label names for test cases
   - Update TC_ID column to match consolidated class names
   - Ensure SubMenuLocators column contains label names instead of XPaths

2. Run Manager.xlsx:
   - Update TestCase column to reference consolidated class names:
     * gileadhiv instead of individual GileadHIV test files
     * p2ptalkprep instead of individual P2PTalkPrep test files
     * talkprep instead of individual TalkPrep test files
     * ukhcv instead of individual UKHCV test files

CONSOLIDATION STATUS:
====================
- GileadHIV: Already consolidated, matches source functionality
- P2PTalkPrep: Needs update to match source file
- TalkPrep: Needs update to include all source file methods
- UKHCV: Needs update to include all source file methods

NEXT ACTIONS:
=============
1. Update consolidated files to include all source file methods
2. Generate Excel update scripts
3. Validate xpath handling in Common.java
4. Test consolidated functionality
