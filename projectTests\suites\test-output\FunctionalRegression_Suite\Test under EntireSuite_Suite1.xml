<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="z1vd7sthprdn255" ignored="15" name="Test under EntireSuite_Suite1" tests="12" failures="3" timestamp="2025-05-21T13:35:40 IST" time="168.8" errors="0">
  <testcase name="invokeURL" time="6.898" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005"/>
  <testcase name="verifyCriticalComponents" time="9.532" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_PatientPharmacyfinder_TC005"/>
  <testcase name="invokeURL" time="5.212" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006"/>
  <testcase name="verifyCriticalComponents" time="6.048" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_FAQs_TC006"/>
  <testcase name="invokeURL" time="5.674" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001"/>
  <testcase name="verifyCriticalComponents" time="15.703" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_CopayCoupon_TC001"/>
  <testcase name="invokeURL" time="5.431" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002">
    <failure type="com.gilead.config.FrameworkException" message="The specified sheet &amp;quot;SmokeCICD&amp;quot;does not exist within the workbook &amp;quot;Thread4FunctionalRegression_Gileadadvancingaccess_HCP_PharmacyFinder_TC002_Instance1.xlsx&amp;quot;">
      <![CDATA[com.gilead.config.FrameworkException: The specified sheet "SmokeCICD"does not exist within the workbook "Thread4FunctionalRegression_Gileadadvancingaccess_HCP_PharmacyFinder_TC002_Instance1.xlsx"
at com.gilead.maintenance.ExcelDataAccess.getWorkSheet(ExcelDataAccess.java:139)
at com.gilead.maintenance.ExcelDataAccess.getRowNum(ExcelDataAccess.java:163)
at com.gilead.maintenance.CraftDataTable.getData(CraftDataTable.java:120)
at businesscomponents.CommonFunctions.preCondition(CommonFunctions.java:785)
at businesscomponents.CommonFunctions.launchApplication(CommonFunctions.java:1117)
at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002.invokeURL(Gileadadvancingaccess_HCP_PharmacyFinder_TC002.java:20)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames]]>
    </failure>
  </testcase> <!-- invokeURL -->
  <testcase name="verifyCriticalComponents" time="15.341" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_PharmacyFinder_TC002"/>
  <testcase name="invokeURL" time="4.829" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003">
    <failure type="com.gilead.config.FrameworkException" message="The specified sheet &amp;quot;SmokeCICD&amp;quot;does not exist within the workbook &amp;quot;Thread5FunctionalRegression_Gileadadvancingaccess_HCP_FAQs_TC003_Instance1.xlsx&amp;quot;">
      <![CDATA[com.gilead.config.FrameworkException: The specified sheet "SmokeCICD"does not exist within the workbook "Thread5FunctionalRegression_Gileadadvancingaccess_HCP_FAQs_TC003_Instance1.xlsx"
at com.gilead.maintenance.ExcelDataAccess.getWorkSheet(ExcelDataAccess.java:139)
at com.gilead.maintenance.ExcelDataAccess.getRowNum(ExcelDataAccess.java:163)
at com.gilead.maintenance.CraftDataTable.getData(CraftDataTable.java:120)
at businesscomponents.CommonFunctions.preCondition(CommonFunctions.java:785)
at businesscomponents.CommonFunctions.launchApplication(CommonFunctions.java:1117)
at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003.invokeURL(Gileadadvancingaccess_HCP_FAQs_TC003.java:20)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 12 stack frames]]>
    </failure>
  </testcase> <!-- invokeURL -->
  <testcase name="verifyCriticalComponents" time="8.167" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_HCP_FAQs_TC003"/>
  <testcase name="invokeURL" time="5.227" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004"/>
  <testcase name="verifyCriticalComponents" time="13.697" classname="com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004">
    <failure type="org.openqa.selenium.NoSuchElementException" message="no such element: Unable to locate element: {&amp;quot;method&amp;quot;:&amp;quot;xpath&amp;quot;,&amp;quot;selector&amp;quot;:&amp;quot; //div[@class=&amp;apos;navbar-collapse collapse&amp;apos;]//a[contains(text(),&amp;apos;Activate&amp;apos;)]&amp;quot;}
  (Session info: chrome=133.0.6943.99)
For documentation on this error, please visit: https://selenium.dev/exceptions/#no_such_element
Build info: version: &amp;apos;4.1.2&amp;apos;, revision: &amp;apos;9a5a329c5a&amp;apos;
System info: host: &amp;apos;Z1VD7STHPRDN255&amp;apos;, ip: &amp;apos;************&amp;apos;, os.name: &amp;apos;Windows 10&amp;apos;, os.arch: &amp;apos;amd64&amp;apos;, os.version: &amp;apos;10.0&amp;apos;, java.version: &amp;apos;1.8.0_291&amp;apos;
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [fbcabfabcaf3acee2648df9c79f95c44, findElement {using=xpath, value= //div[@class=&amp;apos;navbar-collapse collapse&amp;apos;]//a[contains(text(),&amp;apos;Activate&amp;apos;)]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:54986}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:54986/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: fbcabfabcaf3acee2648df9c79f95c44">
      <![CDATA[org.openqa.selenium.NoSuchElementException: no such element: Unable to locate element: {"method":"xpath","selector":" //div[@class='navbar-collapse collapse']//a[contains(text(),'Activate')]"}
  (Session info: chrome=133.0.6943.99)
For documentation on this error, please visit: https://selenium.dev/exceptions/#no_such_element
Build info: version: '4.1.2', revision: '9a5a329c5a'
System info: host: 'Z1VD7STHPRDN255', ip: '************', os.name: 'Windows 10', os.arch: 'amd64', os.version: '10.0', java.version: '1.8.0_291'
Driver info: org.openqa.selenium.chrome.ChromeDriver
Command: [fbcabfabcaf3acee2648df9c79f95c44, findElement {using=xpath, value= //div[@class='navbar-collapse collapse']//a[contains(text(),'Activate')]}]
Capabilities {acceptInsecureCerts: false, browserName: chrome, browserVersion: 133.0.6943.99, chrome: {chromedriverVersion: 133.0.6943.141 (2a5d6da0d61..., userDataDir: C:\Users\<USER>\AppData\L...}, fedcm:accounts: true, goog:chromeOptions: {debuggerAddress: localhost:54986}, javascriptEnabled: true, networkConnectionEnabled: false, pageLoadStrategy: normal, platform: WINDOWS, platformName: WINDOWS, proxy: Proxy(), se:cdp: ws://localhost:54986/devtoo..., se:cdpVersion: 133.0.6943.99, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}
Session ID: fbcabfabcaf3acee2648df9c79f95c44
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)
at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:53)
at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:184)
at org.openqa.selenium.remote.service.DriverCommandExecutor.invokeExecute(DriverCommandExecutor.java:167)
at org.openqa.selenium.remote.service.DriverCommandExecutor.execute(DriverCommandExecutor.java:142)
at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:558)
at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElement(ElementLocation.java:162)
at org.openqa.selenium.remote.ElementLocation.findElement(ElementLocation.java:60)
at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:382)
at org.openqa.selenium.remote.RemoteWebDriver.findElement(RemoteWebDriver.java:374)
at com.gilead.reports.CraftDriver.findElement(CraftDriver.java:164)
at businesscomponents.CommonFunctions.verifyLinksInWebPage(CommonFunctions.java:1137)
at com.gilead.testscripts.FunctionalRegression.Gileadadvancingaccess_Patient_CopayCouponpage_TC004.verifyCriticalComponents(Gileadadvancingaccess_Patient_CopayCouponpage_TC004.java:29)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)
... Removed 16 stack frames]]>
    </failure>
  </testcase> <!-- verifyCriticalComponents -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite afterSuite" time="0.0" classname="com.gilead.base.BaseTest">
    <ignored/>
  </testcase> <!-- @AfterSuite afterSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@AfterSuite tearDownTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @AfterSuite tearDownTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
  <testcase name="@BeforeSuite setUpTestSuite" time="0.0" classname="com.gilead.maintenance.CRAFTLiteTestCase">
    <ignored/>
  </testcase> <!-- @BeforeSuite setUpTestSuite -->
</testsuite> <!-- Test under EntireSuite_Suite1 -->
