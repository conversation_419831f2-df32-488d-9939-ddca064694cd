package smoketest;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.testng.xml.XmlClass;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlTest;

public class DynamicTestNGRunner {

	public static Properties property = new Properties();

	/**
	 * main method to create DynamicTestNG file for smoke Test execution
	 * 
	 */
	public static void main(String[] args) throws Exception {
		// Load the Properties for Dynamic XML Generation
		loadProperties();
		File currentDirectory = new File(System.getProperty("user.dir"));
		String excelFilePath = currentDirectory + File.separator + property.getProperty("Excel_Configuration")
				+ ".xlsx";
		String basePath = currentDirectory + File.separator + "src/test/java/" + property.getProperty("Package_Name").replace('.', '/');
		List<String> classFilesFromExcel = readTestNGFilesFromExcel(excelFilePath);
		if (!classFilesFromExcel.isEmpty()) {
			createDynamicSuite(classFilesFromExcel, basePath);
		} else {
			System.out.println("No Websites are configured for execution");
		}
	}

	/**
	 * Function to get the list of suit names to be executed
	 * 
	 * @param filePath The path of the excel file which has the configuration
	 * 
	 * @return List<String> List of SuiteNames to be executed
	 */
	public static List<String> readTestNGFilesFromExcel(String filePath) throws Exception {
		List<String> xmlFiles = new ArrayList<>();
		try {
			FileInputStream fis = new FileInputStream(new File(filePath));
			Workbook workbook = new XSSFWorkbook(fis);
			try {
				Sheet sheet = workbook.getSheet("SmokeTest");
				if (sheet == null) {
					throw new IllegalArgumentException("Unable to find the SmokeTest Sheet in the workbook");
				}

				// Get the Index of the Execution Flag Column
				Row headerRow = sheet.getRow(0);
				int executionFlagColumnNumber = -1;
				for (Cell cell : headerRow) {
					if (cell.getStringCellValue().equalsIgnoreCase("Execution Flag")) {
						executionFlagColumnNumber = cell.getColumnIndex();
						break;
					}
				}

				// Check If the Execution Flag column available or not
				if (executionFlagColumnNumber == -1) {
					throw new IllegalArgumentException("Execution Flag Header is not available in the Config sheet");
				}

				// Get the Index of the WebsiteName Column
				int webSiteNameColumnNumber = -1;
				for (Cell cell : headerRow) {
					if (cell.getStringCellValue().equalsIgnoreCase("Website Name")) {
						webSiteNameColumnNumber = cell.getColumnIndex();
						break;
					}
				}

				// Check If the WebsiteName Column available or not
				if (webSiteNameColumnNumber == -1) {
					throw new IllegalArgumentException("WebsiteName Header is not available in the Config sheet");
				}

				// Fetch the WebsiteNames which marked as 'Yes' in Execution Flag and add into the
				// list
				for (int i = 1; i <= sheet.getLastRowNum(); i++) {
					Row row = sheet.getRow(i);
					if (row != null) {
						Cell executionFlagCell = row.getCell(executionFlagColumnNumber);
						if (executionFlagCell != null) {
							String cellValueAsString = getCellValueAsString(executionFlagCell);
							if (cellValueAsString.equalsIgnoreCase("Yes")) {
								Cell webSiteNameCell = row.getCell(webSiteNameColumnNumber);
								if (webSiteNameCell != null) {
									xmlFiles.add(getCellValueAsString(webSiteNameCell).trim());
								}
							}
						}
					}
				}
			} finally {
				workbook.close();
				fis.close();
			}
		} catch (FileNotFoundException e) {
			throw new IllegalArgumentException("Config Excel Sheet is not available");
		}
		return xmlFiles;
	}

	/**
	 * Function to return the cell value as String
	 * 
	 * @param Cell The Cell Value
	 * 
	 * @return String Return the cell value as String
	 */
	private static String getCellValueAsString(Cell cell) {
		switch (cell.getCellTypeEnum()) {
		case STRING:
			return cell.getStringCellValue();
		case NUMERIC:
			return String.valueOf(cell.getNumericCellValue());

		// Handle other cell types if needed
		default:
			return "";
		}
	}

	/**
	 * Function to return the cell value as String
	 * 
	 * @param List<String> List of suitNames read from the excel configuration
	 * 
	 */
	public static void createDynamicSuite(List<String> webSiteNames, String basePath)throws IOException {
		try {
			Path checkExistingSuite = Paths.get(System.getProperty("user.dir") + File.separator + "suites/SmokeTestPOC.xml");
			Files.deleteIfExists(checkExistingSuite);
			List<XmlClass> xmlClasses = new ArrayList<>();
			for (String className : webSiteNames) {
				File classFile = new File(basePath + "/" + className + ".java");
				if (classFile.exists()) {
					xmlClasses.add(new XmlClass(property.getProperty("Package_Name") + "." + className,false));
				} else {
					System.out.println("Website %s not found " + className);
				}
			}
			// Create Suite
			XmlSuite suite = new XmlSuite();
			suite.setName("Surefire suite");

			XmlTest test = new XmlTest(suite);
			test.setName("Smoke Test");
			test.setXmlClasses(xmlClasses);
			test.setParallel(XmlSuite.ParallelMode.CLASSES);
			test.setThreadCount(Integer.parseInt(property.getProperty("Threads")));
			
			// Write to file
			try (FileWriter writer = new FileWriter(
					System.getProperty("user.dir") + File.separator + "suites/SmokeTestPOC.xml")) {
				writer.write(suite.toXml());
				System.out.println("Generated testng-dynamic.xml with " + xmlClasses.size() + " classes.");
			} catch (IOException e) {
				throw new IllegalArgumentException("TestNG XML file 'SmokeTest.xml' is failed to generate");
			}
		} catch (Exception e) {
			throw new IllegalArgumentException("TestNG XML file 'SmokeTest.xml' is failed to generate");
		}
	}

	/**
	 * This method is used to get load the global properties
	 */
	public static void loadProperties() throws IOException {
		InputStream globalProperty = smoketest.DynamicTestNGRunner.class.getClassLoader()
				.getResourceAsStream("DynamicXMLGeneration.properties");
		if (globalProperty != null) {
			property.load(globalProperty);
		} else {
			throw new IllegalArgumentException("Dynamic XML Generation Property File is Not Avaialble");
		}
	}
}
